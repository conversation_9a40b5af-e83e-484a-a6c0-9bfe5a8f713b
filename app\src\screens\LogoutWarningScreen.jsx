import React, { useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import { handleLogoutWarningScreen } from '../app/applicationConfigReducer'
import { Button, Dialog, DialogContent, Grid, IconButton, Typography } from '@mui/material'
import { Box, Stack } from '@mui/system'
import CloseIcon from "@mui/icons-material/Close";
import userLogout from "../../src/utilityImages/userLogout.jpg"
import { destination_Admin } from '../destinationVariables'
import { doAjax } from '../components/common/fetchService'
import { END_POINTS } from '@constant/apiEndPoints'
const LogoutWarningScreen = () => {
    let dispatch = useDispatch()
    const [count, setcount] = useState(10)
    const [intervalId, setIntervalId] = useState(null)
    let closeScreen = ()=>{
        clearInterval(intervalId)
        dispatch(handleLogoutWarningScreen(false))
    }
    
    let refreshScreen = () =>{
      getApirefresh()
      clearInterval(intervalId)
      dispatch(handleLogoutWarningScreen(false))
    }

    const getApirefresh = () => {
      const hSuccess=(data)=>{}
      const hError=()=>{}
      doAjax(`/${destination_Admin}${END_POINTS?.DUMMY_API}`,'get',hSuccess,hError)
    }
  
    useEffect(() => {
       let intervalId =  setInterval(()=>{
              setcount((prev)=> prev-1)
          },1000)
          setIntervalId(intervalId)
    }, [])
    useEffect(()=>{
        if(count === 0){
        clearInterval(intervalId)
          window.location.href = "/do/logout"
        }
    },[count])
    
  return (
    <Dialog
    hideBackdrop={false}
    elevation={2}
    PaperProps={{
      sx: { boxShadow: "none" },
    }}
    open={true}
    onClose={closeScreen}
  >
    <Grid
      container
      sx={{ position:'relative'}}
    >
      <Grid item sx={{ position: "absolute",top:'1rem',right:'1rem' }}>
        
          <IconButton
            onClick={(e) => {
              e.stopPropagation();
              closeScreen();
            }}
          >
            <CloseIcon />
          </IconButton>
    
      </Grid>
    </Grid>

    <DialogContent>
      <Stack>
        <Grid container sx={{display:'flex', justifyContent:'center'}}>
            <Grid item sx={{ opacity: ".5"}}>
            <img src={userLogout} style={{
                width:'16rem'
            }} >

            </img>
            </Grid>
          <Grid
            item
            md={12}
            sx={{
             
              textAlign: "center",  
            }}
          >
            <Typography>
          Due to inactivity you will be logged out in <b>{count}</b> Seconds.
            </Typography>
          </Grid>
          <Grid item sx={{display:'flex', justifyContent:'end',width:'100%'}}>
            <Button variant='contained' onClick={refreshScreen}>
                Refresh
            </Button>
          </Grid>
        </Grid>
      
      </Stack>
    </DialogContent>
  </Dialog>
  )
}

export default LogoutWarningScreen