import React, { useState } from 'react';
import StatsGrid from './StatsGrid';
import WorkflowGroupCard from './WorkflowGroupCard';
import TaskModal from './TaskModal';
import './workflow.css';

const WorkflowDashboard = ({ data }) => {
  const [modalData, setModalData] = useState(null);

  const handleShowModal = (taskInfo) => {
    setModalData(taskInfo);
  };

  const handleCloseModal = () => {
    setModalData(null);
  };

  return (
    <div className="container">
      <StatsGrid data={data} />
      <div className="workflow-container">
        {Object.entries(data).map(([groupName, groupData]) => {
          return (
            <WorkflowGroupCard
            key={groupName}
            groupName={groupName}
            groupData={groupData}
            onTaskClick={handleShowModal}
          />
          )
        }
        )}
      </div>
      {modalData && <TaskModal task={modalData} onClose={handleCloseModal} />}
    </div>
  );
};

export default WorkflowDashboard;
