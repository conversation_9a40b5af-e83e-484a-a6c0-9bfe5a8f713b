import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';
import { resetPayloadData, setChangeFieldRows, setChangeFieldRowsDisplay, setDynamicKeyValue, setMatlNoData, setPlantData, setWhseData, updateSelectedRows } from '../app/payloadslice';
import { updateSlNo, updatePage } from "../app/paginationSlice"
import { setDropDown } from "../app/dropDownDataSlice";
import { v4 as uuidv4 } from "uuid";
import { TEMPLATE_KEYS } from '@constant/changeTemplates';
import { useEffect } from 'react';
import { doAjax } from '@components/Common/fetchService';
import useLogger from './useLogger';
import { API_CODE, DIALOUGE_BOX_MESSAGES } from '@constant/enum';
import { END_POINTS } from '@constant/apiEndPoints';
import { convertToDateFormat } from '@helper/helper';
import { destination_MaterialMgmt } from '../../src/destinationVariables';

const useChangeMaterialRows = () => {
    const dispatch = useDispatch();
    const taskData = useSelector((state) => state.userManagement.taskData)
    const paginationData = useSelector((state) => state.paginationData);
    const changeFieldRows = useSelector((state) => state.payload.changeFieldRows)
    const changeFieldRowsDisplay = useSelector((state) => state.payload.changeFieldRowsDisplay)
    const dynamicData = useSelector((state) => state.payload.dynamicKeyValues)
    const selectedRowData = useSelector((state) => state.payload.selectedRows)
    const whseList = useSelector((state) => state.payload.whseList)
    const plantList = useSelector((state) => state.payload.plantList)
    const matNoList = useSelector((state) => state.payload.matNoList)
    const { customError } = useLogger()

    // NOTE: SlNo may be used later for pagination.
    
    // useEffect(() => {
    //   if (Array.isArray(changeFieldRows)) {
    //     if (changeFieldRows?.length > 0) {
    //       dispatch(updateSlNo(changeFieldRows?.length));
    //     }
    //   } else if (changeFieldRows && typeof changeFieldRows === "object") {
    //     const lengthObject = Object?.keys(changeFieldRows)?.reduce((acc, key) => {
    //       acc[key] = changeFieldRows[key]?.length || 0;
    //       return acc;
    //     }, {});
    
    //     dispatch(updateSlNo(lengthObject));
    //   }
    // }, [changeFieldRows, dispatch]);

    const fetchDisplayDataRows = (apiResponse) => {
        const result = 
        apiResponse[0]?.TemplateName === TEMPLATE_KEYS?.LOGISTIC ? processedLogisticData(apiResponse)
        : apiResponse[0]?.TemplateName === TEMPLATE_KEYS?.ITEM_CAT ? processedItemCatData(apiResponse)
        : apiResponse[0]?.TemplateName === TEMPLATE_KEYS?.MRP ? processedMRPData(apiResponse)
        : apiResponse[0]?.TemplateName === TEMPLATE_KEYS?.UPD_DESC ? processedUpdDescData(apiResponse)
        : apiResponse[0]?.TemplateName === TEMPLATE_KEYS?.WARE_VIEW_2 ? processedWarehouseData(apiResponse)
        : apiResponse[0]?.TemplateName === TEMPLATE_KEYS?.CHG_STAT ? processedChangeStatusData(apiResponse)
        : apiResponse[0]?.TemplateName === TEMPLATE_KEYS?.SET_DNU ? processedSetToDNUData(apiResponse)
        : []

        if (Array.isArray(result)) {
          dispatch(setChangeFieldRows([...changeFieldRows, ...result]));
          dispatch(setChangeFieldRowsDisplay({ ...changeFieldRowsDisplay, [paginationData?.page]: result }));
        } else if (typeof result === "object" && result !== null) {
          const updatedChangeFieldRows = { ...changeFieldRows };
        
          Object?.keys(result)?.forEach((key) => {
            updatedChangeFieldRows[key] = [
              ...(updatedChangeFieldRows[key] || []),
              ...result[key],
            ];
          });
        
          dispatch(setChangeFieldRows(updatedChangeFieldRows));
          dispatch(setChangeFieldRowsDisplay({ ...changeFieldRowsDisplay, [paginationData?.page]: result }));
        }

        // dispatch(setChangeFieldRows(result));
        let idsArray;
        if (Array.isArray(result)) {
          idsArray = result.map((row) => row?.id);
          dispatch(updateSelectedRows([...selectedRowData, ...idsArray]));
        } else if (typeof result === "object" && result !== null) {
          idsArray = Object.keys(result).reduce((acc, key) => {
            acc[key] = result[key]?.map((row) => row?.id) || [];
            return acc;
          }, {});
          const updatedSelectedRows = { ...selectedRowData };
        
          Object?.keys(idsArray)?.forEach((key) => {
            updatedSelectedRows[key] = [
              ...(updatedSelectedRows[key] || []),
              ...idsArray[key],
            ];
          });
        
          dispatch(updateSelectedRows(updatedSelectedRows));
        }

        dispatch(
            setDynamicKeyValue({
                keyName: "requestHeaderData",
                data: apiResponse[0]?.Torequestheaderdata
            })
        )
        dispatch(
            setDynamicKeyValue({
                keyName: "templateName",
                data: apiResponse[0]?.TemplateName
            })
        )
        const massErrorData = {}
        apiResponse?.forEach((object) => {
            massErrorData[object?.Material] = object?.Tomaterialerrordata
        })
        dispatch(
          setDynamicKeyValue({
              keyName: "errorData",
              data: {...(dynamicData?.errorData || {}), ...massErrorData} // Append new data to existing data
          })
        );
        const otherPayloadData = {}
        otherPayloadData["IntermediateTaskCount"] = apiResponse[0]?.IntermediateTaskCount
        otherPayloadData["TotalIntermediateTasks"] = apiResponse[0]?.TotalIntermediateTasks
        otherPayloadData["MassEditId"] = apiResponse[0]?.MassEditId
        otherPayloadData["Comments"] = apiResponse[0]?.Comments || ""
        otherPayloadData["TaskId"] = taskData?.taskId
        otherPayloadData["TaskName"] = taskData?.taskDesc
        otherPayloadData["CreationTime"] = taskData?.createdOn ? convertToDateFormat(taskData?.createdOn) : null,
        otherPayloadData["DueDate"] = taskData?.criticalDeadline ? convertToDateFormat(taskData?.criticalDeadline) : null
        dispatch(
            setDynamicKeyValue({
                keyName: "otherPayloadData",
                data: otherPayloadData
            })
        )

        const payloadData = {
            ReqCreatedBy: apiResponse[0]?.Torequestheaderdata?.ReqCreatedBy,
            RequestStatus: apiResponse[0]?.Torequestheaderdata?.RequestStatus,
            Region: apiResponse[0]?.Torequestheaderdata?.Region,
            ReqCreatedOn: new Date().toISOString(),
            ReqUpdatedOn: new Date().toISOString(),
            RequestType: apiResponse[0]?.Torequestheaderdata?.RequestType,
            RequestDesc: apiResponse[0]?.Torequestheaderdata?.RequestDesc,
            RequestPriority: apiResponse[0]?.Torequestheaderdata?.RequestPriority,
            LeadingCat: apiResponse[0]?.Torequestheaderdata?.LeadingCat,
            RequestId: apiResponse[0]?.Torequestheaderdata?.RequestId,
            TemplateName: apiResponse[0]?.Torequestheaderdata?.TemplateName,
            FieldName: apiResponse[0]?.Torequestheaderdata?.FieldName?.split("$^$"),
          }
          dispatch(resetPayloadData({ data: payloadData }));
    }

      const processedLogisticData = (data) => {
        const result = [];
        let index = 1;
        const matNoSet = new Set();
      
        data.forEach((parent) => {
          parent.Touomdata.forEach((child) => {
            matNoSet.add(child.Material);
            const transformedChild = {
              ...child,
              Material: parent?.Material,
              MaterialId: parent?.MaterialId,
              ClientId: parent?.Toclientdata?.ClientId,
              id: uuidv4(),
              slNo: index++,
              ChangeLogId: parent?.changeLogData?.ChangeLogId ?? null             
            };
            result.push(transformedChild);
          });
        });
      
        dispatch(setMatlNoData([...matNoList, ...matNoSet]))
        return result;
      }
      const processedItemCatData = (data) => {
        const result = [];
        let index = 1;
      
        data.forEach((parent) => {
          parent.Tosalesdata.forEach((child) => {
            const transformedChild = {
              ...child,
              Material: parent?.Material,
              MaterialId: parent?.MaterialId,
              ClientId: parent?.Toclientdata?.ClientId,
              id: uuidv4(),
              slNo: index++,
              ChangeLogId: parent?.changeLogData?.ChangeLogId ?? null
            };
            result.push(transformedChild);
          });
        });
      
        return result;
      }
      const processedMRPData = (data) => {
        const result = {
          "Basic Data": [],
          "Plant Data": [],
        };
        let clientDataSlNo = 1;
        let plantDataSlNo = 1;
        const plantSet = new Set();
      
        data.forEach((parent) => {
          const { Toplantdata, Toclientdata, Material, MaterialId } = parent;
      
          result["Basic Data"].push({
            ...Toclientdata,
            id: uuidv4(),
            slNo: clientDataSlNo++,
            type: "Basic Data",
            Material,
            MaterialId,
            Function: "UPD",
            ChangeLogId: parent?.changeLogData?.ChangeLogId ?? null
          });

          Toplantdata?.forEach((plantData) => {
            plantSet.add(plantData?.Plant);
            result["Plant Data"].push({
              ...plantData,
              id: uuidv4(),
              Material,
              slNo: plantDataSlNo++,
              type: "Plant Data",
              MaterialId,
              Function: "UPD",
              ChangeLogId: parent?.changeLogData?.ChangeLogId ?? null
            });
          });
        });
      
        dispatch(setPlantData([...plantList, ...plantSet]))
        return result;
      };
      const processedChangeStatusData = (data) => {
        const result = {
          "Basic Data": [],
          "Plant Data": [],
          "Sales Data": [],
        };
        let basicDataSlNo = 1;
        let plantDataSlNo = 1;
        let salesDataSlNo = 1;
      
        data.forEach((parent) => {
          const { Toplantdata, Toclientdata, Tosalesdata, Material, MaterialId } = parent;
          result["Basic Data"].push({
            ...Toclientdata,
            id: uuidv4(),
            slNo: basicDataSlNo++,
            type: "Basic Data",
            Material,
            MaterialId,
            Function: "UPD",
            ChangeLogId: parent?.changeLogData?.ChangeLogId ?? null
          });

          Toplantdata?.forEach((plantData) => {
            result["Plant Data"].push({
              ...plantData,
              id: uuidv4(),
              Material,
              slNo: plantDataSlNo++,
              type: "Plant Data",
              MaterialId,
              Function: "UPD",
              ChangeLogId: parent?.changeLogData?.ChangeLogId ?? null
            });
          });

          Tosalesdata?.forEach((salesData) => {
            result["Sales Data"].push({
              ...salesData,
              id: uuidv4(),
              Material,
              slNo: salesDataSlNo ++,
              type: "Sales Data",
              MaterialId,
              Function: "UPD",
              ChangeLogId: parent?.changeLogData?.ChangeLogId ?? null
            });
          });
        });
      
        return result;
      };
      const processedSetToDNUData = (data) => {
        const result = {
          "Basic Data": [],
          "Plant Data": [],
          "Sales Data": [],
          "Description": [],
        };
        let basicDataSlNo = 1;
        let plantDataSlNo = 1;
        let salesDataSlNo = 1;
        let descDataSlNo = 1;
      
        data.forEach((parent) => {
          const { Toplantdata, Toclientdata, Tosalesdata, Tomaterialdescription, Material, MaterialId } = parent;
      
          result["Basic Data"].push({
            ...Toclientdata,
            id: uuidv4(),
            slNo: basicDataSlNo++,
            type: "Basic Data",
            Material,
            MaterialId,
            Function: "UPD",
            ChangeLogId: parent?.changeLogData?.ChangeLogId ?? null
          });

          Toplantdata?.forEach((plantData) => {
            result["Plant Data"].push({
              ...plantData,
              id: uuidv4(),
              Material,
              slNo: plantDataSlNo++,
              type: "Plant Data",
              MaterialId,
              Function: "UPD",
              ChangeLogId: parent?.changeLogData?.ChangeLogId ?? null
            });
          });

          Tosalesdata?.forEach((salesData) => {
            result["Sales Data"].push({
              ...salesData,
              id: uuidv4(),
              Material,
              slNo: salesDataSlNo ++,
              type: "Sales Data",
              MaterialId,
              Function: "UPD",
              ChangeLogId: parent?.changeLogData?.ChangeLogId ?? null
            });
          });

          Tomaterialdescription?.forEach((descData) => {
            result["Description"].push({
              ...descData,
              id: uuidv4(),
              Material,
              slNo: descDataSlNo ++,
              type: "Description",
              MaterialId,
              Function: "UPD",
              ChangeLogId: parent?.changeLogData?.ChangeLogId ?? null
            });
          });
        });
      
        return result;
      };
      const processedUpdDescData = (data) => {
        const result = [];
        let index = 1;
        const matNoSet = new Set();

        data.forEach((parent) => {
          parent.Tomaterialdescription.forEach((child) => {
            matNoSet.add(child.Material);
            const transformedChild = {
              ...child,
              Material: parent?.Material,
              MaterialId: parent?.MaterialId,
              ClientId: parent?.Toclientdata?.ClientId,
              id: uuidv4(),
              slNo: index++,
              ChangeLogId: parent?.changeLogData?.ChangeLogId ?? null               
            };
            result.push(transformedChild);
          });
        });
      
        dispatch(setMatlNoData([...matNoList, ...matNoSet]))
        return result;
      }
      const processedWarehouseData = (data) => {
        const result = [];
        const whseNoSet = new Set();
        let index = 1;
      
        data.forEach((parent) => {
          parent.Towarehousedata.forEach((child) => {
            whseNoSet.add(child.WhseNo);
            const transformedChild = {
              ...child,
              Material: parent?.Material,
              MaterialId: parent?.MaterialId,
              ClientId: parent?.Toclientdata?.ClientId,
              id: uuidv4(),
              slNo: index++,
              ChangeLogId: parent?.changeLogData?.ChangeLogId ?? null               
            };
            result.push(transformedChild);
          });
        });
      
        const whseListArray = [...whseNoSet];
        dispatch(setWhseData(whseListArray))
        return result;
      }
      useEffect(() => {
        const fetchData = async () => {
          if (whseList?.length > 0) {
            const whseOptions = await fetchWarehouseOptions(whseList);
            dispatch(setDropDown({ keyName: "Unittype1", data: whseOptions }));
          }
        };
      
        fetchData();
      }, [whseList]);
      const fetchWarehouseOptions = async (whseList) => {
        const whseOptions = {};
      
        for (const whseNo of whseList) {
          let payload = { whseNo };
      
          try {
            const data = await new Promise((resolve) => {
              doAjax(`/${destination_MaterialMgmt}${END_POINTS?.DEPENDENT_LOOKUPS?.UNITTYPE}`,
              "post",
              (response) => {
                if (response.statusCode === API_CODE?.STATUS_200) {
                  resolve(response?.body);
                } else {
                  customError(DIALOUGE_BOX_MESSAGES?.ERROR_MSG);
                  resolve([]);
                }
              },
              (error) => {
                customError(error);
                resolve([]);
              }, 
              payload);
            });
      
            whseOptions[whseNo] = data;
          } catch (error) {
            customError(error);
            whseOptions[whseNo] = [];
          }
        }
      
        return whseOptions;
      };

      useEffect(() => {
        const fetchData = async () => {
          if (plantList?.length > 0) {
            const plantOptions = await fetchPlantOptions(plantList);
            dispatch(setDropDown({ keyName: "Spproctype", data: plantOptions }));
            const mrpCtrlerOptions = await fetchMrpCtrlerOptions(plantList);
            dispatch(setDropDown({ keyName: "MrpCtrler", data: mrpCtrlerOptions }));
          }
        };
      
        fetchData();
      }, [plantList]);
      
      const fetchPlantOptions = async (plantList) => {
        const plantOptions = {};
      
        for (const plant of plantList) {
          let payload = { plant };
      
          try {
            const data = await new Promise((resolve) => {
              doAjax(`/${destination_MaterialMgmt}${END_POINTS?.DATA?.GET_SPPROC_TYPE}`,
              "post",
              (response) => {
                if (response.statusCode === API_CODE?.STATUS_200) {
                  resolve(response?.body);
                } else {
                  customError("Failed to fetch data");
                  resolve([]);
                }
              },
              (error) => {
                customError(error);
                resolve([]);
              }, 
              payload);
            });
      
            plantOptions[plant] = data;
          } catch (error) {
            customError(error);
            plantOptions[plant] = [];
          }
        }
      
        return plantOptions;
      };

      const fetchMrpCtrlerOptions = async (plantList) => {
        const plantOptions = {};
      
        for (const plant of plantList) {
          let payload = { plant };
      
          try {
            const data = await new Promise((resolve) => {
              doAjax(`/${destination_MaterialMgmt}${END_POINTS?.DATA?.GET_MRP_CONTROLLER_BASED_ON_PLANT}`,
              "post",
              (response) => {
                if (response.statusCode === API_CODE?.STATUS_200) {
                  resolve(response?.body);
                } else {
                  customError("Failed to fetch data");
                  resolve([]);
                }
              },
              (error) => {
                customError(error);
                resolve([]);
              }, 
              payload);
            });
      
            plantOptions[plant] = data;
          } catch (error) {
            customError(error);
            plantOptions[plant] = [];
          }
        }
      
        return plantOptions;
      };
    
    return { fetchDisplayDataRows };
}

export default useChangeMaterialRows