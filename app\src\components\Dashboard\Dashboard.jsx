import { Box, Stack, Switch, Typography } from '@mui/material';
import { useState } from 'react';
import FilterBar from './FilterBar';
import AllCharts from './Graphs/AllCharts';
import ReportsDashboard from './ReportsDashboard';
import DashboardKpis from './DashboardKpis';

const Dashboard = () => {
  const [showReports, setShowReports] = useState(false);

  return (
    <Box sx={{ height: '100vh', overflow: 'hidden', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ position: 'sticky', top: 0, bgcolor: 'background.default', p: 2 }}>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Box>
            <Typography variant="h5" fontWeight="bold">Dashboard</Typography>
            <Typography variant="body2" color="text.secondary">
              This view displays various metrics related to Master Data
            </Typography>
          </Box>

          <Stack direction="row" alignItems="center" spacing={1}>
            <Typography variant="body2">KPI Metrics</Typography>
            <Switch
              checked={showReports}
              onChange={() => setShowReports(prev => !prev)}
              color="primary"
            />
            <Typography variant="body2">KPI Reports</Typography>
          </Stack>
        </Stack>

        {!showReports && (
          <Box mt={2}>
            <FilterBar />
            <DashboardKpis />
          </Box>
        )}
      </Box>
      <Box sx={{ flex: 1, overflowY: 'auto', p: 2 }}>
        {showReports ? <ReportsDashboard /> : <AllCharts />}
      </Box>
    </Box>
  );
};

export default Dashboard;
