import { useState, useEffect, lazy, Suspense } from "react";
import { Route, Routes } from "react-router-dom";
import { <PERSON>, Button } from "@mui/material";
import localConfigServer from "../data/localConfigServer.json";
import AppHeader from "../components/Common/AppHeader";
let SideNavBar = lazy(() => import("../components/Common/SideNavBar"));
import BroadcastHome from "../components/BroadcastManagement/BroadcastHome";
import NewBroadcast from "../components/BroadcastManagement/NewBroadcast";
import { useSelector, useDispatch } from "react-redux";
import CreateMultipleBankKey from "../components/MasterDataCockpit/BankKey/CreateMultipleBankKey.jsx";
import EditMultipleBankKey from "../components/MasterDataCockpit/BankKey/editMultipleBankKey.jsx";
import MassBKTableRequestBench from "../components/MasterDataCockpit/BankKey/MassBKTableRequestBench.jsx";
import DisplayCopyGeneralLedger from "../components/MasterDataCockpit/GeneralLedger/DisplayCopyGeneralLedger.jsx";
import LogoutWarningScreen from "./LogoutWarningScreen.jsx";
import RequestBench from "../components/RequestBench/RequestBench.jsx";
import { DialogActions, DialogContent } from "@mui/material";
import { button_Outlined, button_Primary } from "../components/Common/commonStyles.jsx";
import { WarningOutlined } from "@mui/icons-material";
import CustomDialog from "@components/Common/ui/CustomDialog";
import { useBlockingPrompt } from "@hooks/useBlockingPrompt";
import { DIALOUGE_BOX_MESSAGES } from "@constant/enum";
import { resetPayloadData } from "@app/payloadSlice";
import Document from "@components/DocumentManagement/Document";

let MyTasks = lazy(() => import("../components/ITMWorkbench/MyTasks.jsx"));
let Playground = lazy(() => import("./Playground.jsx"));
let AdminTasks = lazy(() => import("@components/ITMWorkbench/AdminTasks"));
let AdminCompletedTasks = lazy(() => import("@components/ITMWorkbench/AdminCompletedTasks"));
let CompletedTasks = lazy(() => import("../components/ITMWorkbench/CompletedTasks.jsx"));
let Dashboard = lazy(() => import("../components/Dashboard/Dashboard.jsx"));
let ApplicationConfiguration = lazy(() => import("../components/ConfigCockpit/ApplicationConfiguration/ApplicationConfigurations.jsx"));
let CostCenter = lazy(() => import("../components/MasterDataCockpit/CostCenter/CostCenter.jsx"));
let DisplayCostCenter = lazy(() => import("../components/MasterDataCockpit/CostCenter/DisplayCostCenter.jsx"));
let DisplayGeneralLedger = lazy(() => import("../components/MasterDataCockpit/GeneralLedger/DisplayGeneralLedger.jsx"));
let DisplayCopyCostCenter = lazy(() => import("../components/MasterDataCockpit/CostCenter/DisplayCopyCostCenter.jsx"));
let DisplayBankKey = lazy(() => import("../components/MasterDataCockpit/BankKey/DisplayBankKey.jsx"));
let DisplayProfitCenter = lazy(() => import("../components/MasterDataCockpit/ProfitCenter/DisplayProfitCenter.jsx"));
let DisplayCopyProfitCenter = lazy(() => import("../components/MasterDataCockpit/ProfitCenter/DisplayCopyProfitCenter.jsx"));
// let DisplayCopyGeneralLedger = lazy(() =>
//   import("../components/MasterDataCockpit/GeneralLedger/DisplayCopyGeneralLedger.jsx")
// );

let CreateNewRequest = lazy(() => import("../components/RequestBench/CreateNewRequest.jsx"));
let RequestHistory = lazy(() => import("../components/RequestHistory/RequestHistory.jsx"));
let LoadingComponent = lazy(() => import("../components/Common/LoadingComponent.jsx"));
// let Dashboard2 = lazy(() => import("../components/Dashboard/Dashboard2.jsx"));
let MaterialMaster = lazy(() => import("../components/MasterDataCockpit/MaterialMaster.jsx"));
let MassMaterialTable = lazy(() => import("../components/MasterDataCockpit/MassMaterialTable.jsx"));
let MassMaterialTableRequestBench = lazy(() => import("../components/MasterDataCockpit/MassMaterialTableRequestBench.jsx"));

let EditMultipleMaterial = lazy(() => import("../components/MasterDataCockpit/EditMultipleMaterial.jsx"));
let DisplayMultipleMaterialRequestBench = lazy(() => import("../components/MasterDataCockpit/DisplayMultipleMaterialRequestBench.jsx"));
let DisplayMultipleCostCenterRequestBench = lazy(() => import("../components/MasterDataCockpit/CostCenter/DisplayMultipleCostCenterRequestBench.jsx"));
let DisplayMultipleProfitCenterRequestBench = lazy(() => import("../components/MasterDataCockpit/ProfitCenter/DisplayMultipleProfitCenterRequestBench.jsx"));
let DisplayMultipleGLRequestBench = lazy(() => import("../components/MasterDataCockpit/GeneralLedger/DisplayMultipleGLRequestBench.jsx"));
let DisplayMultiplebankkeyRequestBench = lazy(() => import("../components/MasterDataCockpit/BankKey/displayMultiplebankKeyRequestBench.jsx"));
let CreateMaterialDetail = lazy(() => import("../components/MasterDataCockpit/CreateMaterialDetail.jsx"));
let DisplayMaterialDetail = lazy(() => import("../components/MasterDataCockpit/DisplayMaterialDetail.jsx"));
let DisplayMaterialSAPView = lazy(() => import("../components/MasterDataCockpit/DisplayMaterialSAPView.jsx"));
let NewMaterial = lazy(() => import("../components/MasterDataCockpit/NewMaterial.jsx"));
let AdditionalData = lazy(() => import("../components/MasterDataCockpit/AdditionalData.jsx"));
let DisplayAdditionalData = lazy(() => import("../components/MasterDataCockpit/DisplayAdditionalData.jsx"));
let FieldSelection = lazy(() => import("../components/Common/FieldSelection/fieldSelection.jsx"));
let FieldConfigurationForCreateBankKey = lazy(() => import("../components/MasterDataCockpit/BankKey/FieldCatalogue/FieldConfigurationForCreateBankKey.jsx"));
let FieldConfigurationProfitCenter = lazy(() => import("../components/MasterDataCockpit/ProfitCenter/FieldConfigurationProfitCenter.jsx"));

let SLAManagement = lazy(() => import("../components/ConfigCockpit/SLAManagement/SLAManagement.jsx"));
let ExtendMaterial = lazy(() => import("../components/MasterDataCockpit/ExtendMaterial.jsx"));
let NewSingleCostCenter = lazy(() => import("../components/MasterDataCockpit/CostCenter/NewSingleCostCenter.jsx"));
let ProfitCenter = lazy(() => import("../components/MasterDataCockpit/ProfitCenter/ProfitCenter.jsx"));
let NewSingleProfitCenter = lazy(() => import("../components/MasterDataCockpit/ProfitCenter/NewSingleProfitCenter.jsx"));
let BankKey = lazy(() => import("../components/MasterDataCockpit/BankKey/BankKey.jsx"));

let GeneralLedger = lazy(() => import("../components/MasterDataCockpit/GeneralLedger/GeneralLedger.jsx"));

let NewSingleBankKey = lazy(() => import("../components/MasterDataCockpit/BankKey/NewSingleBankKey.jsx"));
let NewSingleGeneralLedger = lazy(() => import("../components/MasterDataCockpit/GeneralLedger/NewSingleGeneralLedger.jsx"));
let CreateMultipleCostCenter = lazy(() => import("../components/MasterDataCockpit/CostCenter/CreateMultipleCostCenter.jsx"));
let CreateMultipleGL = lazy(() => import("../components/MasterDataCockpit/GeneralLedger/CreateMultipleGL.jsx"));

let CreateMultipleProfitCenter = lazy(() => import("../components/MasterDataCockpit/ProfitCenter/CreateMultipleProfitCenter.jsx"));
let CreateMultipleMaterial = lazy(() => import("../components/MasterDataCockpit/CreateMultipleMaterial.jsx"));
let EditMultipleCostCenter = lazy(() => import("../components/MasterDataCockpit/CostCenter/EditMultipleCostCenter.jsx"));
let EditMultipleGL = lazy(() => import("../components/MasterDataCockpit/GeneralLedger/EditMultipleGL.jsx"));
let EditMultipleProfitCenter = lazy(() => import("../components/MasterDataCockpit/ProfitCenter/EditMultipleProfitCenter.jsx"));
let MassCostCenterTableRequestBench = lazy(() => import("../components/MasterDataCockpit/CostCenter/MassCostCenterTableRequestBench.jsx"));
let MassGLTableRequestBench = lazy(() => import("../components/MasterDataCockpit/GeneralLedger/MassGLTableRequestBench.jsx"));

let MassProfitCenterTableRequestBench = lazy(() => import("../components/MasterDataCockpit/ProfitCenter/MassProfitCenterTableRequestBench.jsx"));
let FieldConfigurationCostCenter = lazy(() => import("../components/MasterDataCockpit/CostCenter/FieldConfigurationCostCenter.jsx"));
let FieldConfigurationGeneralLedger = lazy(() => import("../components/MasterDataCockpit/GeneralLedger/FieldCatalogue/FieldConfigurationForCreateGL.jsx"));
let EmailConfiguration = lazy(() => import("../components/ConfigCockpit/EmailManagement/EmailConfiguration.jsx"));

let UserManagement = lazy(() => import("../components/ConfigCockpit/UserManagementModule/UserManagement.jsx"));
let Authoring = lazy(() => import("../components/BusinessRules/Authoring/Authoring.jsx"));
let Modelling = lazy(() => import("../components/BusinessRules/Modelling/Modelling.jsx"));
let ErrorHistory = lazy(() => import("../components/RequestBench/ErrorHistory.jsx"));
function RouteGenerator() {
  // const accessList = useSelector(state => state?.masterData?.accessList)
  return (
    <Routes>
      {/* <Route path="/" element={<Dashboard2 />} /> */}
      <Route path="/dashboard" element={<Dashboard />} />
      <Route path="/broadcastManagement" element={<BroadcastHome />} />
      <Route path="/broadcastManagement/newBroadcast" element={<NewBroadcast />} />
    </Routes>
  );
}

const ApplicationRouter = () => {
  const entitesAndActivities = useSelector((state) => state.userManagement.entitiesAndActivities);
  const dispatch = useDispatch();
  let applicationConfig = useSelector((state) => state.applicationConfig);

  useEffect(() => {
    if ((entitesAndActivities, localConfigServer)) updateSideNavConfig();
  }, [entitesAndActivities]);

  /* Setting Default Dates */
  const backDate = new Date();
  backDate.setDate(backDate.getDate() - 8);

  const [sideNavConfig, setSideNavConfig] = useState(localConfigServer);

  const updateSideNavConfig = () => {
    let tempSideConfig = [];
    let index = 1;
    localConfigServer?.accessItems?.map((config) => {
      if (config.isAccessible && config.isSideOption && Object.keys(entitesAndActivities).includes(config.iwaName)) {
        tempSideConfig.push({ ...config, id: index });
        index = index + 1;
      }
    });
    const sideNavItems = Math.floor((window.innerHeight - 130) / 62);
    setSideNavConfig({
      configuration: {
        moreOptions: window.innerHeight >= 800 ? sideNavItems - 2 : sideNavItems - 1,
      },
      data: tempSideConfig,
    });
  };

  const payloadFields = useSelector((state) => state.payload.payloadData);
  const [isCreateRequestFieldFilled, setisCreateRequestFieldFilled] = useState(false);
  const { blockNavigation, isDialogVisible, handleConfirm, handleCancel } = useBlockingPrompt(isCreateRequestFieldFilled);

  useEffect(() => {
    const { RequestType, RequestDesc, RequestPriority } = payloadFields || {};
    setisCreateRequestFieldFilled(!!(RequestType || RequestDesc || RequestPriority));
  }, [payloadFields]);

  function pathRedirection(pathName) {
    if (pathName) {
      blockNavigation(pathName);
    }
  }

  const onClickNavigateNavListItem = (pathName) => {
    pathRedirection(pathName);
  };

  const onClickNavDrawerItem = (pathName) => {
    pathRedirection(pathName);
  };

  const onClickMoreNavDrawerItem = (pathName) => {
    pathRedirection(pathName);
  };

  const handlePayloadData = () => {
    dispatch(resetPayloadData({ data: {} }));
  };

  const handleYes = () => {
    handleConfirm();
    handlePayloadData();
  };

  useEffect(() => {
    if ((entitesAndActivities, localConfigServer)) updateSideNavConfig();
  }, [entitesAndActivities]);

  return (
    <Box sx={{ backgroundColor: (theme) => theme.background.default }}>
      <AppHeader />
      {sideNavConfig && entitesAndActivities && (
        <SideNavBar
          entitesAndActivities={entitesAndActivities}
          sideNavOptions={sideNavConfig}
          onClickNavigateNavListItem={onClickNavigateNavListItem}
          onClickNavDrawerItem={onClickNavDrawerItem}
          onClickMoreNavDrawerItem={onClickMoreNavDrawerItem}
          // mode={localConfigServer?.mode}
        />
      )}

      <Box
        id={"app_Container"}
        sx={(theme) => ({
          // display: "flex",
          flexGrow: 1,
          marginTop: "63px",
          marginLeft: "90px",
          height: "calc(100vh - 63px)",
          // minHeight: "calc(100% - 36px)",
          // minWidth: "calc(100% - 90px)",
          boxSizing: "border-box",
          paddingBottom: "64px",
          overflowY: "auto",
        })}
      >
        {applicationConfig?.logoutUserWarning && <LogoutWarningScreen />}
        <Suspense fallback={<LoadingComponent />}>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/broadcastManagement" element={<BroadcastHome />} />
            <Route path="/masterDataCockpit/materialMaster/materialSingle" element={<MaterialMaster />} />
            <Route path="/masterDataCockpit/materialMaster/massMaterialTable" element={<MassMaterialTable />} />
            <Route path="/playground" element={<Playground />} />

            <Route path="/masterDataCockpit/materialMaster/editMultipleMaterial/:description" element={<EditMultipleMaterial />} />
            <Route path="/masterDataCockpit/materialMaster/displayMultipleMaterialRequestBench/:description" element={<DisplayMultipleMaterialRequestBench />} />
            <Route path="/masterDataCockpit/costCenter/massCostCenterTableRequestBench/displayMultipleCostCenterRequestBench/:costCenter" element={<DisplayMultipleCostCenterRequestBench />} />
            <Route path="/masterDataCockpit/profitCenter/massProfitCenterTableRequestBench/displayMultipleProfitCenterRequestBench/:profitCenter" element={<DisplayMultipleProfitCenterRequestBench />} />
            <Route path="/masterDataCockpit/bankkey/massBKTableRequestBench/displayMultipleBankKeyRequestBench/:bankKey" element={<DisplayMultiplebankkeyRequestBench />} />
            <Route path="/masterDataCockpit/generalLedger/massGLTableRequestBench/displayMultipleGLRequestBench/:glAccount" element={<DisplayMultipleGLRequestBench />} />
            <Route path="/masterDataCockpit/materialMaster/createMaterialDetail" element={<CreateMaterialDetail />} />
            <Route path="/masterDataCockpit/materialMaster/materialSingle/createMultipleMaterial" element={<CreateMultipleMaterial />} />
            <Route path="/masterDataCockpit/materialMaster/displayMaterialDetail/:reqId" element={<DisplayMaterialDetail />} />
            <Route path="/masterDataCockpit/materialMaster/newMaterial" element={<NewMaterial />} />
            <Route path="/masterDataCockpit/materialMaster/createMaterialDetail/additionalData" element={<AdditionalData />} />
            <Route path="/masterDataCockpit/materialMaster/displayMaterialDetail/displayAdditionalData" element={<DisplayAdditionalData />} />
            <Route path="/configCockpit/fieldSelection" element={<FieldSelection />} />
            {/* Config Cockpit */}
            <Route path="/configCockpit/userManagement" element={<UserManagement />} />
            <Route path="/configCockpit/applicationConfiguration" element={<ApplicationConfiguration />} />
            {/* <Route
              path="/ApplicationSettings"
              element={<ApplicationSettings />}
            /> */}
            <Route path="/configCockpit/SLAManagement" element={<SLAManagement />} />
            <Route path="/masterDataCockpit/materialMaster/displayMaterialDetail/extend/:reqId" element={<ExtendMaterial />} />
            <Route path="/masterDataCockpit/materialMaster/DisplayMaterialSAPView/:matNo" element={<DisplayMaterialSAPView />} />

            <Route path="/masterDataCockpit/costCenter" element={<CostCenter />} />
            <Route path="/configCockpit/businessRules/authoring" element={<Authoring />} />
            <Route path="/configCockpit/businessRules/modelling" element={<Modelling />} />
            <Route path="/masterDataCockpit/costCenter/displayCostCenter/:reqId" element={<DisplayCostCenter />} />
            <Route path="/masterDataCockpit/costCenter/displayCopyCostCenter/:reqId" element={<DisplayCopyCostCenter />} />
            <Route path="/masterDataCockpit/generalLedger/displayCopyGeneralLedger" element={<DisplayCopyGeneralLedger />} />
            <Route path="/masterDataCockpit/profitCenter/displayProfitCenter/:reqId" element={<DisplayProfitCenter />} />
            <Route path="/masterDataCockpit/profitCenter/displayCopyProfitCenter/:reqId" element={<DisplayCopyProfitCenter />} />
            <Route path="/masterDataCockpit/generalLedger/displayGeneralLedger/:reqId" element={<DisplayGeneralLedger />} />
            <Route path="/masterDataCockpit/bankKey" element={<BankKey />} />
            <Route
              path="/masterDataCockpit/bankKey/displayBankKey/:reqId"
              element={<DisplayBankKey />}
            />
            <Route
              path="/masterDataCockpit/bankKey/massBKTableRequestBench"
              element={<MassBKTableRequestBench />}
            />
            <Route
              path="/masterDataCockpit/bankKey/newSingleBankKey"
              element={<NewSingleBankKey />}
            />
            <Route
              path="/masterDataCockpit/bankKey/createMultipleBankKey"
              element={<CreateMultipleBankKey />}
            />
            <Route
              path="/masterDataCockpit/generalLedger"
              element={<GeneralLedger />}
            />
            <Route
              path="/masterDataCockpit/costCenter/newSingleCostCenter"
              element={<NewSingleCostCenter />}
            />
            <Route
              path="/masterDataCockpit/generalLedger/newSingleGeneralLedger"
              element={<NewSingleGeneralLedger />}
            />
            <Route
              path="/masterDataCockpit/profitCenter"
              element={<ProfitCenter />}
            />
            <Route
              path="/masterDataCockpit/profitCenter/newSingleProfitCenter"
              element={<NewSingleProfitCenter />}
            />
            <Route
              path="/masterDataCockpit/costCenter/createMultipleCostCenter"
              element={<CreateMultipleCostCenter />}
            />
            <Route
              path="/masterDataCockpit/generalLedger/createMultipleGL"
              element={<CreateMultipleGL />}
            />
            <Route
              path="/masterDataCockpit/profitCenter/createMultipleProfitCenter"
              element={<CreateMultipleProfitCenter />}
            />
            <Route
              path="/masterDataCockpit/costCenter/createMultipleCostCenter/editMultipleCostCenter/:costCenter"
              element={<EditMultipleCostCenter />}
            />
            <Route
              path="/masterDataCockpit/generalLedger/createMultipleGL/editMultipleGL/:costCenter"
              element={<EditMultipleGL />}
            />
            <Route
              path="/masterDataCockpit/profitCenter/createMultipleProfitCenter/editMultipleProfitCenter/:profitCenter"
              element={<EditMultipleProfitCenter />}
            />
            <Route
              path="/masterDataCockpit/bankKey/createMultipleBankKey/editMultipleBankkey/:bankKey"
              element={<EditMultipleBankKey />}
            />
            <Route
              path="/masterDataCockpit/costCenter/massCostCenterTableRequestBench"
              element={<MassCostCenterTableRequestBench />}
            />
            <Route
              path="/masterDataCockpit/generalLedger/massGLTableRequestBench"
              element={<MassGLTableRequestBench />}
            />
            <Route
              path="/masterDataCockpit/profitCenter/massProfitCenterTableRequestBench"
              element={<MassProfitCenterTableRequestBench />}
            />
            <Route
              path="/configCockpit/fieldConfiguration/costCenter"
              element={<FieldConfigurationCostCenter />}
            />
            <Route
              path="/configCockpit/emailTemplateConfig"
              element={<EmailConfiguration />}
            />
            <Route
              path="/configCockpit/fieldConfiguration/profitCenter"
              element={<FieldConfigurationProfitCenter />}
            />
            <Route
              path="/configCockpit/fieldConfiguration/generalLedger"
              element={<FieldConfigurationGeneralLedger />}
            />
            <Route
              path="/configCockpit/fieldConfiguration/bankKey"
              element={<FieldConfigurationForCreateBankKey />}
            />
            <Route path="/requestBench" element={<RequestBench/>} />
            <Route path="/documentManagement" element={<Document/>} />
            <Route path="/requestBench/createRequest" element={<CreateNewRequest/>} />
            <Route path="/requestBench/RequestHistory" element={<RequestHistory />} />
            <Route path="/workspace/MyTasks" element={<MyTasks />} />
            <Route path="/workspace/CompletedTasks" element={<CompletedTasks />} />
            <Route path="/workspace/AdminTasks" element={<AdminTasks />} />
            <Route path="/workspace/AdminCompletedTasks" element={<AdminCompletedTasks />} />
            <Route path="/requestBench/errorHistory" element={<ErrorHistory />} />
          </Routes>
        </Suspense>
      </Box>

      {isDialogVisible && (
        <CustomDialog isOpen={isDialogVisible} titleIcon={<WarningOutlined size="small" sx={{ color: "orange", fontSize: "20px" }} />} Title={"Warning"} handleClose={handleCancel}>
          <DialogContent sx={{ mt: 2 }}>{DIALOUGE_BOX_MESSAGES.LEAVE_PAGE_MESSAGE}</DialogContent>
          <DialogActions>
            <Button variant="outlined" size="small" sx={{ ...button_Outlined }} onClick={handleCancel}>
              No
            </Button>
            <Button variant="contained" size="small" sx={{ ...button_Primary }} onClick={handleYes}>
              Yes
            </Button>
          </DialogActions>
        </CustomDialog>
      )}
    </Box>
  );
};

export default ApplicationRouter;
