import { configureStore } from "@reduxjs/toolkit";
import initialDataReducer from "./initialDataSlice";
import tabsDetailsReducer from "./tabsDetailsSlice";
import dropDownDataReducer from "./dropDownDataSlice";
import commonFilterReducer from "./commonFilterSlice";
import selectedSectionsReducer from './selectedSelectionsSlice';
// import utilityReducer from "./utilitySlice";
// import dprReducer from "./dprSlice";
// import serviceRequestReducer from "./serviceRequestSlice";
import userManagementReducer from "./userManagementSlice";
import masterDataReducer from "./masterDataSlice";
// import notificationsReducer from "./notificationsSlice";
import applicationConfigReducer from "./applicationConfigReducer";
import commonSearchBarReducer from "./commonSearchBarSlice";
import appSettingsReducer from "./appSettingsSlice";
import userReducer from "./userReducer";
import { payloadReducer } from "./payloadslice";
import tabsDetailsSlice from "./tabsDetailsSlice";
import { costCenterReducer } from "./costCenterTabsSlice";
import { profitCenterReducer } from "./profitCenterTabsSlice";
import { editPayloadReducer } from "./editPayloadSlice";
import { bankKeyReducer } from "./bankKeyTabSlice";
import { generalLedgerReducer } from "./generalLedgerTabSlice";
import notificationSlice from "./notificationSlice";
import { requestSliceReducer } from "./requestDataSlice";
import { changeLogReducer } from "./changeLogReducer";
import { paginationReducer } from "./paginationSlice"

let Store = configureStore({
  reducer: {
    // invoice: invoiceReducer,
    // purchaseOrder:purchaseOrderReducer,
    initialData: initialDataReducer,
    commonFilter: commonFilterReducer,
    commonSearchBar: commonSearchBarReducer,
    selectedSections: selectedSectionsReducer,
    // tabsDetails: tabsDetailsSlice, // commented because used twice, please check if any issue occurs, use tabsData instead of this
    // createInvoice: createInvoiceReducer,
    userManagement: userManagementReducer,
    // utility:utilityReducer,
    // DPR:dprReducer,
    // SR:serviceRequestReducer,
    userReducer: userReducer,
    // initiateReturn: createReturnReducer,
    masterData: masterDataReducer,
    // notifications: notificationsReducer,
    applicationConfig: applicationConfigReducer,
    // initiateSES: initiateServiceEntryReducer,
    appSettings: appSettingsReducer,
    tabsData: tabsDetailsReducer, //name
    payload: payloadReducer,
    paginationData: paginationReducer,
    changeLog:changeLogReducer,
    AllDropDown: dropDownDataReducer,
    costCenter: costCenterReducer,
    profitCenter: profitCenterReducer,
    bankKey: bankKeyReducer,
    generalLedger: generalLedgerReducer,
    edit: editPayloadReducer,
    notifications: notificationSlice,
    request:requestSliceReducer,
  },
});

export default Store;
