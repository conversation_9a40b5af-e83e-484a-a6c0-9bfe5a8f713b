import {
  Autocomplete,
  Backdrop,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  MenuItem,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import CloseIcon from "@mui/icons-material/Close";
import { font_Small } from "./commonStyles";
import { Box } from "@mui/system";
import { useSelector, useDispatch } from "react-redux";
import { destination_Admin, destination_SLA_Mgmt } from "../../destinationVariables";
import ReusablePromptBox from "./ReusablePromptBox/ReusablePromptBox";
import { doAjax } from "./fetchService";
import { appSettingsUpdate } from "../../app/appSettingsSlice";
import moment from "moment/moment";
import { formValidator } from "../../functions";
import SystemConfiguration from "./ReusableAttachmentAndComments/SystemConfiguration";

export default function ApplicationSettings(props) {
  const [loading, setLoading] = useState(false);
  const [extension, setextension] = useState(true);
  const appSettings = useSelector((state) => state.appSettings);
  let userData = useSelector((state) => state.userManagement.userData);
  let masterData = useSelector((state) => state.masterData);
  const dropDownData = masterData?.dropDown?.["Application Settings"];

  const [SettingsObj, setSettingsObj] = useState({
    dateFormat: appSettings.dateFormat,
    range: appSettings.range,
    timeFormat: appSettings.timeFormat,
    // timeZone: appSettings.timeZone,
  });
  const [formValidationErrorItems, setFormValidationErrorItems] = useState([]);
  const dispatch = useDispatch();
  const handleSelect = (e) => {
    setSettingsObj({ ...SettingsObj, [e.target.name]: e.target.value });
  };
  const handleFormat = (e) => {
    setSettingsObj({ ...SettingsObj, dateFormat: e.target.value });
  };
  const handleRange = (e) => {
    setSettingsObj({ ...SettingsObj, range: e.target.value });
  };
  const handleClear = () => {
    setSettingsObj({
      ...SettingsObj,
      dateFormat: "",
      range: "",
      timeFormat: "",
      // timeZone: "",
    });
    setFormValidationErrorItems([]);
  };
  const fetchAppSettings = () => {
    let hSuccess = (data) => {
      if (!data) {
        dispatch(appSettingsUpdate());
      } else {
        if (
          data.dateFormat ||
          // data.timeZone ||
          data.dateRangeValue ||
          data.timeFormat
        ) {
          const appSettingsData = {
            dateFormat: data.dateFormat,
            // timeZone: "Asia/Kolkata",
            range: data.dateRangeValue,
            timeFormat: data.timeFormat,
          };
          dispatch(appSettingsUpdate(appSettingsData));
          setSettingsObj({
            ...SettingsObj,
            dateFormat: data.dateFormat,
            // timeZone: "Asia/Kolkata",
            range: data.dateRangeValue,
            timeFormat: data.timeFormat,
          });
        }
      }
    };
    let hError = () => {};
    doAjax(
      `/${destination_SLA_Mgmt}/applicationSetting/getApplicationSetting/${userData?.emailId}`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(() => {
    fetchAppSettings();
  }, []);
  const toggleAcordion = () => {
    setextension((prev) => !prev);
  };

  //<-- Functions and variables for ReusablePromptBox *promptAction_Functions -->
  const [promptBoxState, setPromptBoxState] = useState({
    open: false,
    type: "",
    redirectOnClose: true,
    message: "",
    title: "",
    severity: "",
  });
  const [promptBoxScenario, setPromptBoxScenario] = useState("");
  const promptAction_Functions = {
    handleClosePromptBox: () => {
      setPromptBoxState((prev) => ({
        open: false,
        type: "",
        redirectOnClose: false,
        message: "",
        title: "",
        severity: "",
      }));
      setPromptBoxScenario("");
      setFormValidationErrorItems([]);
    },
    handleOpenPromptBox: (ref, data = {}) => {
      // SUCCESS,FAILURE
      let initialData = {
        open: true,
        title: "",
        message: "",
        okButton: true,
        cancelButton: true,
        okButtonText: "Ok",
        cancelText: "Cancel",
        type: "dialog",
      };
      if (ref === "SUCCESS") {
        initialData.type = "snackbar";
      }
      setPromptBoxScenario(ref);
      setPromptBoxState({
        ...initialData,
        ...data,
      });
    },
    handleCloseAndRedirect: () => {
      promptAction_Functions.handleClosePromptBox();
      props?.handleClose();
    },
    getCancelFunction: () => {
      switch (promptBoxScenario) {
        default:
          return promptAction_Functions.handleClosePromptBox;
      }
    },
    getCloseFunction: () => {
      switch (promptBoxScenario) {
        default:
          return promptAction_Functions.handleClosePromptBox;
      }
    },
    getCloseAndRedirectFunction: () => {
      if (!promptBoxState.redirectOnClose) {
        return promptAction_Functions.handleClosePromptBox;
      }
      return promptAction_Functions.handleCloseAndRedirect;
    },
  };

  const handleSave = () => {
    let payload = {
      dateRangeValue: SettingsObj.range,
      dateFormat: SettingsObj.dateFormat,
      email: userData?.emailId,
      // timeZone: "Asia/Kolkata",
      timeFormat: SettingsObj.timeFormat,

      // language:"",
      landingPage:"",
      // roleId:""

    };
    let hSuccess = (data) => {
      const Data = {
        dateFormat: SettingsObj.dateFormat,
        range: SettingsObj.range,
        timeFormat: SettingsObj.timeFormat,
        // timeZone: "Asia/Kolkata",
      };
      dispatch(appSettingsUpdate(Data));
      if (data.status === "Success") {
        promptAction_Functions.handleOpenPromptBox("SUCCESS", {
          message: `Application Settings Updated Successfully`,
          redirectOnClose: true,
        });
      } else {
        promptAction_Functions.handleOpenPromptBox("ERROR", {
          title: "Failed",
          message: `Application Settings Update Failed`,
          severity: "danger",
          cancelButton: false,
        });
      }
    };
    let hError = () => {
      promptAction_Functions.handleOpenPromptBox("ERROR", {
        title: "Failed",
        message: `Application Settings Update Failed`,
        severity: "danger",
        cancelButton: false,
      });
    };
    doAjax(
      `/${destination_SLA_Mgmt}/applicationSetting/saveApplicationSetting`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  // const timezoneOptions = moment.tz.names();

  return (
    <div>
      <ReusablePromptBox
        type={promptBoxState.type}
        promptState={promptBoxState.open}
        setPromptState={promptAction_Functions.handleClosePromptBox}
        onCloseAction={promptAction_Functions.getCloseFunction()}
        promptMessage={promptBoxState.message}
        dialogSeverity={promptBoxState.severity}
        dialogTitleText={promptBoxState.title}
        handleCancelButtonAction={promptAction_Functions.getCancelFunction()}
        cancelButtonText={promptBoxState.cancelText} //Cancel button display text
        showCancelButton={promptBoxState.cancelButton} //Enable Cancel button
        handleSnackBarPromptClose={promptAction_Functions.getCloseAndRedirectFunction()}
        // handleOkButtonAction={promptAction_Functions.getOkFunction()}
        okButtonText={promptBoxState.okButtonText}
        showOkButton={promptBoxState.okButton}
      />
      <Backdrop sx={{ color: "#fff", zIndex: 100 }} open={loading}>
        <CircularProgress color="primary" />
      </Backdrop>

      <Dialog
        maxWidth="xs"
        open={props?.status}
        onClose={() => {
          props?.handleClose();
        }}
      >
        <DialogTitle
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            height: "max-content",
            padding: ".5rem",
            paddingLeft: "1rem",
            backgroundColor: "#EAE9FF40",
            display: "flex",
          }}
        >
          <Typography variant="h6">Application Settings</Typography>

          <IconButton
            sx={{ width: "max-content" }}
            onClick={() => {
              props?.handleClose();
            }}
            children={<CloseIcon />}
          />
        </DialogTitle>
        <DialogContent sx={{ padding: ".5rem 1rem" }}>
          <Grid
            container
            rowSpacing={1}
            spacing={2}
            sx={{ paddingTop: ".5rem" }}
          >
            <Grid item md={12}>
              <Box>
                <Typography sx={font_Small}>Date Format</Typography>
                <FormControl fullWidth size="small" sx={{ margin: ".5em 0px" }}>
                  <Select
                    placeholder="Select Date Format"
                    select
                    sx={font_Small}
                    size="small"
                    value={SettingsObj.dateFormat}
                    name={"dateFormat"}
                    onChange={handleFormat}
                    displayEmpty={true}
                    error={formValidationErrorItems.includes("dateFormat")}
                  >
                    <MenuItem sx={font_Small} value={""}>
                      <div style={{ color: "#C1C1C1" }}>
                        Select Date Format{" "}
                      </div>
                    </MenuItem>

                    <MenuItem value={"DD MMM YYYY"}>
                      DD MMM YYYY (01 Apr 2023)
                    </MenuItem>
                    <MenuItem value={"MMM DD, YYYY"}>
                      MMM DD, YYYY (Apr 01, 2023)
                    </MenuItem>
                    <MenuItem value={"YYYY MMM DD"}>
                      YYYY MMM DD (2023 Apr 01)
                    </MenuItem>

                    <MenuItem value={"DD-MM-YYYY"}>
                      DD-MM-YYYY (01-04-2023)
                    </MenuItem>
                    <MenuItem value={"MM-DD-YYYY"}>
                      MM-DD-YYYY (04-01-2023)
                    </MenuItem>
                    <MenuItem value={"YYYY-MM-DD"}>
                      YYYY-MM-DD (2023-04-01)
                    </MenuItem>

                    <MenuItem value={"DD/MM/YYYY"}>
                      DD/MM/YYYY (01/04/2023)
                    </MenuItem>
                    <MenuItem value={"MM/DD/YYYY"}>
                      MM/DD/YYYY (04/01/2023)
                    </MenuItem>
                    <MenuItem value={"YYYY/MM/DD"}>
                      YYYY/MM/DD (2023/04/01)
                    </MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </Grid>
            <Grid item md={12}>
              <Box>
                <Typography sx={font_Small}>Time Format</Typography>
                <FormControl fullWidth size="small" sx={{ margin: ".5em 0px" }}>
                  <Select
                    placeholder="Select Time Format"
                    select
                    size="small"
                    value={SettingsObj.timeFormat}
                    name={"timeFormat"}
                    onChange={handleSelect}
                    displayEmpty={true}
                    sx={font_Small}
                    error={formValidationErrorItems.includes("timeFormat")}
                  >
                    <MenuItem sx={font_Small} value={""}>
                      <div style={{ color: "#C1C1C1" }}>
                        Select Time Format{" "}
                      </div>
                    </MenuItem>

                    <MenuItem value={"hh:mm A"}>12-hour (01:34 AM)</MenuItem>
                    <MenuItem value={"HH:mm"}>24-hour (13:34)</MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </Grid>
            {/* <Grid item md={12}>
              <Box>
                <Typography sx={font_Small}>Time Zone</Typography>
                <Autocomplete
                  size="small"
                  placeholder={`Select Time Zone`}
                  // sx={font_Small}
                  value={SettingsObj.timeZone}
                  onChange={(e, newValue) => {
                    const updatedValue = newValue || ``;
                    handleSelect({
                      target: { name: "timeZone", value: updatedValue },
                    });
                  }}
                  options={timezoneOptions}
                  autoHighlight
                  getOptionLabel={(option) => option}
                  renderInput={(params) => (
                    <TextField
                      fullWidth
                      size="small"
                      {...params}
                      placeholder="Select Time Zone"
                    />
                  )}
                  sx={{
                    ...font_Small,
                    backgroundColor: "white",
                    border: formValidationErrorItems.includes("timeZone")
                      ? "1px solid red"
                      : "",
                    borderRadius: "6px",
                  }}
                  error={formValidationErrorItems.includes("timeZone")}
                />
              </Box>
            </Grid> */}
            <Grid item md={12}>
              <Box>
                <Typography sx={font_Small}>Default Date Range</Typography>
                <FormControl fullWidth size="small" sx={{ margin: ".5em 0px" }}>
                  <Select
                    placeholder="Select Default Date Range"
                    select
                    size="small"
                    value={SettingsObj.range}
                    name={"range"}
                    onChange={handleRange}
                    displayEmpty={true}
                    sx={font_Small}
                    error={formValidationErrorItems.includes("range")}
                  >
                    <MenuItem sx={font_Small} value={0}>
                      <div style={{ color: "#C1C1C1" }}>
                        Select Default Date Range
                      </div>
                    </MenuItem>

                    {[
                      "Last Week",
                      "Last Month",
                      "Current Month",
                      "Current Quarter",
                      "Year To Date",
                    ]?.map((rangeName) => (
                      <MenuItem
                        value={
                          rangeName === "Last Week"
                            ? 7
                            : rangeName === "Last Month"
                            ? 50
                            : rangeName === "Current Month"
                            ? 100
                            : rangeName === "Current Quarter"
                            ? 150
                            : rangeName === "Year To Date"
                            ? 200
                            : 0
                        }
                      >
                        {rangeName}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
            </Grid>
            {
              
                <Grid item md={12} alignItems={"center"}>
                  {/* <Typography sx={font_Small}>System Configuration (ECC/S4)</Typography> */}
                  <Box>
                    <SystemConfiguration/>
                  </Box>
                </Grid>
              
             
            }
          </Grid>
        </DialogContent>
        <DialogActions
          sx={{ display: "flex", justifyContent: "end", margin: "0 0.5rem" }}
        >
          <Button
            sx={{ width: "max-content", textTransform: "capitalize" }}
            onClick={handleClear}
            variant="outlined"
          >
            Clear
          </Button>
          <Button
            className="button_primary--normal"
            type="save"
            variant="contained"
            onClick={() => {
              if (
                formValidator(
                  SettingsObj,
                  ["dateFormat", "range", "timeFormat"],
                  setFormValidationErrorItems
                )
              ) {
                handleSave();
              }
            }}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}
