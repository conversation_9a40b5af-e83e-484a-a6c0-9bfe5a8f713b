import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
let initialState_appSettings = {
  dateFormat: "DD MMM YYYY",
  timeFormat: "hh:mm A",
  range: 7,
  // timeZone: "Asia/Kolkata",
};
const appSettingsSlice = createSlice({
  name: "appSettingsSlice",
  initialState: initialState_appSettings,
  reducers: {
    appSettingsUpdate(state, action) {
      const { dateFormat, timeFormat, range, timeZone } = action.payload;

      if (dateFormat !== "") {
        state.dateFormat = dateFormat;
      }

      if (timeFormat !== "") {
        state.timeFormat = timeFormat;
      }

      if (range !== "") {
        state.range = range;
      }

      // if (timeZone !== "") {
      //   state.timeZone = timeZone;
      // }
    },
  },
});
export default appSettingsSlice.reducer;
export const { appSettingsUpdate } = appSettingsSlice.actions;