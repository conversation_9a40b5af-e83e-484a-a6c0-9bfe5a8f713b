export const END_POINTS = {
  MASS_ACTION: {
    CREATE_MATERIAL_SEND_FOR_REVIEW: '/massAction/createMaterialSendForReview',
    EXTEND_MATERIAL_SEND_FOR_REVIEW: '/massAction/extendMaterialSendForReview',
    CHANGE_MATERIAL_SEND_FOR_REVIEW: '/massAction/changeMaterialSendForReview',
    CREATE_MATERIAL_SEND_FOR_CORRECTION: '/massAction/createMaterialSendForCorrection',
    CHANGE_MATERIAL_SEND_FOR_CORRECTION: '/massAction/changeMaterialSendForCorrection',
    VALIDATE_MATERIAL:'/massAction/validateMaterial',
    EXTEND_MATERIAL_SEND_FOR_CORRECTION: '/massAction/extendMaterialSendForCorrection',
    CREATE_MATERIAL_REJECTION:'/massAction/createMaterialRejection',
    CHAN<PERSON>_MATERIAL_REJECTION:'/massAction/changeMaterialRejection',
    EXTEND_MATERIAL_REJECTION:'/massAction/extendMaterialRejection',
    MAT_NO_DUPLICATE_CHECK : '/massAction/fetchMaterialNosDupliChk',
    MRP_DEFAULT_VALUES: '/data/getMrpBasedOnMrpProfile',
    EXTEND_MATERIAL_SAVE_AS_DRAFT:'/massAction/extendMaterialSaveAsDraft',
    EXTEND_MATERIAL_DIRECT_APPROVED:'/massAction/extendMaterialDirectApproved',
    VALIDATE_FINANCE_COSTING: '/massAction/validateFinanceData',
    AGGREGATE_DAILY_REQUESTS: 'massAction/aggregateDailyRequests',
    FINANCE_COSTING_APPROVED:'massAction/financeCostingApproved',
    WORKFLOW_DETAILS_BIFURCATION:'/massAction/workflow-details/bifurcation',
    MATERIAL_SEND_TO_LEVEL: '/massAction/materialSendToLevel'
  },
  DATA:{
    GET_SEARCH_PARAMS_MATERIAL_NO:'/data/getSearchParamsMaterialNo',
    DISPLAY_MATERIAL_DTO:'/data/displayMaterialDTO',
    COPY_FROM_MATERIAL_ORG_ELMS_ETEXTEND:'/data/CopyFromMaterialOrgElmSetExtend',
    GET_EXTEND_SEARCH_SET:'/data/getExtendSearchSet',
    GET_VALUATION_CLASS:'/data/getValuationClass',
    GET_SPPROC_TYPE:'/data/getSpecialProcurementBasedOnPlant',
    GET_LANGUAGE:'/data/getLangu',
    GET_COPY_MATERIAL:'/data/CopyFromMaterialOrgElmSetExtend',
    GET_STORAGE_LOCATION_FOR_PLANT:'/data/getStorageLocationForPlant',
    GET_STORAGE_LOCATION_SET_BASED_ON_PLANT:'/data/getStorageLocationSetBasedOnPlant',
    GET_SPECMVMT:'/data/getSpecMvmt',
    GET_DISTRCHNL:'/data/getDistrChan',
    GET_DELIVARING_PLANT_BASED_ON_SALES_ORG_AND_DISTCHNL:'/data/getDelivaringPlantBasedOnSalesOrgAndDistChnl',
    GET_PROFIT_CENTER_BASED_ON_PLANT:'/data/getProfitCenterBasedOnPlant',
    GET_MRP_CONTROLLER_BASED_ON_PLANT:'/data/getMRPControllerBasedOnPlant',
    GET_PRODUCTION_SCHEDULING_PROFILE_BASED_ON_PLANT:'/data/getProductionSchedulingProfileBasedOnPlant',
    GET_PROD_STORAGE_LOCATION_BASED_ON_PLANT:'/data/getProdStorageLocationBasedOnPlant',
    GET_PROCUREMENT_STORAGE_LOCATION_BASED_ON_PLANT:'/data/getProcurementStorageLocationBasedOnPlant',
    GET_SALES_ORG_BASED_ON_PLANT:'/data/getSalesOrgBasedOnPlant',
    GET_SCHEDULING_MARGIN_KEY_BASED_ON_PLANT:'/data/getSchedulingMarginKeyBasedOnPlant',
    GET_COMMODITY_CODE_BASED_ON_COUNTRY:'/data/getCommodityCodeBasedOnCountry',
    GET_PLACEMENT:'/data/getPlacement',
    GET_CHECK_DIGIT:`/data/calculateCheckDigit`,
    GET_HTS_CODE : '/data/getHTSCode',
    GET_SALES_ORG_EXTENDED : '/data/getSalesOrgExtended',
    GET_PLANT_EXTENDED : '/data/getPlantExtended',
    GET_DISTR_CHAN_EXTENDED : '/data/getDistributionChannelExtended',
    GET_WAREHOUSE_EXTENDED : '/data/getWarehouseExtended',
    GET_STOR_LOC_EXTENDED : '/data/getStorageLocationExtended',
    VALIDATE_MANUFACTURER_ID : '/data/checkMINumber',
    GET_COUNTRY_BASED_ON_PLANT : '/data/getCountryBasedOnPlant'
  },
  WORK_FLOW:{
    CANCEL_WORKFLOW:'/workflow/cancelWorkflow',
    FETCH_USER_DATA:'/workflow/fetchIASUsersData',
    FETCH_ROLES_DATA:'/workflow/fetchEntitiesAndActivitiesFromRoles?applicationId=1',
    CANCEL_WORKFLOWS: '/workflow/cancelWorkflows',
  },
  INVOKE_RULES:{
    LOCAL:"/rest/v1/invoke-rules",
    PROD:"/v1/invoke-rules"
  },
  USER_ACCESS: {
    CHECK_ACCESS: "/data/getUserAccess"
  },
  CHG_DISPLAY_REQUESTOR:{
    LOGISTIC: 'data/getLogisticData',
    MRP: 'data/getMRPData',
    SALES: 'data/getSalesData',
    DESC: 'data/getDescriptionData',
    WAREHOUSE: 'data/getWareHouseData',
    CHG_STATUS: 'data/getChangeStatusData',
    SET_DNU: 'data/getSetToDNUData',
    FETCH_CHANGELOG_DATA: 'data/fetchChangeLogData',
    DISPLAY_DTO: 'data/displayMassMaterialDTO'
  },
  MAT_SEARCH_APIS:{
    LOGISTIC: '/data/getMatNoForLogisticData',
    MRP: '/data/getMatNoForMRPData',
    SALES: '/data/getMatNoForSalesData',
    DESC: '/data/getMatNoForUpdateDescriptionData',
    WAREHOUSE: '/data/getMatNoForWareHouseData',
    CHG_STATUS: '/data/getMatNoForChangeStatusData',
    SET_DNU: '/data/getMatNoForSetToDnuData',
  },
  DEPENDENT_LOOKUPS:{
    UNITTYPE: '/data/getUnitType',
  },
  DMS_API:{
    UPLOAD_DOCUMENT : 'documentManagement/uploadDocument',
    FETCH_DOCUMENTS : 'documentManagement/filterDocument',
    GET_ATTACHMENT_TYPE:"documentManagement/getAttachmentType",
    GET_FILE_TYPE:"documentManagement/getDocType"
  },
  REQUEST_BENCH:{
    BIFURCATION_DETAILS : '/child-requests',
    DIVISION : 'data/getSearchParamDivision',
    CREATED_BY : 'data/getSearchParamCreatedBy',
  },
  DOCUMENT_MANAGEMENT: {
    DELETE: '/documentManagement/delete',
  },
  TAX_DATA:{
    GET_TAX_COUNTRY:'/data/getTaxDataSetBasedOnCountry',
    GET_COUNTRY_SALESORG:'/data/getCountriesBasedOnSalesOrg',

  },
  DASHBOARD_APIS:{
    All_MATERIALS_BY_PLANT_AND_SALES:'getAllMaterialsByPlantsAndSalesOrg',
    DRAFT_STATUS:'getAllReqIdInDraftStatus',
    MATERIAL_LIST:'getAllCreatedMatList',
    PRICING_GROUP:'getAllMatPricingGroup',
    MISSING_HTS:'getHTCCodeMissing',
    KPI3: 'counts/getTotalRequestsPendingByGroups',
    KPI7: 'counts/getTotalChangeTemplateCount',
    KPI8: 'counts/getTotalReqBasedOnRegion',
    KPI10 : 'counts/getTop5Requestor',
    KPI1: '/getTotalRequestsBasedOnReqTypeAndReqStatus',
    KPI2: '/getAverageLifecycleDurationOfReqByObjType',
    KPI4: '/getPercentageOfReqSuccCompWithOutRejection',
    KPI5: '/getPercentageOfReqSuccCompleted',
    KPI6: '/getReqThatHaveBreachedSLA',
    KPI9: '/getHowManyReqWereRejectedByRoleByUser',
    KPI11: '/getHowManyRequestsArePendingApproverMDM',
    KPI12: '/getAvgTimeFromMDMApproveToSAPSyndication',
    KPI13: '/getAvgApprovalTimeByApprover',
    KPI14: '/getNumberOfRejectedReqBasedOnRequestor',
    KPI15: '/getApproverWhoHaveBreachedSLA',
    KPI16: '/getApproverAndTheirAvgApprovalTime',
    KPI17: '/getNoOfReqCompletedByMDM',
    KPI18: '/getNoOfOpenReqByReqType',
    KPI19: '/getNoOfReqRequestedAndNoOfApprovApproved',
    KPI20: '/getAllReqPendingApprover',
    KPI21: '/getAllTempBlockRequests',
    KPI22: '/getAllScheduledRequests',
    KPI23: '/getAllScheduledPDFs',
    KPI24: '/getReqHoldWithRequestors',
    KPI25: '/getAllErroneousRequests',
    KPI26: '/getObjectNoAssociatedWithRequestId',
    KPI27: '/getAllFERCRequests',
    KPI28: '/fetchRequestHistory',
    KPI29: '/getObjectNoAssociatedWithSunoco',
    KPI30: '/getNoOfFailedReqByReqType',
    KPI31: '/getAllSLAReminderIds',
    KPI_CARDS:'/counts/getTotalRequestCount'
    }, 
  DUMMY_API:'/dummy/dummyResponse',

  EXCEL:{
    DOWNLOAD_EXCEL_FOR_EXTEND:'/excel/downloadExcelForExtend',
    DOWNLOAD_EXCEL_FOR_EXTEND_MAIL:'/excel/downloadExcelForExtendInMail',
    DOWNLOAD_EXCEL:'/excel/downloadExcel',
    DOWNLOAD_EXCEL_MAIL:'/excel/downloadExcelInMail',
    DOWNLOAD_EXCEL_WITH_DATA:'excel/downloadExcelWithData',
    DOWNLOAD_EXCEL_FINANCE:'/excel/exportFinanceCostExcel',
    DOWNLOAD_EXCEL_MAT:'/excel/exportMATExcel',
    DOWNLOAD_EXCEL_SAP_REPORT:'/excel/downloadExcelForCopyFromOrgData',
  },
  ERROR_HISTORY:{
    ERROR_LOG:'data/getErrorLogFromRequestId',
    ERROR_LOG_CHILD:'data/getErrorLogFromChildRequestId',
    DOWNLOAD_EXCEL_BP_ERROR:'mass/downloadExcelForBpHavingError',
    EXCEL_ERROR_HISTORY:'excel/findExcelErrorByRequestId',
  },
  TASK_ACTION_DETAIL: {
    FETCH_TASK: 'activitylog/fetchTaskActionDetail',
    FETCH_REQUEST_HISTORY :'activitylog/fetchRequestHistory',
    FETCH_DETAILS_WORKFLOW : 'activitylog/fetchInitialDetailsForWorkflow',
    GET_DOCS : 'documentManagement/getDocByRequestId',
    FETCH_MAILS :'mail/fetchMails',
    TASKDETAILS_FOR_REQUESTID : 'activitylog/fetchTaskDetailsForRequestId'
  },
  ACCORDION_API :{
    WAREHOUSE: 'data/displayLimitedWarehouseData',
    PLANT: 'data/displayLimitedPlantData',
    ACCOUNTING: 'data/displayLimitedAccountingData'
  },
  HEALTH_API :{
    LAST_TWO_RESULTS : 'api/lastTwoResults'
  },
  EMAIL_CONFIG: {
    FETCH_NOTIFICATION_MODULES: '/mail/fetchNotificationModulesOnIdentifiersHana',
    FETCH_NOTIFICATION_EVENTS: '/mail/fetchNotificationEventsOnIdentifiersHana'
  },
  SYSTEM_CONFIG: {
    GET_CURRENT_SAP_SYSTEM: '/api/global/getCurrentSapSystem',
    UPDATE_SAP_SYSTEM: '/api/global/updateSapSystem',
  },
  WORK_FLOW:{
  CANCEL_WORKFLOW:'/workflow/cancelWorkflow',
  }
};
