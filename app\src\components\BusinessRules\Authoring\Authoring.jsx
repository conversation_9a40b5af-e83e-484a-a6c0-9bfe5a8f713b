import React, { useEffect, useMemo, useState } from "react";
import Box from "@mui/material/Box";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import { useSelector } from "react-redux";
import ModeEditOutlineIcon from "@mui/icons-material/ModeEditOutline";
import { destination_IDM } from "../../../destinationVariables"; 
import DecisionTable from "./DecisionTable";
import moment from "moment";
import { doAjax } from "../../Common/fetchService";
import { outermostContainer_Information,  outermostContainer, outerContainer_Information } from "../../common/commonStyles";
import { Grid, Stack, Typography } from "@mui/material";


export default function AuthoringComp() {

  const [DTdetails, setDTdetails] = useState({});
  const [showDT, setShowDT] = useState(false);
  const [DtTables, setDtTables] = useState([]);
  const token = ""
  const appId = "baaf87db-3f78-44c8-8c78-32bec835f6ff"
  const userManagementState = useSelector((state)=>state?.userManagement)
  const userData = userManagementState?.userData
  const appSettings = useSelector((state) => state.appSettings);
  const dateFormat = appSettings?.dateFormat
  const timeFormat = appSettings?.timeFormat
    useEffect(()=>{
      doAjax(
        destination_IDM + `/v1/application/hierarchy?app=${appId}&isAuthoring=true&mode=DT`,
        "get",
        hSuccess,
        hError
      );
    },[])

    const hSuccess = (data) =>{
      setDtTables(data.data[0].childs[0].childs);
      let rms = data.data[0];
      let rs = rms.childs[0];
      setDTdetails({ ...DTdetails, RMSid: rms.id, RSid: rs.id });
    }

    let hError = (err) => { 
      console.log("Error Fetching Filter Data in this API", err); 

    };


  const handleRowClick = (row) => {
    setDTdetails({
      userDetails: {
        displayName:userData?.displayName,
        emailId: userData?.emailId,
      },
      RMSid: DTdetails?.RMSid,
      RSid: DTdetails?.RSid,
      DTid: row.id,
      applicationId: appId,
      ruleName: row.name,
      version: row.version,
      token: token.replace("Bearer ", "")
    });
    setShowDT(true);
  };
  console.log(moment(1702891324188).format(`${dateFormat},${timeFormat}`))
  return(
    <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
    <Stack spacing={1}>
      {/* Information */}
      {/* <Grid container sx={outermostContainer_Information}> */}
        <Grid item md={5} sx={outerContainer_Information}>
          <Typography variant="h3">
            <strong>Authoring</strong>
          </Typography>
          <Typography variant="body2" color="#777">
          This view displays the list of rules set for the application
          </Typography>
        </Grid>
    <Box className='content' sx={{margin:"25px"}}>    
        {!showDT ? (
        <TableContainer component={Paper} sx={{boxShadow:"none",border:"1px solid #e0e0e0"}}>
          <Table sx={{ minWidth: 650 }} aria-label="simple table">
            <TableHead sx={{background:"#f5f5f5"}}>
              <TableRow
                sx={{ 
                  '& .MuiTableCell-root':{
                    height: "52px",
                    padding: "0px 16px",
                    fontWeight:600
                  }
                }}
              >
                <TableCell>Decision Table Name</TableCell>
                <TableCell align="right">Version</TableCell>
                <TableCell align="right">Modified By</TableCell>
                <TableCell align="right">Modified On</TableCell>
                <TableCell align="right">Status</TableCell>
                <TableCell align="right">Action</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {DtTables.map((row) => {
                return (
                  <TableRow
                    key={row.id}
                    sx={{ 
                      "&:last-child td, &:last-child th": { border: 0 }, 
                      cursor: "pointer" ,
                      '& .MuiTableCell-root':{
                        borderBottom:"1px solid #e0e0e0 !important",
                        height: "52px",
                        padding: "0px 16px"
                      }
                    }}
                    onClick={() => handleRowClick(row)}
                    >
                    <TableCell component="th" scope="row">
                      {row.name}
                    </TableCell>
                    <TableCell align="right">{row.version}</TableCell>
                    <TableCell align="right">{row.updatedBy}</TableCell>
                    <TableCell align="right">{moment(row.updatedOn).format(`${dateFormat},${timeFormat}`)}</TableCell>
                    <TableCell align="right">{row.status}</TableCell>
                    <TableCell align="right" >{<ModeEditOutlineIcon onClick={() => {handleRowClick(row)}} />}</TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
        ) : (
        DTdetails && 
       
        <DecisionTable DTdetails={DTdetails} setDTdetails={setDTdetails} setShowDT={setShowDT} />
      )}
    </Box>
    {/* </Grid> */}
    </Stack>
    </div>
  )
}