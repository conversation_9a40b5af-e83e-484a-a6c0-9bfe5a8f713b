import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Autocomplete,
  Box,
  Button,
  FormControl,
  Grid,
  FormControlLabel,
  Checkbox,
  FormGroup,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import React from "react";
import {
  destination_Dashboard,
} from "../../destinationVariables";
import {
  button_Outlined,
  button_Primary,
  font_Small,
  iconButton_SpacingSmall,
  icon_MarginLeft,
} from "../common/commonStyles";
import DateRange from "../common/DateRangePicker";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useState } from "react";
import { useEffect } from "react";
import { destination_MaterialMgmt, destination_Po } from "../../destinationVariables";
import { useDispatch, useSelector } from "react-redux";
import { commonFilterClear, commonFilterUpdate } from "../../app/commonFilterSlice";
import FilterField from "../common/ReusableFilterBox/FilterField";
import { doAjax } from "../common/fetchService";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import AutoCompleteSimpleDropDown from "@components/Common/ui/dropdown/AutoCompleteSimpleDropDown";

const FilterDashboard = ({handleSearch, company, supplier,clearFunction}) => {
  const rbSearchForm = useSelector(
    (state) => state.commonFilter["RequestBench"]
  );
  const dashboardSearchForm = useSelector(
    (state) => state.commonFilter["Dashboard"]
    );
  const [companyCodeSet, setCompanyCodeSet] = useState({});
  const [vendorDetailsSet, setVendorDetailsSet] = useState({});
  const [requestTypeOptions,setRequestTypeOptions]=useState([])
  const [requestStatusOptions,setRequestStatusOptions]=useState([])
  const [userIdOptions,setuserIdOptions]=useState([])
  const [requestType,setRequestType]=useState([])
  const [requestStatus,setRequestStatus]=useState([])
  const [userIdName,setUserIdName]=useState([])
  const [selectedValue, setSelectedValue] = useState("defaultOption")
  const [moduleName,setModuleName]=useState([])
  const [regionName,setRegionName]=useState([])
  const [dateTime,setDateTime]=useState([...dashboardSearchForm.dashboardDate])
  let masterData = useSelector((state) => state.masterData);
  const dummy = [];
  
//WILL UNCOMMENT LATER
//   useEffect(() => {
//     getRequestType();
//     getRequestStatus();
//     getUserIds()

// }, []);

  useEffect(() => {
    var RegName = regionName
    .map((item) => item)
    .join("$^$");

      let tempFilterData = {
        ...rbSearchForm,
        selectedRegion: RegName,
      };
      dispatch(
        commonFilterUpdate({
          module: "Dashboard",
          filterData: tempFilterData,
        })
      );
  },[regionName])

  useEffect(()=>{
    let tempFilterData = ''
    tempFilterData = {
        ...dashboardSearchForm,
        dashBoardModuleName: moduleName,
    };
    dispatch(
        commonFilterUpdate({
            module: "Dashboard",
            filterData: tempFilterData,
        })
    );
  },[moduleName])

  useEffect(()=>{
    let userIdArr = []
      userIdName?.map((itemData) => {
        userIdArr.push(itemData?.code)
      })

      dispatch(
        commonFilterUpdate({
          module: "Dashboard",
          filterData: {
            ...dashboardSearchForm,
            selectedusersId: userIdArr,
          },
        })
      );

  },[userIdName])

  useEffect(()=>{
    let reqStatusArr = []
      requestStatus?.map((itemData) => {
        reqStatusArr.push(itemData?.lable)
      })

      dispatch(
        commonFilterUpdate({
          module: "Dashboard",
          filterData: {
            ...dashboardSearchForm,
            selectedRequestStatus: reqStatusArr,
          },
        })
      );

  },[requestStatus])

  useEffect(()=>{
    let reqTypeArr = []
      requestType?.map((itemData) => {
        reqTypeArr.push(itemData?.lable)
      })
      dispatch(
        commonFilterUpdate({
          module: "Dashboard",
          filterData: {
            ...dashboardSearchForm,
            selectedRequestType: reqTypeArr,
          },
        })
      );
  },[requestType])

const presentDate = new Date();
const backDate = new Date();
backDate.setDate(backDate.getDate() - 7);
  
const dispatch = useDispatch();
let userData = useSelector((state) => state.userManagement.userData);

//const dbSearchForm = useSelector((state) => state.commonFilter["CostCenter"]);


  const [date, setDate] = useState([...dashboardSearchForm.dashboardDate]);
  const appSettings=useSelector((state)=> state.appSettings)

  const handleDate = (e) => {
    const tempdate=e;

    dispatch(
      commonFilterUpdate({
        module: "Dashboard",
        filterData: {
          ...dashboardSearchForm,
          dashboardDate: tempdate,
        },
      })
    );
  };

  useEffect(() => {
    if (dashboardSearchForm?.dashboardDate) {
      const presentDate = new Date(dashboardSearchForm?.dashboardDate[0]);
      const backDate = new Date(dashboardSearchForm?.dashboardDate[1]);
      setDateTime([presentDate,backDate]);
    }
  },[dashboardSearchForm?.dashboardDate])

  const moduleFilterData = [
    {
      type: "singleSelect",
      filterName: "companyCode",
      filterData: companyCodeSet,
      filterTitle: "Company Code",
    },
    {
      type: "singleSelect",
      filterName: "vendorNo",
      filterData: vendorDetailsSet,
      filterTitle: "Business Partner",
    },
    {
      type: "dateRange",
      filterName: "dashboardDate",
      filterTitle: "Date Range",
    },
  ];
  const moduleOptions=[
    'Material',
    'Profit Center',
    'Cost Center',
    'General Ledger',
  ]

  const regionOptions=[
     'US',
      'EUR'
  ]
  const handleSelectAllRegion = () => {
    if (regionName.length === regionOptions?.length) {
      setRegionName([]);
     
    } else {
      setRegionName(regionOptions);
    }
  };

  return (
    <>
      <Grid item md={12}>
        <Accordion
          defaultExpanded={false}
          sx={{
            marginTop: "0px !important",
            border: "1px solid",
            borderColor: "#E0E0E0",
            "&:not(:last-child)": {
              borderBottom: 0,
            },
            "&:before": {
              display: "none",
            },
          }}
        >
          <AccordionSummary
            expandIcon={<ExpandMoreIcon sx={{ ...iconButton_SpacingSmall }} />}
            aria-controls="panel1a-content"
            id="panel1a-header"
            sx={{
              minHeight: "2rem !important",
              margin: "0px !important",
              color: "#1D1D1D",
            }}
          >
            <Typography
              sx={{
                fontWeight: "700",
              }}
            >
              Filter Dashboard
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Grid
              container
              rowSpacing={1}
              spacing={2}
            >

              {/* WILL BE UNCOMMENT LATER */}
              <Grid item md={2}>
                <Typography sx={font_Small}>
                  Module
                </Typography>
                <AutoCompleteSimpleDropDown
                    options={[
                      ...moduleOptions
                        .filter((name) => name !== "Select All")
                        .sort((a, b) => a.localeCompare(b))
                    ]}
                    value={moduleName}
                    onChange={(value) => {
                      if (
                        value.length > 0 &&
                        value[value.length - 1]?.label ===
                          "Select All"
                      ) {
                        handleSelectAllModule();
                      } else {
                        setModuleName(value);
                      }
                    }}
                    placeholder="Select Module Name"
                  />
              </Grid>

              <Grid item md={2}>
                <Typography sx={font_Small}>
                  Region
                </Typography>
                <AutoCompleteSimpleDropDown
                    options={[
                      ...regionOptions
                        .filter((name) => name !== "Select All")
                        .sort((a, b) => a.localeCompare(b))
                    ]}
                    value={regionName}
                    onChange={(value) => {
                      if (
                        value.length > 0 &&
                        value[value.length - 1]?.label ===
                          "Select All"
                      ) {
                        handleSelectAllRegion();
                      } else {
                        setRegionName(value);
                      }
                    }}
                    placeholder="Select Region"
                  />
              </Grid>

              
              
              <Grid item md={2}>
                <Typography sx={font_Small}>Date Range</Typography>
                <FormControl
                  fullWidth
                  sx={{ padding: 0, height: "37px" }}
                >
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DateRange
                      handleDate={handleDate}
                      cleanDate = {false}
                      date={dateTime}
                    />
                  </LocalizationProvider>
                </FormControl>
              </Grid>
              {moduleFilterData?.map((filter) => filter?.hideFilter ? (<></>) : (
                  <Grid item md={3} key={filter.filterTitle}>
                    <FilterField
                      type={filter.type}
                      filterName={filter.filterName}
                      filterData={filter.filterData}
                      moduleName={"Dashboard"}
                      onChangeFilter={filter.onChangeFilter}
                      filterTitle={filter.filterTitle}
                    />
                  </Grid>
                ))}
            </Grid>
            <Grid
              container
              style={{
                display: "flex",
                justifyContent: "flex-end",
              }}
            >
              <Grid
                item
                style={{
                  display: "flex",
                  justifyContent: "space-around",
                }}
              >
                <Button
                  variant="outlined"
                  sx={{ ...button_Outlined}}
                  onClick={() =>{
                    setDateTime([backDate,presentDate]);
                    setRequestStatus([])
                    setRequestType([]);
                    setRegionName([])
                    setUserIdName([]);
                    setModuleName([]);
                    clearFunction();
                    dispatch(commonFilterClear({ module: "Dashboard",days:appSettings.range }))
                    dispatch(
                      commonFilterUpdate({
                        module: "Dashboard",
                        filterData: {
                          ...dashboardSearchForm,
                          selectedRequestType: [],
                          selectedRequestStatus:[],
                          selectedusersId:[],
                          selectedRegion:"",
                          dashboardDate: [ backDate,presentDate],
                        },
                      })
                    );
                  
                  }
                  }
                >
                  Clear
                </Button>

                <Button
                  variant="contained"
                  sx={{ ...button_Primary, ...icon_MarginLeft }}
                  onClick={handleSearch}
                >
                  Apply
                </Button>
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>
      </Grid>
    </>
  );
};

export default FilterDashboard;
