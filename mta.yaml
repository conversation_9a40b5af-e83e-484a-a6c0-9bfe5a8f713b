ID: mdgdev
_schema-version: "3.1"
parameters:
  deploy_mode: html5-repo
version: 1.0.0
modules:
  - name: cw-mdg-dev
    type: approuter.nodejs
    path: approuter
    properties:
      SEND_XFRAMEOPTIONS: false
    parameters:
      disk-quota: 256M
      memory: 256M
    requires:
      - name: mdg_html5_repo_runtime
      - name: cw-mdg-xsuaa
      - name: cw-mdg-services-dev
      - name: cw-mdg-usernode-api-dev
        group: destinations
        properties:
          name: cw-mdg-usernode-api-dev
          url: '~{url}'
          forwardAuthToken: true
  - name: cw-mdg-uideployer-dev
    type: com.sap.html5.application-content
    path: html5Deployer
    parameters:
        config:
            sizeLimit: 16
    requires:
      - name: mdg_html5_repo_host
    build-parameters:
      requires:
        - name: mdgdev
          artifacts:
            - "./*"
          target-path: resources/app
  - name: mdgdev
    type: html5
    path: app
    build-parameters:
      builder: custom
      commands:
        - npm install -f
      supported-platforms: []
      build-result: dist
  - name: cw-mdg-usernode-dev
    type: nodejs
    path: usernode
    parameters:
      disk-quota: 256M
      memory: 128M
    provides:
      - name: cw-mdg-usernode-api-dev
        properties:
          url: "${default-url}"
    properties:
      UAA_SERVICE_NAME: cw-mdg-xsuaa
    requires:
      - name: cw-mdg-xsuaa
resources:
  - name: mdg_html5_repo_runtime
    parameters:
      service-plan: app-runtime
      service: html5-apps-repo
    type: org.cloudfoundry.managed-service
  - name: mdg_html5_repo_host
    parameters:
      service-plan: app-host
      service: html5-apps-repo
      config:
        sizeLimit: 100
    type: org.cloudfoundry.managed-service
  - name: cw-mdg-xsuaa
    parameters:
      path: ./xs-security.json
      service-plan: application
      service: xsuaa
    type: org.cloudfoundry.managed-service
  - name: cw-mdg-services-dev
    parameters:
      service-plan: lite
      service: destination
    type: org.cloudfoundry.managed-service
