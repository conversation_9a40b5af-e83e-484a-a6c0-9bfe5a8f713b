import {
  <PERSON><PERSON>,
  IconButton,
  <PERSON><PERSON>,
  <PERSON>po<PERSON>,
  Box,
  Card,
  InputLabel,
  FormControl,
  Select,
  TextField,
  Button,
  BottomNavigation,
  Paper,
  MenuItem,
  Chip,
  Tooltip,
  Modal,
} from "@mui/material";
import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  font_Small,
  iconButton_SpacingSmall,
  outermostContainer_Information,
} from "../common/commonStyles";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
// import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { DateTimePicker, LocalizationProvider, MobileDateTimePicker } from "@mui/x-date-pickers";
import FileUploadOutlinedIcon from "@mui/icons-material/FileUploadOutlined";
import axios from "axios";
import { destination_Admin } from "../../destinationVariables";
import moment from "moment";
import { useSelector } from "react-redux";

import ReactPlayer from "react-player/lazy";
import { MatView } from "../DocumentManagement/UtilDoc";
import { ViewDetailsIcon } from "../../Common/icons.jsx";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { doAjax } from "../Common/fetchService";
const ViewBroadcast = () => {
  let { BroadcastID } = useParams();
  const source = `/${destination_Admin}/broadcastManagement/showBroadcastById/${BroadcastID}`;
  let userData = useSelector((state) => state.userManagement.userData);
  const presentDate = new Date();
  const futureDate = new Date();
  futureDate.setDate(presentDate.getDate() + 7);
  const navigate = useNavigate();
  const [BroadcastForm, setBroadcastForm] = useState({
    title: "",
    description: "",
    category: "",
    startDate: presentDate,
    endDate: futureDate,
    supplier: "",
    file: "",
    status: "",
    link: "",
  });
  const [TitleLength, setTitleLength] = useState(0);
  const [DescLength, setDescLength] = useState(0);
  const [videoPlay, setvideoPlay] = useState("");
  const [items, setItems] = useState([]);
  const [temp, settemp] = useState("");
  const [open, setOpen] = React.useState(false);
  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);
  const handleForm = (e) => {
    setBroadcastForm({ ...BroadcastForm, [e.target.name]: e.target.value });
    if (e.target.name == "title") {
      setTitleLength(e.target.value.length);
    }
    if (e.target.name == "description") {
      setDescLength(e.target.value.length);
    }
    if (e.target.name == "files") {
      setBroadcastForm({ ...BroadcastForm, [e.target.name]: e.target.files });
    }
  };
  const handleDate = (e) => {
    setBroadcastForm({ ...BroadcastForm, startDate: e });
  };
  const handleDate2 = (e) => {
    setBroadcastForm({ ...BroadcastForm, endDate: e });
  };

  const style = {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",

    bgcolor: "background.paper",
    border: "2px solid #000",

    p: 4,
  };
  const handleOpenView = () => {
    let tempArr = [];
    let hSuccess=() => {
      handleOpen();
    }
    let hError=()=>{}
    doAjax(`/${destination_Admin}/broadcastManagement/showBroadcastById/${BroadcastID}`,'get',hSuccess,hError)
     
    
  };
  const getDetails = () => {
    let hSuccess=(data) => {
      setBroadcastForm({
        title: data.broadcastDetailsDto.broadcastTitle,
        description: data.broadcastDetailsDto.description,
        category: data.broadcastDetailsDto.broadcastCategory,
        startDate: data.broadcastDetailsDto.createdDate,
        endDate: data.broadcastDetailsDto.endDate,
        supplier: data.broadcastDetailsDto.supplierCategory,
        file: data.broadcastDetailsDto.fileName,
        status: data.broadcastDetailsDto.status,
        link: data.broadcastDetailsDto.externalUrl,
      });
    }
    let hError=()=>{}
   doAjax(
      `/${destination_Admin}/broadcastManagement/getBroadcastDetailsById/${BroadcastID}`,'get',hSuccess,hError
    )
      
     
  };

  const publish = () => {
    const formData = new FormData();
    if (BroadcastForm.files) {
      [...BroadcastForm.files].forEach((item) =>
        formData.append("files", item)
      );
    }

    var doc = JSON.stringify({
      broadcastCategory: BroadcastForm.category,
      broadcastTitle: BroadcastForm.title,
      createdBy: userData.displayName,

      startDate: moment(BroadcastForm.startDate).format(
        "YYYY-MM-DD HH:mm:ss.000"
      ),
      endDate: moment(BroadcastForm.endDate).format("YYYY-MM-DD HH:mm:ss.000"),
      description: BroadcastForm.description,
      supplierCategory: BroadcastForm.supplier,
      createdDate: moment(presentDate).format("YYYY-MM-DD HH:mm:ss.000"),
    });
    formData.append("broadcastDetails", doc);
    let hSuccess=(data) => {
      console.log(data);
    }
    let hError=()=>{}
    doAjax(`/${destination_Admin}/broadcastManagement/uploadFiles`,'postformdata',hSuccess,hError, formData)
     
 
  };
  const draft = () => {
    const formData = new FormData();
    if (BroadcastForm.files) {
      [...BroadcastForm.files].forEach((item) =>
        formData.append("files", item)
      );
    }

    var doc = JSON.stringify({
      broadcastCategory: BroadcastForm.category,
      broadcastTitle: BroadcastForm.title,
      createdBy: userData.displayName,

      startDate: moment(BroadcastForm.startDate).format(
        "YYYY-MM-DD HH:mm:ss.000"
      ),
      endDate: moment(BroadcastForm.endDate).format("YYYY-MM-DD HH:mm:ss.000"),
      description: BroadcastForm.description,
      supplierCategory: BroadcastForm.supplier,
      createdDate: moment(presentDate).format("YYYY-MM-DD HH:mm:ss.000"),
      status: "Draft",
    });
    formData.append("broadcastDetails", doc);
    let hSuccess=(data) => {
      console.log(data);
    }
    let hError=()=>{}
    doAjax(`/${destination_Admin}/broadcastManagement/uploadFiles`,'postformdata',hSuccess,hError, formData)
    
      
  };

  useEffect(() => {
    getDetails();
  }, []);

  return (
    <>
      <div style={{ padding: "0 1rem 0 1rem" }}>
        <Modal
          open={open}
          onClose={handleClose}
          aria-labelledby="modal-modal-title"
          aria-describedby="modal-modal-description"
        >
          <Box sx={style}>
            <ReactPlayer url={source} controls />
          </Box>
        </Modal>
        <Grid container sx={outermostContainer_Information}>
          <Grid container>
            <Grid item md={7} style={{ padding: "16px", paddingLeft: "" }}>
              <Stack direction="row">
                <IconButton
                  onClick={() => navigate("/configCockpit/broadcastManagement")}
                  color="primary"
                  aria-label="upload picture"
                  component="label"
                >
                  <ArrowCircleLeftOutlinedIcon
                    sx={{
                      fontSize: "25px",
                      color: "#000000",
                    }}
                  />
                </IconButton>
                <Box>
                  <Typography variant="h5" paddingTop="0.3rem" fontSize="20px">
                    <strong>Broadcast Details : {BroadcastID}</strong>
                  </Typography>

                  <Typography variant="body2" color="#777" fontSize="12px">
                    This view displays the details of the Broadcast and allows
                    user to edit some info
                  </Typography>
                </Box>
              </Stack>
            </Grid>
          </Grid>
        </Grid>
        <Card sx={{ padding: "25px" }}>
          <Grid container rowSpacing={2} columnSpacing={2}>
            <Grid item xs={3}>
              <Box sx={{ minWidth: 140 }}>
                <Typography sx={font_Small} fontWeight="normal">
                  Broadcast Category
                  <span style={{ color: "red" }}>*</span>
                </Typography>
                <FormControl fullWidth size="small" sx={{ margin: ".5em 0px" }}>
                  <Select
                    disabled
                    placeholder="Select Broadcast Category"
                    name="category"
                    onChange={handleForm}
                    value={BroadcastForm.category}
                    displayEmpty={true}
                    sx={font_Small}
                    renderValue={() =>
                      BroadcastForm.category !== "" ? (
                        BroadcastForm.category
                      ) : (
                        <div
                          className="placeholderstyle"
                          style={{ color: "#C1C1C1" }}
                        >
                          Select Broadcast Category{" "}
                        </div>
                      )
                    }
                  >
                    <MenuItem sx={font_Small} value={""}>
                      <div style={{ color: "#C1C1C1" }}>
                        Select Broadcast Category{" "}
                      </div>
                    </MenuItem>
                    <MenuItem sx={font_Small} value={"Announcements"}>
                      Announcements
                    </MenuItem>
                    <MenuItem sx={font_Small} value={"Videos"}>
                      Videos
                    </MenuItem>
                    <MenuItem sx={font_Small} value={"Events"}>
                      Events
                    </MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </Grid>
            <Grid item xs={3}>
              <Box>
                <Typography sx={font_Small} fontWeight="normal">
                  Start Date & Time
                  <span style={{ color: "red" }}>*</span>
                </Typography>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <MobileDateTimePicker
                    disabled
                    inputFormat="dd MMM yyyy & HH:mm"
                    value={BroadcastForm.startDate}
                    name="startDate"
                    onChange={(e) => handleDate(e)}
                    renderInput={(params) => (
                      <TextField
                        fullWidth
                        size="small"
                        {...params}
                        sx={{ margin: ".5em 0px" }}
                      />
                    )}
                  />
                </LocalizationProvider>
              </Box>
            </Grid>

            <Grid item xs={3}>
              <Box>
                <Typography sx={font_Small} fontWeight="normal">
                  End Date & Time
                  <span style={{ color: "red" }}>*</span>
                </Typography>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <MobileDateTimePicker
                    disabled
                    inputFormat="dd MMM yyyy & HH:mm"
                    value={BroadcastForm.endDate}
                    name="endDate"
                    onChange={(e) => handleDate2(e)}
                    renderInput={(params) => (
                      <TextField
                        fullWidth
                        size="small"
                        {...params}
                        sx={{
                          margin: ".5em 0px",
                        }}
                      />
                    )}
                  />
                </LocalizationProvider>
              </Box>
            </Grid>
            <Grid item xs={3}>
              <Box sx={{ minWidth: 140 }}>
                <Typography sx={font_Small} fontWeight="normal">
                  Broadcast Status
                </Typography>
                <Chip
                  sx={{
                    margin: ".5em 0px",

                    justifyContent: "flex-start",
                    fontSize: "14px",
                    borderRadius: "4px",
                    color: "#000",

                    minWidth: "130px",
                    backgroundColor:
                      BroadcastForm.status === "Active"
                        ? "#cdefd6"
                        : BroadcastForm.status === "Draft"
                        ? "#FFC88787"
                        : BroadcastForm.status === "Archived"
                        ? "#FAFFC0"
                        : "#cddcef",
                  }}
                  label={BroadcastForm.status}
                />
              </Box>
            </Grid>
            <Grid item xs={12}>
              <Box sx={{ minWidth: 120 }}>
                <Typography sx={font_Small} fontWeight="normal">
                  Broadcast Title
                  <span style={{ color: "red" }}>*</span>
                  <span style={{ color: "#C1C1C1", fontSize: "12px" }}>
                    {" "}
                    (Max length 100 characters)
                  </span>
                </Typography>
                <TextField
                  sx={{ margin: ".5em 0px" }}
                  disabled
                  fullWidth
                  placeholder="Enter Broadcast Title"
                  variant="outlined"
                  size="small"
                  name="title"
                  onChange={handleForm}
                  value={BroadcastForm.title}
                  inputProps={{ maxLength: 100 }}
                ></TextField>
                <Typography
                  variant="caption"
                  ml={1}
                  sx={{ display: "flex", justifyContent: "flex-start" }}
                >
                  {BroadcastForm.title.length}/100
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ minWidth: 120 }}>
                <Typography sx={font_Small} fontWeight="normal">
                  Broadcast Description
                  <span style={{ color: "red" }}>*</span>
                  <span style={{ color: "#C1C1C1", fontSize: "12px" }}>
                    {" "}
                    (Max length 300 characters)
                  </span>
                </Typography>
                <TextField
                  sx={{ margin: ".5em 0px" }}
                  disabled
                  fullWidth
                  multiline
                  rows={4}
                  placeholder="Enter Broadcast Description"
                  variant="outlined"
                  size="small"
                  name="description"
                  inputProps={{ maxLength: 300 }}
                  onChange={handleForm}
                  value={BroadcastForm.description}
                ></TextField>
                <Typography
                  variant="caption"
                  ml={1}
                  sx={{ display: "flex", justifyContent: "flex-start" }}
                >
                  {BroadcastForm.description.length}/300
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={3}>
              <Box sx={{ marginTop: "0rem" }}>
                <label htmlFor="file">
                  <Typography>Document</Typography>
                </label>

                <Typography sx={{ margin: ".5em 0px" }}>
                  {BroadcastForm.file == null ? "No File" : BroadcastForm.file}
                  {BroadcastForm.category == "Videos" ||
                  BroadcastForm.file == null ? (
                    <IconButton
                      sx={{ ...iconButton_SpacingSmall }}
                      onClick={handleOpenView}
                    >
                      {BroadcastForm.file != null && (
                        <Tooltip title="View">{ViewDetailsIcon}</Tooltip>
                      )}
                    </IconButton>
                  ) : (
                    <MatView
                      index={BroadcastID}
                      name={BroadcastForm.file}
                      isBroadcast={true}
                    />
                  )}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={4}>
            {(BroadcastForm.link=="") ? <><Typography>Link</Typography>  <Typography
                sx={{
                  margin: ".5em 0px",
                  textOverflow: "ellipsis",
                  overflow: "hidden",
                  whiteSpace: "noWrap",
                }}
                width="50%"
              >
                No Link
              </Typography></>: <><Typography>Link</Typography>
             <Typography
                sx={{
                  margin: ".5em 0px",
                  textOverflow: "ellipsis",
                  overflow: "hidden",
                  whiteSpace: "noWrap",
                }}
                width="50%"
              >
                <a href={BroadcastForm.link}></a>
                {BroadcastForm.link}
              </Typography></>}
            </Grid>

            <Grid item xs={12}>
              <Grid container sx={{ alignItems: "center", marginTop: "5px" }}>
                <Grid item xs={1}>
                  <hr />
                </Grid>
                <Grid item xs={2}>
                  <Typography variant="body2" color="#777" align="center">
                    New Broadcast Applicable for{" "}
                  </Typography>
                </Grid>
                <Grid item xs={9}>
                  <hr />
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={2.5}>
              <Box sx={{ minWidth: 140 }}>
                <Typography sx={font_Small} fontWeight="normal">
                  Supplier Category
                  <span style={{ color: "red" }}>*</span>
                </Typography>
                <FormControl
                  fullWidth
                  size="small"
                  disabled
                  sx={{ margin: ".5em 0px" }}
                >
                  <InputLabel
                    id="demo-multiple-checkbox-label"
                    shrink={false}
                    sx={{
                      color: "#C1C1C1",
                      fontSize: "14px",
                      fontWeight: 400,
                    }}
                  >
                    {true && "Select Supplier Category"}
                  </InputLabel>
                  <Select
                    disabled
                    placeholder="Select Supplier Category"
                    displayEmpty={true}
                    sx={font_Small}
                    multiple
                  ></Select>
                </FormControl>
              </Box>
            </Grid>
          </Grid>
        </Card>
      </div>
    </>
  );
};

export default ViewBroadcast;
