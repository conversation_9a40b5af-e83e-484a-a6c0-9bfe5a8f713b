import StorageIcon from '@mui/icons-material/Storage';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import AssignmentIcon from '@mui/icons-material/Assignment';
import DescriptionIcon from '@mui/icons-material/Description';
import SettingsIcon from '@mui/icons-material/Settings';
import ShowChartIcon from '@mui/icons-material/ShowChart';
import DataObjectIcon from '@mui/icons-material/DataObject';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ManIcon from '@mui/icons-material/Man';

const getIconComponent = (taskName) => {
  if (taskName.includes('Storage')) return <StorageIcon />;
  if (taskName.includes('Buyer')) return <ShoppingCartIcon />;
  if (taskName.includes('Planner')) return <AssignmentIcon />;
  if (taskName.includes('Regulatory')) return <DescriptionIcon />;
  if (taskName.includes('Product')) return <SettingsIcon />;
  if (taskName.includes('Plant')) return <ShowChartIcon />;
  if (taskName.includes('Data')) return <DataObjectIcon />;
  if (taskName.includes('Requestor')) return <PersonAddIcon />;
  if (taskName.includes('Steward')) return <CheckCircleIcon />;
  if (taskName.includes('Manager')) return <ManIcon />;
  return <AssignmentIcon />;
};

const getStatusColor = (status) => {
  switch (status) {
    case 'In Progress':
      return '#1976d2'; // blue
    case 'Completed':
      return '#43a047'; // green
    case 'Cancelled':
      return '#e53935'; // red
    default:
      return '#bdbdbd'; // grey
  }
};

const Task = ({ id, task, onClick }) => {
  const statusColor = getStatusColor(task.status);
  return (
    <div
      id={id}
      onClick={onClick}
      className='task'
      style={{
        borderRadius: 12,
        marginBottom: 8,
        background: '#fff',
        boxShadow: "0 4px 12px rgba(0, 0, 0, 0.08)",
        cursor: 'pointer',
        transition: "transform 0.1s ease",
        maxWidth: 230,
        padding: 8,
        borderLeft: `4px solid ${statusColor}`,
        position: 'relative',
      }}
    >
      {/* Status circle */}
      <div
        style={{
          position: 'absolute',
          left: -12,
          top: 12,
          width: 16,
          height: 16,
          borderRadius: '50%',
          background: '#fff',
          border: `3px solid ${statusColor}`,
          boxSizing: 'border-box',
          zIndex: 2,
        }}
      />
      <div style={{ display: 'flex', alignItems: 'start', gap: 8, paddingTop:"16px", paddingBottom:"16px" }}>
        <div style={{ color: '#1976d2', fontSize: 24,display: 'flex', alignItems: 'center', }}>
          {getIconComponent(task.name)}
        </div>
        <div style={{ fontWeight: 600 }}>{task.name}</div>
        <div style={{ marginLeft: 'auto', color: '#3498db', fontSize: 14, background: "#e1f0fa",paddingLeft:'10px',paddingRight:"10px",borderRadius:'20px' }}>{task.sla}d</div>
      </div>
    </div>
  );
};

export default Task;
