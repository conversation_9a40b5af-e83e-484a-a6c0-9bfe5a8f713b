import { useEffect, useState } from "react";
import { <PERSON>, Button, Grid, <PERSON>po<PERSON>, <PERSON><PERSON>, Step, StepButton, Icon<PERSON>utton, DialogContent, DialogActions } from "@mui/material";
import RequestHeader from "./RequestPages/RequestHeader";
import { doAjax } from "../Common/fetchService";
import { destination_IDM, destination_MaterialMgmt } from "../../destinationVariables";
import { useDispatch, useSelector } from "react-redux";
import RequestDetails from "./RequestPages/RequestDetails";
import { usePDF } from 'react-to-pdf';
import RequestDetailsForExtend from "./RequestPages/RequestDetailsForExtend";
import { clearTemplateArray, clearChangeLogData, setDataLoading, setDisplayPayload, setChangeFieldRows, setChangeFieldRowsDisplay, setDynamicKeyValue, resetPayloadData, setMatlNoData, setRequestorPayload, clearDynamicKeyValue, updateSelectedRows, setPlantData, setFCRows } from "../../app/payloadslice";
import { updateCurrentCount, updateTotalCount, updateNextButtonStatus } from "../../app/paginationSlice";
import { idGenerator, transformApiResponseToReduxPayload } from "../../functions";
import AttachmentsCommentsTab from "./RequestPages/AttachmentsCommentsTab";
import { setMaterialRows, setRequestHeader, setTabValue } from "../../app/requestDataSlice";
import { useLocation, useNavigate } from "react-router-dom";
import AddIcon from "@mui/icons-material/Add";
import CloudDownloadIcon from '@mui/icons-material/CloudDownload';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import Divider from '@mui/material/Divider';
import PictureAsPdfIcon from "@mui/icons-material/PictureAsPdf";
import Paper from '@mui/material/Paper';
import PreviewPage from "./PreviewPage";
import ArrowCircleLeftOutlined from "@mui/icons-material/ArrowCircleLeftOutlined";
import SummarizeOutlinedIcon from "@mui/icons-material/SummarizeOutlined";
import PermIdentityOutlinedIcon from "@mui/icons-material/PermIdentityOutlined";
import FeedOutlinedIcon from "@mui/icons-material/FeedOutlined";
import RequestDetailsForChange from "./RequestPages/RequestDetailsForChange";
import useMaterialRequestHeaderConfig from "@hooks/useMaterialRequestHeaderConfig";
import useMaterialChangeFieldConfig from "@hooks/useMaterialChangeFieldConfig";
import useChangeMaterialRows from "@hooks/useChangeMaterialRows";
import AttachmentUploadDialog from "@components/Common/AttachmentUploadDialog";
import TrackChangesTwoToneIcon from "@mui/icons-material/TrackChangesTwoTone";
import FileUploadOutlinedIcon from "@mui/icons-material/FileUploadOutlined";
import ReusableDialog from "@components/Common/ReusableDialog";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { appendPrefixByJavaKey, clearLocalStorageItem, convertKeysName, getLocalStorage, } from "@helper/helper";
import ChangeLog from "@components/Changelog/ChangeLog";
import useGenericDtCall from "@hooks/useGenericDtCall";
import { DECISION_TABLE_NAME, DIALOUGE_BOX_MESSAGES, ENABLE_STATUSES, LOADING_MESSAGE, REGION, DIVERSION_CONTROL_FLAG, REQUEST_STATUS, REQUEST_TYPE, LOCAL_STORAGE_KEYS, API_CODE } from "@constant/enum";
import { setDropDown } from "@app/dropDownDataSlice";
import useChangePayloadCreation from "@hooks/useChangePayloadCreation";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";
import { changeTemplateDT, clearMaterialFieldConfig, updateAllTabsData } from "@app/tabsDetailsSlice";
import { clearCreateChangeLogData, clearCreateTemplateArray } from "@app/changeLogReducer";
import CreateChangeLog from "../createChangeLog/CreateChangeLog";
import useLogger from "@hooks/useLogger";
import useFinanceCostingRows from "@hooks/useFinanceCostingRows";
import { ERROR_MESSAGES } from "../../constant/enum";
import { colors } from "@constant/colors";
import CustomDialog from "@components/Common/ui/CustomDialog";
import { WarningOutlined } from "@mui/icons-material";
import { button_Outlined, button_Primary } from "../Common/commonStyles.jsx";
import { APP_END_POINTS } from "@constant/appEndPoints";
import ExcelOperationsCard from "../Common/ExcelOperationsCard"
import RequestDetailsForFC from "./RequestPages/RequestDetailsForFC";
import { END_POINTS } from "@constant/apiEndPoints";
import PreviewChange from "./PreviewChange";
import PreviewCreate from "./RequestPages/PreviewCreate";

const CreateNewRequest = () => {
  const { toPDF, targetRef } = usePDF({ filename: 'request-preview.pdf' });
  const { customError } = useLogger();
  const [isLoading, setIsLoading] = useState(false);
  const [mandFields, setMandFields] = useState([]);
  const [downloadClicked, setDownloadClicked] = useState(false);
  const [ruleData, setRuleData] = useState([]);
  const [isSecondTabEnabled, setIsSecondTabEnabled] = useState(false);
  const [isAttachmentTabEnabled, setIsAttachmentTabEnabled] = useState(false);
  const [pcNumber, setPcNumber] = useState("");
  const [enableDocumentUpload, setEnableDocumentUpload] = useState(false);
  const [attachmentsData, setAttachmentsData] = useState([]);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [isChangeLogopen, setisChangeLogopen] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState();
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [alertType, setAlertType] = useState("success");
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [addHardCodeData, setAddHardCodeData] = useState(false);
  const [createChangeLogIsOpen, setCreateChangeLogIsOpen] = useState(false);
  const [isDialogVisible, setisDialogVisible] = useState(false);
  const dispatch = useDispatch();
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const payloadData = useSelector((state) => state.payload.payloadData);
  const requestIdHeader = useSelector((state) => state.request.requestHeader?.requestId);
  const requestType = useSelector((state) => state.request.requestHeader.requestType);
  const requestDetails = useSelector((state) => state.request.requestHeader);
  const taskData = useSelector((state) => state.userManagement?.taskData);
  const { getChangeTemplate } = useMaterialChangeFieldConfig();
  const { fetchDisplayDataRows } = useChangeMaterialRows();
  const { getDtCall, dtData } = useGenericDtCall();
  const { createFCRows } = useFinanceCostingRows();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const tabValue = useSelector((state) => state.request.tabValue);

  const { getRequestHeaderTemplate } = useMaterialRequestHeaderConfig();
  const { changePayloadForTemplate } = useChangePayloadCreation(payloadData?.TemplateName);
  //const isStepCompleted = (stepId) => completedSteps.has(stepId);
  const steps = ["Request Header", "Material List", "Attachments & Remarks","Preview"];
  const [completed, setCompleted] = useState([false]);

  const handleTabChange = (index) => {
    dispatch(setTabValue(index));
  };

  const location = useLocation();
  const rowData = location.state;
  const urlSearchParams = new URLSearchParams(location.search.split("?")[1]);
  const requestId = urlSearchParams.get("RequestId");

  const queryParams = new URLSearchParams(location.search);
  const RequestId = queryParams.get("RequestId");
  const RequestType = queryParams.get("RequestType");
  const reqBench = queryParams.get("reqBench");

  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };

  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };

  const openChangeLog = () => {
    setisChangeLogopen(true);
  };

  const handleClosemodalData = (data) => {
    setisChangeLogopen(data);
  };

  const handleOk = () => {
    if (messageDialogSeverity === "success") {
      navigate(`/requestBench`);
    } else {
      handleMessageDialogClose();
    }
  };

  const handleDownload = () => {
    setDownloadClicked(true);
  }

  const handleUploadMaterial = (file) => {
    let url = "";
    if (RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) {
      url = "getAllMaterialsFromExcel";
    } else if (RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) {
      url = "getAllMaterialsFromExcelForMassExtend";
    } else {
      url = "getAllMaterialsFromExcelForMassChange";
    }
    setLoaderMessage("Initiating Excel Upload");
    setBlurLoading(true);
    const formData = new FormData();
    [...file].forEach((item) => formData.append("files", item));
    formData.append("dtName", (RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) ? "MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG" : "MDG_MAT_CHANGE_TEMPLATE");
    formData.append("version", (RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) ? "v1" : "v4");
    formData.append("requestId", requestId ? requestId.slice(3) : "");
    formData.append("region", payloadData?.Region ? payloadData?.Region : "US");
    formData.append("matlType", "ALL");

    const hSuccess = (data) => {
      if (data?.statusCode === API_CODE.STATUS_200) {
        setEnableDocumentUpload(false);
        setBlurLoading(false);
        setLoaderMessage("");
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      } else {
        setEnableDocumentUpload(false);
        setBlurLoading(false);
        setMessageDialogMessage(data?.message);
        setLoaderMessage("");
        setAlertType("error");
        handleSnackBarOpen();
      }
    };
    const hError = (error) => {
      setBlurLoading(false);
      setMessageDialogMessage(error?.message);
      setLoaderMessage("");
      setAlertType("error");
      handleSnackBarOpen();
    };

    doAjax(`/${destination_MaterialMgmt}/massAction/${url}`, "postformdata", hSuccess, hError, formData);
  };

  const getDisplaydata = async (effectiveRequestId = null) => {
    return new Promise((resolve, reject) => {
      dispatch(setDataLoading(true))
      const idToUse = effectiveRequestId || requestId || RequestId;
      const savedTask = getLocalStorage(LOCAL_STORAGE_KEYS.CURRENT_TASK, true, {});
      const effectiveRequestType = RequestType || taskData?.ATTRIBUTE_2 || savedTask?.ATTRIBUTE_2 ;

      let payload = reqBench
        ? {
            massCreationId: !rowData?.isBifurcated ? ((effectiveRequestType === REQUEST_TYPE.CREATE || effectiveRequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) ? idToUse.slice(3) : "") : "",
            massChildCreationId: rowData?.isBifurcated ? ((effectiveRequestType === REQUEST_TYPE.CREATE || effectiveRequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) ? idToUse.slice(3) : "") : "",
            massChangeId: !rowData?.isBifurcated ? ((effectiveRequestType === REQUEST_TYPE.CHANGE || effectiveRequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD) ? idToUse.slice(3) : "") : "",
            massExtendId: !rowData?.isBifurcated ?((effectiveRequestType === REQUEST_TYPE.EXTEND || effectiveRequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) ? idToUse.slice(3) : "") : "",
            massSchedulingId: !rowData?.isBifurcated ? (effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? idToUse.slice(3): "") : "",
            screenName: effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? "" : effectiveRequestType,
            dtName: effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? "" : "MDG_MAT_MATERIAL_FIELD_CONFIG",
            version: effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? "" : "v2",
            page: 0,
            size: effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? 100 : (effectiveRequestType === REQUEST_TYPE.CHANGE || effectiveRequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD) ? 10 : 50,
            sort: "",
            ApproverGroup:taskData?.ATTRIBUTE_5,
            Region:"",
            massChildSchedulingId : rowData?.isBifurcated ? (effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? idToUse.slice(3): "") : "",
            massChildExtendId : rowData?.isBifurcated ?((effectiveRequestType === REQUEST_TYPE.EXTEND || effectiveRequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) ? idToUse.slice(3) : "") : "",
            massChildChangeId : rowData?.isBifurcated ? ((effectiveRequestType === REQUEST_TYPE.CHANGE || effectiveRequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD) ? idToUse.slice(3) : "") : "",
          }
        : {
            massCreationId: "",
            massChildCreationId : effectiveRequestType === "MASS_CREATE" || effectiveRequestType === "Mass Create" || effectiveRequestType === REQUEST_TYPE.CREATE || effectiveRequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || effectiveRequestType == REQUEST_TYPE.EXTEND_WITH_UPLOAD? idToUse.slice(3) : "",
            massChangeId: effectiveRequestType === "MASS_CHANGE" || effectiveRequestType === "Mass Change" || effectiveRequestType === REQUEST_TYPE.CHANGE || effectiveRequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD ? idToUse.slice(3) : "",
            massSchedulingId: effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING || effectiveRequestType === "Finance Costing" ? idToUse.slice(3) : "",
            massExtendId: effectiveRequestType === REQUEST_TYPE.EXTEND || effectiveRequestType === REQUEST_TYPE.EXTEND || effectiveRequestType === REQUEST_TYPE.EXTEND ? idToUse.slice(3) : "",
            screenName: effectiveRequestType === "MASS_CREATE" || effectiveRequestType === "Mass Create" || effectiveRequestType === REQUEST_TYPE.CREATE ? REQUEST_TYPE.CREATE : effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? "" : REQUEST_TYPE.CHANGE,
            dtName: effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? "" : "MDG_MAT_MATERIAL_FIELD_CONFIG",
            version: effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? "" :"v2",
            page: 0,
            size: (effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING || RequestType === REQUEST_TYPE.FINANCE_COSTING) ? 100 :(RequestType === REQUEST_TYPE.CHANGE || RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD || effectiveRequestType === REQUEST_TYPE.CHANGE || effectiveRequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD) ? 10 : 50,
            sort: "",
            ApproverGroup:taskData?.ATTRIBUTE_5,
            Region:"",
            massChildSchedulingId : "",
            massChildExtendId : "",
            massChildChangeId : ""
          };
      
      const hSuccess = async (data) => {
        try {
          dispatch(setDataLoading(false));
          const apiResponse = data.body;
          dispatch(updateTotalCount(data?.totalElements));
          if (data?.totalPages === 1 || data?.currentPage + 1 === data?.totalPages) {
            dispatch(updateCurrentCount(data?.totalElements));
            dispatch(updateNextButtonStatus(true));
          } else {
            dispatch(updateCurrentCount((data?.currentPage + 1) * data?.pageSize));
          }
          if (taskData?.ATTRIBUTE_2 === REQUEST_TYPE.CHANGE || taskData?.ATTRIBUTE_2 === REQUEST_TYPE.CHANGE_WITH_UPLOAD || RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD || RequestType === REQUEST_TYPE.CHANGE ) {
            dispatch(
              setDynamicKeyValue({
                keyName: "requestHeaderData",
                data: apiResponse[0]?.Torequestheaderdata,
              })
            );
            getChangeTemplate(apiResponse[0]?.Torequestheaderdata?.TemplateName || "", apiResponse[0] || {});
            fetchDisplayDataRows(apiResponse);
            resolve();
            return;
          }
          if(RequestType === REQUEST_TYPE.FINANCE_COSTING || taskData?.ATTRIBUTE_2 === REQUEST_TYPE.FINANCE_COSTING) {
            
            const payloadData = {
              ReqCreatedBy: apiResponse[0]?.Torequestheaderdata?.ReqCreatedBy,
              RequestStatus: apiResponse[0]?.Torequestheaderdata?.RequestStatus,
              Region: apiResponse[0]?.Torequestheaderdata?.Region,
              ReqCreatedOn: new Date().toISOString(),
              ReqUpdatedOn: new Date().toISOString(),
              RequestType: apiResponse[0]?.Torequestheaderdata?.RequestType,
              RequestDesc: apiResponse[0]?.Torequestheaderdata?.RequestDesc,
              RequestPriority: apiResponse[0]?.Torequestheaderdata?.RequestPriority,
              LeadingCat: apiResponse[0]?.Torequestheaderdata?.LeadingCat,
              RequestId: apiResponse[0]?.Torequestheaderdata?.RequestId,
              TemplateName: apiResponse[0]?.Torequestheaderdata?.TemplateName,
              
            }
            dispatch(resetPayloadData({ data: payloadData }));
            const fcRows = await createFCRows(apiResponse)
            dispatch(setFCRows(fcRows));
            resolve();
            return;
          }
          const transformedPayload = await transformApiResponseToReduxPayload(apiResponse);
          await dispatch(setDisplayPayload({ data: transformedPayload?.payload }));
          const numericKeys = Object.keys(transformedPayload?.payload).filter((key) => !isNaN(Number(key)));
          const extractedData = {};
          numericKeys.forEach((key) => {
            extractedData[key] = transformedPayload?.payload[key];
          });
          dispatch(setMaterialRows(Object.values(extractedData)?.map((item) => item.headerData)));
          resolve();
        } catch (error) {
          customError(ERROR_MESSAGES.ERROR_GET_DISPLAY_DATA);
          reject(error);
        }
      };

      const hError = (error) => {
        customError(ERROR_MESSAGES.ERROR_FETCHING_DATA);
        reject(error);
      };
      doAjax(`/${destination_MaterialMgmt}/data/displayMassMaterialDTO`, "post", hSuccess, hError, payload);
    });
  };

  useEffect(() => {
    const loadData = async () => {
      if (RequestId) {
        await getDisplaydata(RequestId);
        if (((RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD && !rowData?.material?.length) || RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) && (rowData?.reqStatus === REQUEST_STATUS.DRAFT || rowData?.reqStatus === REQUEST_STATUS.UPLOAD_FAILED)) {
          dispatch(setTabValue(0));
          setIsSecondTabEnabled(false);
          setIsAttachmentTabEnabled(false);
        } else {
          dispatch(setTabValue(1));
          setIsSecondTabEnabled(true);
          setIsAttachmentTabEnabled(true);
        }

        setAddHardCodeData(true);
      } else {
        dispatch(setTabValue(0));
      }
    };
    
    loadData();
    return () => {
      dispatch(changeTemplateDT([]));
      dispatch(clearChangeLogData());
      dispatch(clearTemplateArray());
      dispatch(clearMaterialFieldConfig());
      dispatch(clearCreateTemplateArray());
      dispatch(clearCreateChangeLogData());
      dispatch(updateAllTabsData({}));
      dispatch(resetPayloadData({ data: {} }));
      dispatch(setMatlNoData([]))
      dispatch(setPlantData([]))
      dispatch(setRequestorPayload({}));
      dispatch(clearDynamicKeyValue());
      dispatch(updateSelectedRows([]));
      dispatch(setChangeFieldRows([]));
      dispatch(setChangeFieldRowsDisplay({}));
      clearLocalStorageItem(LOCAL_STORAGE_KEYS.CURRENT_TASK);
      clearLocalStorageItem(LOCAL_STORAGE_KEYS.ROLE);
    };
  }, [requestId, dispatch]);

  function callDivisionDtCall(region) {
    let payload = {
      decisionTableId: null,
      decisionTableName: DECISION_TABLE_NAME.MDG_MAT_REGION_DIVISION_MAPPING,
      version: "v1",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_REGION": region,
        },
      ],
    };
    getDtCall(payload);
  }

  useEffect(() => {
    if(payloadData?.Region) {
      callDivisionDtCall(payloadData?.Region);
    }
  }, [payloadData?.Region]);

  useEffect(() => {
    if (dtData) {
      const convertedData = convertKeysName(dtData?.result?.[0]?.MDG_MAT_REGION_DIVISION_MAPPING);
      const sortedData = [...convertedData].sort((a, b) => a.code.localeCompare(b.code));
      dispatch(setDropDown({ keyName: "Division", data: sortedData }));
      setLoading(false);
      setLoaderMessage(LOADING_MESSAGE.DT_LOADING);
    }
  }, [dtData]);

  useEffect(() => {
    getRequestHeaderTemplate();
    getAttachmentsIDM();
    // dispatch(setTabValue(0))
    dispatch(setMaterialRows([]));
    dispatch(setDropDown({ keyName: "Region", data: REGION }));
    dispatch(setDropDown({ keyName: "DiversionControlFlag", data: DIVERSION_CONTROL_FLAG }));
    return () => {
      dispatch(setRequestHeader({}));
    };
  }, []);

  useEffect(() => {
    if (isSecondTabEnabled) {
      setCompleted([true]);
    }
  }, [isSecondTabEnabled]);

  useEffect(() => {
    setPcNumber(idGenerator("MAT"));
  }, []);

  const getAttachmentsIDM = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_ATTACHMENTS_LIST_DT",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE": "Material",
          "MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO": "Create",
          "MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE": 1,
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    setIsLoading(true);
    // const formData = new FormData();
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        let responseData = data?.data?.result[0]?.MDG_ATTACHMENTS_ACTION_TYPE;
        let templateData = [];
        responseData?.map((element, index) => {
          var tempRow = {
            id: index,
            // templateName: element?.MDG_CHANGE_TEMPLATE_NAME,
            // templateData: element?.MDG_CHANGE_TEMPLATE_FIELD_LIST,
          };
          templateData.push(tempRow);
        });
        setRuleData(templateData);
        const attachmentNames = data?.data?.result[0]?.MDG_ATTACHMENTS_ACTION_TYPE || [];
        setAttachmentsData(attachmentNames);
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(`/${destination_IDM}/rest/v1/invoke-rules`, "post", hSuccess, hError, payload);
    } else {
      doAjax(`/${destination_IDM}/v1/invoke-rules`, "post", hSuccess, hError, payload);
    }
  };

  const handleExportTemplateExcel = () => {
    
    const url = RequestId?.includes("FCA") ? END_POINTS.EXCEL.DOWNLOAD_EXCEL_FINANCE : END_POINTS.EXCEL.DOWNLOAD_EXCEL_MAT;
    setLoaderMessage("Please wait 1-2 minutes while real-time data are exported into the template. Thank you for your patience.");
    setBlurLoading(true);
    let financePayload = {
      "massSchedulingId": payloadData?.RequestId,
    };
    let payload = {
      dtName: payloadData?.RequestType === REQUEST_TYPE?.CHANGE || payloadData?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD ? "MDG_MAT_CHANGE_TEMPLATE" : "MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",
      version: payloadData?.RequestType === REQUEST_TYPE?.CHANGE || payloadData?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD ? "v4" : "v1",
      requestId: payloadData?.RequestId || requestIdHeader || "",
      scenario: payloadData?.RequestType === REQUEST_TYPE?.CHANGE || payloadData?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD ? "Change with Upload" : "Create with Upload",
      templateName: payloadData?.TemplateName || "",
      region: payloadData?.Region || "",
      matlType: "ALL",
    };
    // NOTE: TO BE UNCOMMENTED WHEN PAYLOAD NEEDS CHANGE

    // if (!reqBench && !RequestId) {
    //   const result = changePayloadForTemplate(false)
    //   payload = { ...payload, materials: result }
    // }
    // else {
    //   const result = changePayloadForTemplate(true)
    //   payload = { ...payload, materials: result }
    // }

    const hSuccess = (response) => {
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      link.href = href;
      link.setAttribute("download", `${payloadData?.TemplateName ? payloadData?.TemplateName : RequestId?.includes("FCA") ? REQUEST_TYPE.FINANCE_COSTING : "Mass_Create"}_Data Export.xlsx`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);

      setBlurLoading(false);
      setLoaderMessage("");
      setMessageDialogMessage(`${payloadData?.TemplateName ? payloadData?.TemplateName : RequestId?.includes("FCA") ? REQUEST_TYPE.FINANCE_COSTING : "Mass_Create"}_Data Export.xlsx has been exported successfully.`);
      setAlertType("success");
      handleSnackBarOpen();
    };
    const hError = () => {};
    doAjax(`/${destination_MaterialMgmt}${url}`, "postandgetblob", hSuccess, hError, RequestId?.includes("FCA") ? financePayload : payload);
  };

  const handleSnackBarOpen = () => {
    setOpenSnackbar(true);
  };

  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };

  const handleYes = () => {
    if(requestId && !reqBench) {
      navigate(APP_END_POINTS?.MY_TASK);
    }
    else if(reqBench) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    }
    else if(!requestId && !reqBench) {
      navigate(APP_END_POINTS?.MASTER_DATA);
    }
  };

  const handleCancel = () => {
    setisDialogVisible(false)
  };
  return (
    <>
      {loading && <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />}
      <Box sx={{ padding: 2 }}>
        <Grid sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          {requestIdHeader || requestId ? (
            <Typography variant="h6" sx={{ mb: 1, textAlign: "left", display: "flex", alignItems: "center", gap: 1 }}>
              <PermIdentityOutlinedIcon sx={{ fontSize: "1.5rem" }} />
              Request Header ID: <span>{requestIdHeader ? appendPrefixByJavaKey(requestType, requestIdHeader) : requestId}</span>
            </Typography>
          ) : (
            <div style={{ flex: 1 }} />
          )}

          {tabValue === 1 && (
            <Box sx={{ display: "flex", justifyContent: "flex-end", gap: "1rem" }}>
              <Button
                variant="outlined"
                size="small"
                title="Download Error Report"
                disabled={!RequestId}
                onClick={() => {
                  navigate(`/requestBench/errorHistory?RequestId=${RequestId ? RequestId : ""}`, { state: { display: true } });
                }}
                color="primary"
              >
                <SummarizeOutlinedIcon sx={{ padding: "2px" }} />
              </Button>
              {payloadData?.RequestType === REQUEST_TYPE.CREATE || payloadData?.RequestType === REQUEST_TYPE.EXTEND || payloadData?.RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD ||
               payloadData?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || requestId?.includes("FCA") ? (
                <Button variant="outlined" disabled={!RequestId} size="small" onClick={() => setCreateChangeLogIsOpen(true)} title={requestId?.includes("FCA") ? "Finance Costing Change Log" : "Create Change Log"}>
                  <TrackChangesTwoToneIcon sx={{ padding: "2px" }} />
                </Button>
              ) : (
                <Button variant="outlined" disabled={!RequestId} size="small" onClick={openChangeLog} title="Change Log">
                  <TrackChangesTwoToneIcon sx={{ padding: "2px" }} />
                </Button>
              )}
              <Button variant="outlined" disabled={!RequestId} size="small" onClick={handleExportTemplateExcel} title="Export Excel">
                <FileUploadOutlinedIcon sx={{ padding: "2px" }} />
              </Button>
            </Box>
          )}

          {isChangeLogopen && <ChangeLog open={true} closeModal={handleClosemodalData} requestId={requestIdHeader || requestId.slice(3)} requestType={payloadData?.RequestType} />}
          {createChangeLogIsOpen && <CreateChangeLog open={true} closeModal={() => setCreateChangeLogIsOpen(false)} requestId={requestIdHeader || requestId.slice(3)} requestType={payloadData?.RequestType} />}
            {
              tabValue===3 && 
              <Box
              sx={{ display: "flex", justifyContent: "flex-end" }}
            >
                <Button
                variant="outlined"
                color="primary"
                startIcon={<PictureAsPdfIcon />}
                onClick={toPDF}
              >
                Export Preview
              </Button>
              </Box>
              }
        </Grid>

        {payloadData?.TemplateName && (
          <Typography variant="h6" sx={{ mb: 1, textAlign: "left", display: "flex", alignItems: "center", gap: 1 }}>
            <FeedOutlinedIcon sx={{ fontSize: "1.5rem" }} />
            Template Name: <span>{payloadData?.TemplateName}</span>
          </Typography>
        )}

        <IconButton
          onClick={() => {
            if(reqBench && !ENABLE_STATUSES?.includes(payloadData?.RequestStatus)) {
              navigate(APP_END_POINTS?.REQUEST_BENCH);
              return;
            }
            setisDialogVisible(true)
          }}
          color="primary"
          aria-label="upload picture"
          component="label"
          sx={{left: "-10px",}}
          title="Back"
        >
          <ArrowCircleLeftOutlined
            sx={{ fontSize: "25px", color: "#000000" }}
          />
        </IconButton>

        <Stepper nonLinear activeStep={tabValue} sx={{ display: "flex", alignItems: "center", justifyContent: "center", margin: "25px 14%", marginTop: "-35px" }}>
          {steps.map((label, index) => (
            <Step key={label} completed={completed[index]}>
              <StepButton color="error" disabled={
                      (index === 1 && !isSecondTabEnabled) ||
                      (index === 2 && !isAttachmentTabEnabled)||
                      (index===3 && !isSecondTabEnabled && !isAttachmentTabEnabled)
                    } onClick={() => handleTabChange(index)} sx={{ fontSize: "50px", fontWeight: "bold" }}>
                <span style={{ fontSize: "15px", fontWeight: "bold" }}>{label}</span>
              </StepButton>
            </Step>
          ))}
        </Stepper>

        <ReusableDialog
          dialogState={openMessageDialog}
          openReusableDialog={handleMessageDialogClickOpen}
          closeReusableDialog={handleMessageDialogClose}
          dialogTitle={messageDialogTitle}
          dialogMessage={messageDialogMessage}
          handleDialogConfirm={handleMessageDialogClose}
          dialogOkText={"OK"}
          handleOk={handleOk}
          // handleExtraButton={handleMessageDialogNavigate}
          dialogSeverity={messageDialogSeverity}
        />

        <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />

        {tabValue === 0 && (
          <>
            <RequestHeader setIsSecondTabEnabled={setIsSecondTabEnabled} setIsAttachmentTabEnabled={setIsAttachmentTabEnabled} requestStatus={rowData?.reqStatus ? rowData?.reqStatus : REQUEST_STATUS.ENABLE_FOR_FIRST_TIME} downloadClicked={downloadClicked} setDownloadClicked={setDownloadClicked} />
            {(RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD || RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) && ((rowData?.reqStatus == REQUEST_STATUS.DRAFT && !rowData?.material?.length) || rowData?.reqStatus == REQUEST_STATUS.UPLOAD_FAILED) && (
              <ExcelOperationsCard
                handleDownload={handleDownload}
                setEnableDocumentUpload={setEnableDocumentUpload}
                enableDocumentUpload={enableDocumentUpload}
                handleUploadMaterial={handleUploadMaterial}
              />
            )}
            {(payloadData?.RequestType === REQUEST_TYPE?.CHANGE || payloadData?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD) && !RequestId && payloadData?.DirectAllowed !== "X" && payloadData?.DirectAllowed !== undefined && (
              <Typography
                sx={{
                  fontSize: "13px",
                  fontWeight: "500",
                  color: colors?.error?.dark,
                  marginTop: "1rem",
                  marginLeft: "0.5rem",
                }}
              >
                <Box component="span" sx={{ fontWeight: "bold" }}>
                  Note:
                </Box>{" "}
                You are not authorized to Tcode{" "}
                <Box component="span" sx={{ fontWeight: "bold" }}>
                  {" "}
                  MM02.
                </Box>
              </Typography>
            )}
          </>
        )}

        {tabValue === 1 &&
          (payloadData?.RequestType === REQUEST_TYPE?.CREATE || taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.CREATE || RequestType === REQUEST_TYPE?.CREATE || RequestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD  ? (
            <RequestDetails requestStatus={rowData?.reqStatus ? rowData?.reqStatus : REQUEST_STATUS.ENABLE_FOR_FIRST_TIME} mandFields={mandFields} addHardCodeData={addHardCodeData} setIsAttachmentTabEnabled={setIsAttachmentTabEnabled} setCompleted={setCompleted} />
          ) : payloadData?.RequestType === REQUEST_TYPE?.EXTEND || taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.EXTEND || taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.EXTEND_WITH_UPLOAD || RequestType === REQUEST_TYPE?.EXTEND || RequestType === REQUEST_TYPE?.EXTEND_WITH_UPLOAD ? (
            <RequestDetailsForExtend requestStatus={rowData?.reqStatus ? rowData?.reqStatus : REQUEST_STATUS.ENABLE_FOR_FIRST_TIME} mandFields={mandFields} addHardCodeData={addHardCodeData} setIsAttachmentTabEnabled={setIsAttachmentTabEnabled} setCompleted={setCompleted} />
          ) : payloadData?.RequestType === REQUEST_TYPE?.FINANCE_COSTING || taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.FINANCE_COSTING || RequestType === REQUEST_TYPE?.FINANCE_COSTING ? (
            <RequestDetailsForFC setCompleted={setCompleted}/>
          ): (
            <RequestDetailsForChange setIsAttachmentTabEnabled={true} setCompleted={setCompleted} downloadClicked={downloadClicked} setDownloadClicked={setDownloadClicked}/>
          ))}
        {tabValue === 2 && <AttachmentsCommentsTab requestStatus={rowData?.reqStatus ? rowData?.reqStatus : REQUEST_STATUS.ENABLE_FOR_FIRST_TIME} attachmentsData={attachmentsData} requestIdHeader={requestIdHeader ? appendPrefixByJavaKey(requestType, requestIdHeader) : requestId} pcNumber={pcNumber} />}
        {tabValue === 3 && 
            <Box
            ref={targetRef}
            sx={{
              width: "100%",
              overflow: "auto", // Ensure all content fits inside
            }}
          >
            <PreviewPage >
              {payloadData?.RequestType === REQUEST_TYPE?.CHANGE || taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.CHANGE || RequestType === REQUEST_TYPE?.CHANGE || RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD ? (
                <PreviewChange/>
              ) : (
                <PreviewCreate/>
              )}
            </PreviewPage>
          </Box>
        }
      </Box>
      {<ReusableSnackBar openSnackBar={openSnackbar} alertMsg={messageDialogMessage} alertType={alertType} handleSnackBarClose={handleSnackBarClose} />}
      {isDialogVisible && (
        <CustomDialog isOpen={isDialogVisible} titleIcon={<WarningOutlined size="small" sx={{ color: colors?.secondary?.amber, fontSize: "20px" }} />} Title={"Warning"} handleClose={handleCancel}>
          <DialogContent sx={{ mt: 2 }}>{DIALOUGE_BOX_MESSAGES.LEAVE_PAGE_MESSAGE}</DialogContent>
          <DialogActions>
            <Button variant="outlined" size="small" sx={{ ...button_Outlined }} onClick={handleCancel}>
              No
            </Button>
            <Button variant="contained" size="small" sx={{ ...button_Primary }} onClick={handleYes}>
              Yes
            </Button>
          </DialogActions>
        </CustomDialog>
      )}
    </>
  );
};

export default CreateNewRequest;
