import { createSlice } from "@reduxjs/toolkit";
import appConfigsJson from '../data/applicationConfig.json';
// console.log(appConfigsJson)
let initialState = {
    environment: window.location.hostname,
    token:'',
    iwaToken:"Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJJNExzTXlreEQ4czlXdFhOLUxaTXoxbDd3SmxfTmZmNGN5WkM5MEpZbklVIn0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.COa1kr_EzNefzauPZr_56H5Mw9kbzrPqA-vQNuw8XvbEhQtKVJIeVIdn5xeGMNrBbaBSF52DNER8XB1UZXFNbtJBtaYn5P2AMBh2UGouygwuwKneBH1PElnm-0V6kMeZgUCYfjTEbhuyIZVffxuOmgSDZW5XRix9LfTf95CcbGdakZqbUbGhMXNMZ2BvaCA9681yOzw26A_6oH28p_xTscz3-nq0FRXIe4WH1D-tGPadY8PPLhmyY8B9J50CFlovU092LSYQerZ26NllVt0Vi_gCKQ2y5_halAw99zdyFlUtYFTXdMdhLTjcfyHUMWtKoZq6FZttpiL0p17_J6zFtw",
    SERVICE_BASE_URL_MAP:{},
    importedModules:{},
    logoutUserWarning:false,
    idmToken:'',
}
appConfigsJson.url.forEach(i=>{
    initialState.SERVICE_BASE_URL_MAP[i.name]= i.url;
})
let applicationConfigReducer = createSlice({
    name: 'applicationConfigSlice',
    initialState,
    reducers:{
        setToken:(state, action)=>{
            state.token = action.payload.token
            return state
        },
        setImportModules:(state, action)=>{
            state.importedModules = action.payload
            return state
        },
        handleLogoutWarningScreen:(state,action)=>{
            state.logoutUserWarning = action.payload
            return state
        },
        setIdmToken:(state, action)=>{
            state.idmToken = action.payload.idmToken
            return state
        },
    }
})

export const {setToken,setImportModules,handleLogoutWarningScreen,setIdmToken,setUtilityToken} = applicationConfigReducer.actions
export default applicationConfigReducer.reducer