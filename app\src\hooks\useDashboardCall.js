import { useState, useEffect } from "react";
import { doAjax } from "../components/Common/fetchService";
import { destination_Dashboard } from "../destinationVariables";
import { useSelector } from "react-redux";

const API_ENDPOINTS = ["getTotalRequestsBasedOnReqTypeAndReqStatus", "getAverageLifecycleDurationOfReqByObjType", "getNumberOfNewReqSubmissionsByAllObjAndObjTypes", "getPercentageOfReqSuccCompWithOutRejection", "getPercentageOfReqSuccCompleted", "getReqThatHaveBreachedSLA", "getHowManyReqWereRejectedByRoleByUser", "getHowManyRequestsArePendingApproverMDM", "getAvgTimeFromMDMApproveToSAPSyndication", "getAvgApprovalTimeByApprover", "getNumberOfRejectedReqBasedOnRequestor", "getNoOfReqCompletedByMDM", "getNoOfOpenReqByReqType", "getNoOfReqRequestedAndNoOfApprovApproved"];




const getColumnByIndex = (index) => {
  const columns = ["First", "Second", "Third"];
  return columns[index %3];
};
const formatApiData = (body, fallbackId, fallbackIndex) => {
  if (!body || typeof body !== "object" || Array.isArray(body)) return null;

  const rawType = body?.graphDetails?.chartType;
  const chartType = rawType?.toString()?.trim()?.toUpperCase();
  const graphName = body?.graphDetails?.graphName || "Untitled Chart";
  const id = body?.id || fallbackId;
  const column = body?.column||getColumnByIndex(id);

  if (!chartType || !graphName) return null;

  if (chartType === "PIE" || chartType === "DONUT") {
    const label = body?.label;
    const series = body?.series;

    if (!Array.isArray(label) || !Array.isArray(series)) return null;

    return {
      id,
      column,
      graphDetails: { chartType, graphName },
      label,
      series,
    };
  }

  if (Array.isArray(body?.data)) {
    return {
      id,
      column,
      graphDetails: { chartType, graphName },
      data: body.data,
    };
  }

  return null;
};

export const useDashboardCall = () => {
  const [cards, setCards] = useState([]);
  const [loading, setLoading] = useState(true);
  const dashboardFilters = useSelector(
  (state) => state.commonFilter?.Dashboard || {}
);

const dynamicPayload = {
  FromDate: dashboardFilters?.dashboardDate?.[0] || "2024-01-01",
  ToDate: dashboardFilters?.dashboardDate?.[1] || "2025-12-31",
  Requestor: "",
  KpiId: "",
  Module: dashboardFilters?.dashBoardModuleName?.join(",") || "Material,Profit Center,Cost Center,General Ledger",
  UserId: "",
  Priority: "",
  Region: dashboardFilters?.selectedRegion || "",
  ReqType: dashboardFilters?.selectedRequestType?.join(",") || "",
  ReqStatus: dashboardFilters?.selectedRequestStatus?.join(",") || "",
};
  useEffect(() => {
    const fetchAll = async () => {
      try {
        const promises = API_ENDPOINTS.map((path, i) => {
          return new Promise((resolve) => {
            doAjax(
              `/${destination_Dashboard}/counts/${path}`,
              "post",
              (res) => {
                const formatted = res?.body ? formatApiData(res.body, i + 1, i) : null;
                resolve(formatted);
              },
              () => resolve(null),
              dynamicPayload
            );
          });
        });

        const results = await Promise.all(promises);
        const filtered = results.filter(Boolean);
        setCards(filtered);
      } catch (error) {
        console.error("Dashboard graph fetch failed", error);
      } finally {
        setLoading(false);
      }
    };

    fetchAll();
  }, [dashboardFilters]);

  return { cards, loading };
};
