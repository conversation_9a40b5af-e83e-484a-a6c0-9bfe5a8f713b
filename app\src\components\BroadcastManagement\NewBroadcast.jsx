import {
  <PERSON><PERSON>,
  IconButton,
  <PERSON><PERSON>,
  Typo<PERSON>,
  Box,
  Card,
  InputLabel,
  FormControl,
  Select,
  TextField,
  Button,
  BottomNavigation,
  Paper,
  MenuItem,
} from "@mui/material";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
// import {
//   button_Primary,
//   button_Outlined,
//   button_MarginRight,
//   font_Small,
//   outermostContainer_Information,
// } from "../Common/commonStyles";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
// import { DateTimePicker, LocalizationProvider, MobileDateTimePicker } from "@mui/x-date-pickers";
import FileUploadOutlinedIcon from "@mui/icons-material/FileUploadOutlined";
import axios from "axios";
import { destination_Admin } from "../../destinationVariables";
import moment from "moment";
// import { useSelector } from "react-redux";
import CampaignOutlinedIcon from "@mui/icons-material/CampaignOutlined";
import EventNoteOutlinedIcon from "@mui/icons-material/EventNoteOutlined";
import SlideshowOutlinedIcon from "@mui/icons-material/SlideshowOutlined";
// import { formValidator } from "../../functions";
import { FamilyRestroomTwoTone } from "@mui/icons-material";
import { doAjax } from "../Common/fetchService";
import ReusableDialog from "../Common/ReusableDialog";
import ReusableSnackBar from "../Common/ReusableSnackBar";
import { font_Small, outermostContainer_Information } from "../common/commonStyles";
import { LocalizationProvider, MobileDateTimePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
// import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
const NewBroadcast = () => {
  // let userData = useSelector((state) => state.userManagement.userData);

  const presentDate = new Date();
  const futureDate = new Date();
  futureDate.setDate(presentDate.getDate() + 7);
  const navigate = useNavigate();
  const [BroadcastForm, setBroadcastForm] = useState({
    title: "",
    description: "",
    category: "",
    startDate: presentDate,
    endDate: futureDate,
    supplier: "",
    files: null,
    link: "",
  });
  const [TitleLength, setTitleLength] = useState(0);
  const [DescLength, setDescLength] = useState(0);

  const handleForm = (e) => {
    setBroadcastForm({ ...BroadcastForm, [e.target.name]: e.target.value });
    if (e.target.name == "title") {
      setTitleLength(e.target.value.length);
    }
    if (e.target.name == "description") {
      setDescLength(e.target.value.length);
    }
    if (e.target.name == "files") {
      setBroadcastForm({ ...BroadcastForm, [e.target.name]: e.target.files });
    }
  };
  const handleDate = (e) => {
    setBroadcastForm({ ...BroadcastForm, startDate: e });
  };
  const handleDate2 = (e) => {
    setBroadcastForm({ ...BroadcastForm, endDate: e });
  };
  const publish = () => {
    const formData = new FormData();
    if (BroadcastForm.files) {
      [...BroadcastForm.files].forEach((item) =>
        formData.append("files", item)
      );
    }

    //  formData.append("files",BroadcastForm.files);
    var doc = JSON.stringify({
      broadcastCategory: BroadcastForm.category,
      broadcastTitle: BroadcastForm.title,
      // createdBy: userData.emailId,

      startDate: moment(BroadcastForm.startDate).format(
        "YYYY-MM-DD HH:mm:ss.000"
      ),
      endDate: moment(BroadcastForm.endDate).format("YYYY-MM-DD HH:mm:ss.000"),
      description: BroadcastForm.description,
      supplierCategory: BroadcastForm.supplier,
      createdDate: moment(presentDate).format("YYYY-MM-DD HH:mm:ss.000"),
      externalUrl: BroadcastForm.link,
    });
    formData.append("broadcastDetails", doc);
    // fetch(
    //   `/${destination_Admin}/broadcastManagement/uploadFiles`,
    //   {
    //     method: "POST",
    //     body: formData,
    //   }
    // )
    let hSuccess=(data) => {
      console.log(data);
    };
    let hError=()=>{}
  doAjax(`/${destination_Admin}/broadcastManagement/uploadFiles`,'postformdata',hSuccess,hError, formData)
    
   
  };
  const draft = () => {
    const formData = new FormData();
    if (BroadcastForm.files) {
      [...BroadcastForm.files].forEach((item) =>
        formData.append("files", item)
      );
    }

    //  formData.append("files",BroadcastForm.files);
    var doc = JSON.stringify({
      broadcastCategory: BroadcastForm.category,
      broadcastTitle: BroadcastForm.title,
      // createdBy: userData.emailId,

      startDate: moment(BroadcastForm.startDate).format(
        "YYYY-MM-DD HH:mm:ss.000"
      ),
      endDate: moment(BroadcastForm.endDate).format("YYYY-MM-DD HH:mm:ss.000"),
      description: BroadcastForm.description,
      supplierCategory: BroadcastForm.supplier,
      createdDate: moment(presentDate).format("YYYY-MM-DD HH:mm:ss.000"),
      status: "Draft",
      externalUrl: BroadcastForm.link,
    });
    formData.append("broadcastDetails", doc);
    // fetch(
    //   `/${destination_Admin}/broadcastManagement/uploadFiles`,
    //   {
    //     method: "POST",
    //     body: formData,
    //   }
    // )
    let hSuccess=(data) => {
      console.log(data);
    }
    let hError=()=>{}
    doAjax(`/${destination_Admin}/broadcastManagement/uploadFiles`, 'postformdata',hSuccess,hError, formData)
   
      
  };
  const [openSnackbarSubmit, setopenSnackbarSubmit] = useState(false);

  const handleopenSnackbarSubmit = () => {
    setopenSnackbarSubmit(true);
  };
  const [openSubmit, setOpenSubmit] = useState(false);
  const handleOpenSubmit = () => setOpenSubmit(true);
  const handleCloseSubmit = () => {
    setOpenSubmit(false);
  };
  const handleCloseConfirm = () => {
    setOpenSubmit(false);
    handleopenSnackbarSubmit();
  };
  const [isPublish, setisPublish] = useState(false);
  const handlePostBroadcast = () => {
    if (isPublish) {
      publish();
    } else {
      draft();
    }
  };
  const [formValidationErrorItems, setformValidationErrorItems] = useState([]);

  const [openMessageDialog, setOpenMessageDialog] = useState(false);

  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };
  const [fieldError, setfieldError] = useState(false);

  return (
    <>
      <div style={{ padding: "0 1rem 0 1rem" }}>
        <Grid container sx={outermostContainer_Information}>
          <Grid container>
            <Grid item md={7} style={{ padding: "16px", paddingLeft: "" }}>
              <Stack direction="row">
                <IconButton
                  onClick={() => navigate("/broadcastManagement")}
                  color="primary"
                  aria-label="upload picture"
                  component="label"
                >
                  <ArrowCircleLeftOutlinedIcon
                    sx={{
                      fontSize: "25px",
                      color: "#000000",
                    }}
                  />
                </IconButton>
                <Box>
                  <Typography variant="h5" paddingTop="0.3rem" fontSize="20px">
                    <strong>New Broadcast</strong>
                  </Typography>

                  <Typography variant="body2" color="#777" fontSize="12px">
                    This view displays the details of the New Broadcast and
                    allows user to create new one
                  </Typography>
                </Box>
              </Stack>
            </Grid>
          </Grid>
        </Grid>
        <ReusableDialog
          dialogState={openSubmit}
          openReusableDialog={handleOpenSubmit}
          closeReusableDialog={handleCloseSubmit}
          dialogTitle="Broadcast Creation"
          dialogMessage={"Proceed with creation of Broadcast?"}
          showInputText={false}
          handleDialogConfirm={handlePostBroadcast}
          handleDialogReject={handleCloseSubmit}
          showCancelButton={true}
          dialogCancelText="Cancel"
          showOkButton={true}
          dialogOkText={"Submit"}
          dialogSeverity={"success"}
          handleOk={handleCloseConfirm}
          // handleExtraButton={handleMessageDialogNavigate}
          // alertMsg={submitText}
        />
        <ReusableSnackBar
          openSnackBar={openSnackbarSubmit}
          alertMsg={`Broadcast Created Successfully`}
          handleSnackBarClose={() =>
            navigate("/configCockpit/broadcastManagement")
          }
        />

        {fieldError && (
          <ReusableDialog
            dialogState={openMessageDialog}
            openReusableDialog={handleMessageDialogClickOpen}
            closeReusableDialog={handleMessageDialogClose}
            dialogTitle={"Error"}
            dialogMessage={"Update valid Inputs for mandatory fields"}
            handleDialogConfirm={handleMessageDialogClose}
            dialogOkText={"OK"}
            dialogSeverity={"danger"}
            // showOkButton={true}
            handleDialogReject={handleMessageDialogClose}
          />
        )}

        <Card sx={{ padding: "25px" }}>
          <Grid container rowSpacing={2} columnSpacing={2}>
            <Grid item xs={3}>
              <Box sx={{ minWidth: 140 }}>
                <Typography sx={font_Small} fontWeight="normal">
                  Broadcast Category
                  <span style={{ color: "red" }}>*</span>
                </Typography>
                <FormControl fullWidth size="small" sx={{ margin: ".5em 0px" }}>
                  <Select
                    sx={{
                      // width: "16.7rem",
                      backgroundColor: "white",
                      border: formValidationErrorItems.includes("category")
                        ? "1px solid red"
                        : "",
                      borderRadius: "6px",
                    }}
                    placeholder="Select Broadcast Category"
                    name="category"
                    onChange={handleForm}
                    value={BroadcastForm.category}
                    // onChange={handleChangeSelect}
                    displayEmpty={true}
                    // sx={font_Small}

                    renderValue={() =>
                      BroadcastForm.category !== "" ? (
                        BroadcastForm.category
                      ) : (
                        <div
                          className="placeholderstyle"
                          style={{ color: "#C1C1C1" }}
                        >
                          Select Broadcast Category{" "}
                        </div>
                      )
                    }
                  >
                    <MenuItem sx={font_Small} value={""}>
                      <div style={{ color: "#C1C1C1" }}>
                        Select Broadcast Category{" "}
                      </div>
                    </MenuItem>
                    <MenuItem sx={font_Small} value={"Announcements"}>
                      <CampaignOutlinedIcon sx={{ color: "#0087d5" }} />
                      &nbsp;&nbsp;Announcements
                    </MenuItem>
                    <MenuItem sx={font_Small} value={"Videos"}>
                      <SlideshowOutlinedIcon sx={{ color: "#0087d5" }} />{" "}
                      &nbsp;&nbsp;Videos
                    </MenuItem>
                    <MenuItem sx={font_Small} value={"Events"}>
                      <EventNoteOutlinedIcon sx={{ color: "#0087d5" }} />{" "}
                      &nbsp;&nbsp;Events
                    </MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </Grid>
            <Grid item xs={3}>
              <Box>
                <Typography sx={font_Small} fontWeight="normal">
                  Start Date & Time
                  <span style={{ color: "red" }}>*</span>
                </Typography>
                {/* <LocalizationProvider
                //  dateAdapter={AdapterDateFns}
                 >
                  <MobileDateTimePicker
                    // error = {formValidationErrorItems.includes("shipmentdatetime")}

                    inputFormat="dd MMM yyyy & HH:mm"
                    value={BroadcastForm.startDate}
                    name="startDate"
                    onChange={(e) => handleDate(e)}
                    renderInput={(params) => (
                      <TextField
                        fullWidth
                        size="small"
                        {...params}
                        sx={{ margin: ".5em 0px" }}
                      />
                    )}
                  />
                </LocalizationProvider> */}
              </Box>
            </Grid>

            <Grid item xs={3}>
              <Box>
                <Typography sx={font_Small} fontWeight="normal">
                  End Date & Time
                  <span style={{ color: "red" }}>*</span>
                </Typography>
                {/* <LocalizationProvider
                //  dateAdapter={AdapterDateFns}
                 >
                  <MobileDateTimePicker
                    // error = {formValidationErrorItems.includes("shipmentdatetime")}

                    inputFormat="dd MMM yyyy & HH:mm"
                    value={BroadcastForm.endDate}
                    name="endDate"
                    onChange={(e) => handleDate2(e)}
                    renderInput={(params) => (
                      <TextField
                        fullWidth
                        size="small"
                        {...params}
                        sx={{ margin: ".5em 0px" }}
                      />
                    )}
                  />
                </LocalizationProvider> */}
              </Box>
            </Grid>
            <Grid item xs={12}>
              <Box sx={{ minWidth: 120 }}>
                <Typography sx={font_Small} fontWeight="normal">
                  Broadcast Title
                  <span style={{ color: "red" }}>*</span>
                  <span style={{ color: "#C1C1C1", fontSize: "12px" }}>
                    {" "}
                    (Max length 100 characters)
                  </span>
                </Typography>
                <TextField
                  sx={{ margin: ".5em 0px" }}
                  error={formValidationErrorItems.includes("title")}
                  fullWidth
                  placeholder="Enter Broadcast Title"
                  variant="outlined"
                  size="small"
                  name="title"
                  onChange={handleForm}
                  value={BroadcastForm.title}
                  inputProps={{ maxLength: 100 }}
                ></TextField>
                <Typography
                  variant="caption"
                  ml={1}
                  sx={{ display: "flex", justifyContent: "flex-start" }}
                >
                  {TitleLength}/100
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ minWidth: 120 }}>
                <Typography sx={font_Small} fontWeight="normal">
                  Broadcast Description
                  <span style={{ color: "red" }}>*</span>
                  <span style={{ color: "#C1C1C1", fontSize: "12px" }}>
                    {" "}
                    (Max length 300 characters)
                  </span>
                </Typography>
                <TextField
                  sx={{ margin: ".5em 0px" }}
                  error={formValidationErrorItems.includes("description")}
                  fullWidth
                  multiline
                  rows={4}
                  placeholder="Enter Broadcast Description"
                  variant="outlined"
                  size="small"
                  name="description"
                  inputProps={{ maxLength: 300 }}
                  onChange={handleForm}
                  value={BroadcastForm.description}
                ></TextField>
                <Typography
                  variant="caption"
                  ml={1}
                  sx={{ display: "flex", justifyContent: "flex-start" }}
                >
                  {DescLength}/300
                </Typography>
              </Box>
            </Grid>

            {BroadcastForm.category == "Videos" ? (
              <>
                {" "}
                <Grid item xs={3}>
                  {" "}
                  <Box sx={{ marginTop: "0rem" }}>
                    <label htmlFor="file">
                      <Typography>Upload Document</Typography>
                    </label>{" "}
                    <input
                      id="file"
                      multiple
                      accept=".mp4"
                      type="file"
                      name="files"
                      onChange={handleForm}
                      style={{ marginTop: ".5rem" }}
                    />
                    <Typography
                      variant="caption"
                      sx={{ display: "block", marginTop: ".4rem" }}
                    >
                      **Only mp4 format supported
                    </Typography>{" "}
                  </Box>{" "}
                </Grid>
              </>
            ) : (
              <Grid item xs={3}>
                {" "}
                <Box sx={{ marginTop: "0rem" }}>
                  <label htmlFor="file">
                    <Typography>Upload Document</Typography>
                  </label>
                  <input
                    id="file"
                    multiple
                    accept=".jpeg, .jpg, .png"
                    type="file"
                    name="files"
                    onChange={handleForm}
                    style={{ marginTop: ".5rem" }}
                  />

                  <Typography
                    variant="caption"
                    sx={{ display: "block", marginTop: ".4rem" }}
                  >
                    **Only png, jpeg, jpg formats supported
                  </Typography>
                </Box>{" "}
              </Grid>
            )}
            <Grid item xs={2}>
              <Box>
                <Typography sx={font_Small} fontWeight="normal">
                  URL
                </Typography>
                <TextField
                  sx={{ margin: ".5em 0px" }}
                  fullWidth
                  placeholder="Enter URL"
                  variant="outlined"
                  size="small"
                  name="link"
                  onChange={handleForm}
                  value={BroadcastForm.link}
                ></TextField>
              </Box>
            </Grid>
            <Grid item xs={12}>
              <Grid container sx={{ alignItems: "center", marginTop: "5px" }}>
                <Grid item xs={1}>
                  <hr />
                </Grid>
                <Grid item xs={2}>
                  <Typography variant="body2" color="#777" align="center">
                    New Broadcast Applicable for{" "}
                  </Typography>
                </Grid>
                <Grid item xs={9}>
                  <hr />
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={2.5}>
              <Box sx={{ minWidth: 140 }}>
                <Typography sx={font_Small} fontWeight="normal">
                  Supplier Category
                  <span style={{ color: "red" }}>*</span>
                </Typography>
                <FormControl fullWidth size="small" sx={{ margin: ".5em 0px" }}>
                  <InputLabel
                    id="demo-multiple-checkbox-label"
                    shrink={false}
                    sx={{
                      color: "#C1C1C1",
                      fontSize: "14px",
                      fontWeight: 400,
                    }}
                  >
                    {true && "Select Supplier Category"}
                  </InputLabel>
                  <Select
                    placeholder="Select Supplier Category"
                    // value={TransactionType}
                    // onChange={handleChangeSelect}
                    displayEmpty={true}
                    sx={font_Small}
                    multiple
                    // renderValue={(selected) => selected.join(", ")}
                  >
                    {/* {names.map((name) => (
                      <MenuItem key={name} value={name}>
                        <Checkbox
                          checked={TransactionType.indexOf(name) > -1}
                        />
                        <ListItemText primary={name} />
                      </MenuItem>
                    ))} */}
                  </Select>
                </FormControl>
              </Box>
            </Grid>
          </Grid>
        </Card>

        <Paper
          sx={{ position: "fixed", bottom: 0, left: 0, right: 0, zIndex: 5 }}
          elevation={2}
        >
          <BottomNavigation
            showLabels
            className="container_BottomNav"
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              
            }}
          >
            <Button
              size="small"
              variant="outlined"
              onClick={() => {
                setisPublish(false);
                // formValidator(
                //   BroadcastForm,
                //   ["title", "category", "description"],
                //   setformValidationErrorItems
                // );
                if (
                  BroadcastForm.category != "" &&
                  BroadcastForm.title != "" &&
                  BroadcastForm.description != ""
                ) {
                  handleOpenSubmit();
                }
                if (
                  BroadcastForm.category == "" ||
                  BroadcastForm.title == "" ||
                  BroadcastForm.description == ""
                ) {
                  setfieldError(true);
                  handleMessageDialogClickOpen();
                }
              }}
              className='btn-mr'
            >
              Save As Draft
            </Button>
            <Button
              size="small"
              variant="contained"
              onClick={() => {
                setisPublish(true);
                // formValidator(
                //   BroadcastForm,
                //   ["title", "category", "description"],
                //   setformValidationErrorItems
                // );
                if (
                  BroadcastForm.category != "" &&
                  BroadcastForm.title != "" &&
                  BroadcastForm.description != ""
                ) {
                  handleOpenSubmit();
                }
                if (
                  BroadcastForm.category == "" ||
                  BroadcastForm.title == "" ||
                  BroadcastForm.description == ""
                ) {
                  setfieldError(true);
                  handleMessageDialogClickOpen();
                }
              }}
             
            >
              Publish
            </Button>
          </BottomNavigation>
        </Paper>
      </div>
    </>
  );
};

export default NewBroadcast;
