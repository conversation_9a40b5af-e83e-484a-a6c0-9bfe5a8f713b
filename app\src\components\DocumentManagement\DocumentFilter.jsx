import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Autocomplete,
  Box,
  Button,
  Checkbox,
  Chip,
  CircularProgress,
  FormControl,
  FormControlLabel,
  FormGroup,
  Grid,
  InputLabel,
  ListItemText,
  MenuItem,
  Popover,
  Select,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import {
  button_Outlined,
  button_Primary,
  container_filter,
  font_Small,
  iconButton_SpacingSmall,
  icon_MarginLeft,
  icon_MarginRight,
} from "../common/commonStyles";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useDispatch, useSelector } from "react-redux";
import DateRange from "../Common/DateRangePicker";
import SearchIcon from '@mui/icons-material/Search';
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import ClearIcon from '@mui/icons-material/Clear';
import FilterListIcon from '@mui/icons-material/FilterList';
import {
  commonFilterClear,
  commonFilterUpdate,
} from "../../app/commonFilterSlice";
import AutoCompleteSimpleDropDown from "@components/Common/ui/dropdown/AutoCompleteSimpleDropDown";
import {
  destination_Dashboard,
  destination_DocumentManagement,
  destination_MaterialMgmt,
} from "../../destinationVariables";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { doAjax } from "@components/Common/fetchService";
import LargeDropdown from "@components/Common/ui/dropdown/LargeDropdown";
import { END_POINTS } from "@constant/apiEndPoints";
import { colors } from "@constant/colors";
import { styled } from '@mui/material/styles';
import { ROLES } from "@constant/enum";
import useLogger from "@hooks/useLogger";



const StyledAccordion = styled(Accordion)(({ theme }) => ({
  marginTop: "0px !important",
  border: `1px solid ${colors.primary.border}`,
  borderRadius: '8px',
  boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
  '&:not(:last-child)': {
    borderBottom: 0,
  },
  '&:before': {
    display: 'none',
  },
}));

const StyledAccordionSummary = styled(AccordionSummary)(({ theme }) => ({
  minHeight: "2rem !important",
  margin: "0px !important",
  backgroundColor: colors.primary.ultraLight,
  borderRadius: '8px 8px 0 0',
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    backgroundColor: `${colors.primary.light}20`,
  },
}));

const StyledTextField = styled(TextField)({
  '& .MuiOutlinedInput-root': {
    borderRadius: '4px',
    '&:hover fieldset': {
      borderColor: colors.primary.main,
    },
  },
});

const FilterContainer = styled(Grid)({
  padding: '0.75rem',
  gap: '0.5rem',
});

const ButtonContainer = styled(Grid)({
  display: 'flex',
  justifyContent: 'flex-end',
  paddingRight: '0.75rem',
  paddingBottom: '0.75rem',
  paddingTop: '0rem',
  gap: '0.5rem',

});

const ActionButton = styled(Button)({
  borderRadius: '4px',
  padding: '4px 12px',
  textTransform: 'none',
  fontSize: '0.875rem',
});

const LabelTypography = styled(Typography)({
  fontSize: '0.75rem',
  color: colors.primary.dark,
  marginBottom: '0.25rem',
  fontWeight: 500,
});

const DocumentFilter = ({
  handleSearchBar,
  names,
  PresetObj,
  PresetMethod,
  handleSearch,
  getFilter  
}) => {
  const dispatch = useDispatch();
  const presentDate = new Date();
  const backDate = new Date();
  backDate.setDate(backDate.getDate() - 7);
  const FilterSearchForm = useSelector(
    (state) => state.commonFilter["DocumentManagement"]
  );
  const { customError } = useLogger();
  const appSettings = useSelector((state) => state.appSettings["Format"]);
  let userData = useSelector((state) => state.userManagement.userData);
  let userRoles = useSelector((state) => state.userManagement.roles);
  const [requestType, setRequestType] = useState([]);
  const [docOptions, setDocOptions] = useState([]);
  const [selectedCreatedBy, setSelectedCreatedBy] = useState([]);
  const [isDropDownLoading, setIsDropDownLoading] = useState(false);
  const [createdByOptions, setCreatedByOptions] = useState([]);
  const [docType, setDocType] = useState([]);
  const [popoverAnchorEl, setPopoverAnchorEl] = useState(null);
  const [popoverContent, setPopoverContent] = useState("");
  const [isPopoverVisible, setIsPopoverVisible] = useState(false);
  const popoverRef = useRef(null);
  const [attachmentOptions, setAttachmentOptions] = useState([]);
  const [attachmentType, setAttachmentType] = useState([]);
  const [timerId, setTimerId] = useState(null);
  const [clearClicked, setClearClicked] = useState(false);

  useEffect(() => {
    if (clearClicked) {
      getFilter();
      setClearClicked(false);
    }
  }, [clearClicked]);

  const handlePopoverOpen = (event, content) => {
    setPopoverAnchorEl(event.currentTarget);
    setPopoverContent(content);
    setIsPopoverVisible(true);
  };

  const handlePopoverClose = () => {
    setIsPopoverVisible(false);
  };

  const handleMouseEnterPopover = () => {
    setIsPopoverVisible(true);
  };

  const handleMouseLeavePopover = () => {
    setIsPopoverVisible(false);
  };

  const popoverOpen = Boolean(popoverAnchorEl);
  const popoverId = popoverOpen ? "custom-popover" : undefined;

  useEffect(() => {
    getDocType();
    getAttachmentType();
  }, []);

  useEffect(() => {
    var tempReqType = requestType.map((item) => item).join("$^$");

    let tempFilterData = {
      ...FilterSearchForm,
      requestType: tempReqType,
    };
    dispatch(
      commonFilterUpdate({
        module: "DocumentManagement",
        filterData: tempFilterData,
      })
    );
  }, [requestType]);

  useEffect(() => {
    var tempDocType = docType.map((item) => item).join("$^$");

    let tempFilterData = {
      ...FilterSearchForm,
      docType: tempDocType,
    };
    dispatch(
      commonFilterUpdate({
        module: "DocumentManagement",
        filterData: tempFilterData,
      })
    );
  }, [docType]);

  useEffect(() => {
    var tempAttType = attachmentType.map((item) => item).join("$^$");

    let tempFilterData = {
      ...FilterSearchForm,
      attType: tempAttType,
    };
    dispatch(
      commonFilterUpdate({
        module: "DocumentManagement",
        filterData: tempFilterData,
      })
    );
  }, [attachmentType]);

  useEffect(() => {
      var tempCreatedBy = selectedCreatedBy.map((item) => item?.code).join("$^$");
  
      let tempFilterData = {
        ...FilterSearchForm,
        createdBy: tempCreatedBy,
      };
      dispatch(
        commonFilterUpdate({
          module: "DocumentManagement",
          filterData: tempFilterData,
        })
      );
    }, [selectedCreatedBy]);

  const requestTypeOptions = [
    "Create",
    "Change",
    "Extend",
    "Create with Upload",
    "Change with Upload",
    "Extend with Upload",
    "Finance Costing",
  ];

  const handleClear = () => {
    setDocType([]);
    setRequestType([]);
    setSelectedCreatedBy([]);
    setAttachmentType([]);
    dispatch(
      commonFilterUpdate({
        module: "DocumentManagement",
        filterData: {
          ...FilterSearchForm,
          transactionId: "",
          transactionType: [],
          docType: "",
          attType: "",
          uploadedBy: "",
          createdBy: "",
          number: "",
          requestType: "",
          uploadedDate: [backDate, presentDate]
        },
      })
    );
    dispatch(
      commonFilterClear({
        module: "DocumentManagement",
        days: 7,
      })
    );
    setClearClicked(true);
  };

  const handleDate = (e) => {
    
    if (e !== null) {
      const uploadedDate = e.map(date =>
        new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()))
      );
      dispatch(
        commonFilterUpdate({
          module: "DocumentManagement",
          filterData: {
            ...FilterSearchForm,
            uploadedDate: uploadedDate,
          },
        })
      );
    }
  };

  const getDocType = () => {
    const hSuccess = (data) => {
      setDocOptions(data?.body);
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_DocumentManagement}/${END_POINTS.DMS_API.GET_FILE_TYPE}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAttachmentType = () => {
    const hSuccess = (data) => {
      setAttachmentOptions(data?.body);
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_DocumentManagement}/${END_POINTS.DMS_API.GET_ATTACHMENT_TYPE}`,
      "get",
      hSuccess,
      hError
    );
  };

  const handleMaterial = (e) => {
    if (e.target.value !== null) {
      var tempMaterial = e.target.value;

      let tempFilterData = {
        ...FilterSearchForm,
        number: tempMaterial,
      };
      dispatch(
        commonFilterUpdate({
          module: "DocumentManagement",
          filterData: tempFilterData,
        })
      );
    }
  };

  const handleSelectAllReqType = () => {
    if (requestType.length === requestTypeOptions?.length) {
      setRequestType([]);
    } else {
      setRequestType(requestTypeOptions);
    }
  };
  const handleSelectAlldocType = () => {
    if (docType.length === docOptions?.length) {
      setDocType([]);
    } else {
      setDocType(docOptions);
    }
  };
  const handleSelectAllAttachmentType = () => {
    if (attachmentType.length === attachmentOptions?.length) {
      setAttachmentType([]);
    } else {
      setAttachmentType(attachmentOptions);
    }
  };

  const handleSelectAllCreatedBy = () => {
    if (selectedCreatedBy.length === createdByOptions.length) {
      setSelectedCreatedBy([]);
    } else {
      setSelectedCreatedBy(createdByOptions);
    }
  };

  const handleCreatedByInputChange = (e) => {
    const inputValue = e.target.value;
    if (inputValue == "") {
      return;
    }
    if (timerId) {
      clearTimeout(timerId);
    }

    const newTimerId = setTimeout(() => {
      getCreatedBy(inputValue);
    }, 500);

    setTimerId(newTimerId);
  };

  const getCreatedBy = (inputValue) => {
      setIsDropDownLoading(true);
      let payload = {
        createdBy: inputValue,
      };
      const hSuccess = (data) => {
        setIsDropDownLoading(false);
        setCreatedByOptions(data.body);
      };
      const hError = (error) => {
        setIsDropDownLoading(false);
        customError(error);
      };
      doAjax(`/${destination_MaterialMgmt}/${END_POINTS.REQUEST_BENCH.CREATED_BY}`, "post", hSuccess, hError, payload);
    };

  const isCreatedBySelected = (option) => {
    return selectedCreatedBy.some((selectedOption) => selectedOption?.code === option?.code);
  };

  return (
    <Grid container sx={container_filter}>
      <Grid item md={12}>
        <StyledAccordion defaultExpanded={false}>
          <StyledAccordionSummary
            expandIcon={<ExpandMoreIcon sx={{ fontSize: '1.25rem', color: colors.primary.main }} />}
            aria-controls="panel1a-content"
            id="panel1a-header"
          >
            <FilterListIcon sx={{ fontSize: '1.25rem', marginRight: 1, color: colors.primary.main }} />
            <Typography
              sx={{
                fontSize: '0.875rem',
                fontWeight: 600,
                color: colors.primary.dark,
              }}
            >
              Filter Document
            </Typography>
          </StyledAccordionSummary>
          <AccordionDetails sx={{ padding: 0 }}>
            <FilterContainer container spacing={0.5}>
              <Grid item md={2}>
                <LabelTypography>Material</LabelTypography>
                <StyledTextField 
                  size="small"
                  fullWidth
                  onChange={handleMaterial}
                  placeholder="ENTER MATERIAL"
                  value={FilterSearchForm?.number}
                />
              </Grid>

              <Grid item md={2}>
                <LabelTypography>Request Type</LabelTypography>
                <AutoCompleteSimpleDropDown
                  options={requestTypeOptions.filter(name => name !== "Select All")}
                  value={requestType}
                  onChange={(value) => {
                    if (value.length > 0 && value[value.length - 1]?.label === "Select All") {
                      handleSelectAllReqType();
                    } else {
                      setRequestType(value);
                    }
                  }}
                  placeholder="SELECT REQUEST TYPE"
                />
              </Grid>
              <Grid item md={2}>
                <LabelTypography>Document Type</LabelTypography>
                <AutoCompleteSimpleDropDown
                  options={[
                    ...docOptions.filter((name) => name !== "Select All"),
                  ]}
                  value={docType}
                  onChange={(value) => {
                    if (
                      value.length > 0 &&
                      value[value.length - 1]?.label === "Select All"
                    ) {
                      handleSelectAlldocType();
                    } else {
                      setDocType(value);
                    }
                  }}
                  placeholder="SELECT DOCUMENT TYPE"
                />
              </Grid>
              <Grid item md={2}>
                <LabelTypography>Attachment Type</LabelTypography>
                <AutoCompleteSimpleDropDown
                  options={[
                    ...attachmentOptions.filter((name) => name !== "Select All"),
                  ]}
                  value={attachmentType}
                  onChange={(value) => {
                    if (
                      value.length > 0 &&
                      value[value.length - 1]?.label === "Select All"
                    ) {
                      handleSelectAllAttachmentType();
                    } else {
                      setAttachmentType(value);
                    }
                  }}
                  placeholder="SELECT ATTACHMENT TYPE"
                />
              </Grid>
              
                {userRoles.includes(`${ROLES.SUPER_USER}`) ? (
                        <Grid item md={3}>
                          <LabelTypography sx={font_Small}>Uploaded By</LabelTypography>
                          <FormControl fullWidth size="small">
                            <Autocomplete
                              fullWidth
                              size="small"
                              value={selectedCreatedBy}
                              multiple
                              disableCloseOnSelect
                              limitTags={1}
                              noOptionsText={
                                isDropDownLoading ? (
                                  <Box
                                    sx={{
                                      display: "flex",
                                      justifyContent: "center",
                                      mt: 1,
                                      zIndex: 9999,
                                      top: "10px",
                                    }}
                                  >
                                    <CircularProgress size={20} />
                                  </Box>
                                ) : (
                                  "No Data Available"
                                )
                              }
                              onChange={(e, value, reason) => {
                                if (reason === "clear" || value?.length === 0) {
                                  setSelectedCreatedBy([]);
                                  // TO BE USED LATER
                                  //setselectedPreset([]);
                                  return;
                                }

                                if (value.length > 0 && value[value.length - 1]?.code === "Select All") {
                                  handleSelectAllCreatedBy();
                                } else {
                                  setSelectedCreatedBy(value);
                                }
                              }}
                              options={createdByOptions?.length ? [{ code: "Select All", desc: "" }, ...createdByOptions] : createdByOptions ?? []}
                              getOptionLabel={(option) => {
                                if (option?.code) return `${option?.code} - ${option?.desc}` ?? "";
                                else return "";
                              }}
                              renderOption={(props, option, { selected }) => (
                                <li {...props}>
                                  <FormGroup>
                                    <FormControlLabel
                                      control={<Checkbox checked={isCreatedBySelected(option) || (option?.code === "Select All" && selectedCreatedBy?.length === createdByOptions?.length)} />}
                                      label={
                                        <Typography style={{ fontSize: 12 }}>
                                          {option?.desc ? (
                                            <>
                                              <strong>{option.code}</strong> - {option.desc}
                                            </>
                                          ) : (
                                            <strong>{option.code}</strong>
                                          )}
                                        </Typography>
                                      }
                                    />
                                  </FormGroup>
                                </li>
                              )}
                              renderTags={(selected, getTagProps) => {
                                const selectedOptionsText = selected.map((option) => `${option.code} `).join("<br />");
                                return selected.length > 1 ? (
                                  <>
                                    <Chip
                                      sx={{
                                        height: 25,
                                        fontSize: "0.85rem",
                                        ".MuiChip-label": { padding: "0 6px" },
                                      }}
                                      label={`${selected[0]?.code}`}
                                      {...getTagProps({ index: 0 })}
                                    />
                                    <Chip
                                      sx={{
                                        height: 25,
                                        fontSize: "0.85rem",
                                        ".MuiChip-label": { padding: "0 6px" },
                                      }}
                                      label={`+${selected.length - 1}`}
                                      onMouseEnter={(event) => handlePopoverOpen(event, selectedOptionsText)}
                                      onMouseLeave={handlePopoverClose}
                                    />
                                    <Popover
                                      id={popoverId}
                                      open={isPopoverVisible}
                                      anchorEl={popoverAnchorEl}
                                      onClose={handlePopoverClose}
                                      anchorOrigin={{
                                        vertical: "bottom",
                                        horizontal: "center",
                                      }}
                                      transformOrigin={{
                                        vertical: "top",
                                        horizontal: "center",
                                      }}
                                      onMouseEnter={handleMouseEnterPopover}
                                      onMouseLeave={handleMouseLeavePopover}
                                      ref={popoverRef}
                                      sx={{
                                        "& .MuiPopover-paper": {
                                          backgroundColor: "#f5f5f5",
                                          boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
                                          borderRadius: "8px",
                                          padding: "10px",
                                          fontSize: "0.875rem",
                                          color: "#4791db",
                                          border: "1px solid #ddd",
                                        },
                                      }}
                                    >
                                      <Box
                                        sx={{
                                          maxHeight: "270px",
                                          overflowY: "auto",
                                          padding: "5px",
                                        }}
                                        dangerouslySetInnerHTML={{ __html: popoverContent }}
                                      />
                                    </Popover>
                                  </>
                                ) : (
                                  selected.map((option, index) => (
                                    <Chip
                                      sx={{
                                        height: 25,
                                        fontSize: "0.85rem",
                                        ".MuiChip-label": { padding: "0 6px" },
                                      }}
                                      label={`${option?.code}`}
                                      {...getTagProps({ index })}
                                    />
                                  ))
                                );
                              }}
                              renderInput={(params) => (
                                <Tooltip title={params.inputProps.value.length == 0 ? "Type to search" : ""} arrow disableHoverListener={params.inputProps.value.length >= 1} placement="top">
                                  <TextField
                                    sx={{
                                      fontSize: "12px !important",
                                      "& .MuiOutlinedInput-root": {
                                        height: 35,
                                      },
                                      "& .MuiInputBase-input": {
                                        padding: "10px 14px",
                                      },
                                    }}
                                    {...params}
                                    variant="outlined"
                                    placeholder="SELECT UPLOADED BY"
                                    onChange={(e) => {
                                      handleCreatedByInputChange(e);
                                    }}
                                  />
                                </Tooltip>
                              )}
                            />
                          </FormControl>
                        </Grid>
                      ) : (
                        <Grid item md={3}>
                          <LabelTypography sx={font_Small}>Uploaded By</LabelTypography>

                          <TextField fullWidth size="small" disabled={true} value={userData?.emailId} onChange={handleCreatedBy} placeholder="ENTER UPLOADED BY" />
                        </Grid>
                      )}
              <Grid item md={2}>
                <LabelTypography>Uploaded Between</LabelTypography>
                <FormControl fullWidth size="small">
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DateRange
                      handleDate={handleDate}
                      date={FilterSearchForm?.uploadedDate}
                    />
                  </LocalizationProvider>
                </FormControl>
              </Grid>
            </FilterContainer>
            <ButtonContainer>
              <ActionButton
                variant="outlined"
                size="small"
                startIcon={<ClearIcon sx={{ fontSize: '1rem' }} />}
                onClick={handleClear}
                sx={{ borderColor: colors.primary.main, color: colors.primary.main }}
              >
                Clear
              </ActionButton>

              <ActionButton
                variant="contained"
                size="small"
                startIcon={<SearchIcon sx={{ fontSize: '1rem' }} />}
                onClick={handleSearch}
                sx={{ backgroundColor: colors.primary.main }}
              >
                Search
              </ActionButton>
            </ButtonContainer>
          </AccordionDetails>
        </StyledAccordion>
      </Grid>
    </Grid>
  );
};

export default DocumentFilter;
