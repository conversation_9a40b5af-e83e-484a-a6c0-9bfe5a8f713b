import { DateRangePicker, Stack } from "rsuite";
import subDays from "date-fns/subDays";
import startOfWeek from "date-fns/startOfWeek";
import endOfWeek from "date-fns/endOfWeek";
import addDays from "date-fns/addDays";
import startOfMonth from "date-fns/startOfMonth";
import startOfQuarter from "date-fns/startOfQuarter";
import endOfMonth from "date-fns/endOfMonth";
import addMonths from "date-fns/addMonths";
import "rsuite/dist/rsuite.css";
import { font_Small } from "./commonStyles";
import { Typography } from "@mui/material";

function DateRange(props) {
  const predefinedRanges = [
    {
      label: "This Week",
      value: [startOfWeek(new Date()), endOfWeek(new Date())],
    },
    {
      label: "Last Month",
      value: [
        startOfMonth(addMonths(new Date(), -1)),
        endOfMonth(addMonths(new Date(), -1)),
      ],
    },
    {
      label: "Year to Date",
      value: [new Date(new Date().getFullYear(), 0, 1), new Date()],
    },
    {
      label: "Current Month",
      value: [startOfMonth(new Date()), new Date()],
    },
    {
      label: "Current Quarter",
      value: [startOfQuarter(new Date()), new Date()],
    },
  ];

  return (
    <div style={{position:'relative'}}>
      <Typography sx={font_Small}>{props?.filterName}</Typography>
      <DateRangePicker
        className="dates"
        cleanable={false}
        size="small"
        placeholder="Select Date Range"
        value={props.date}
        onClean={props.handleClear}
        ranges={predefinedRanges}
        onChange={props.handleDate}
        format="dd MMM yyyy"
        placement="auto"
        
        style={{
          ...font_Small,
          zIndex:'1444',
          height:"1.25rem"
        }}
      />
    </div>
  );
}
export const dateGapVariable = 8;
export default DateRange;
