import "react-dropzone-uploader/dist/styles.css";
import Dropzone from "react-dropzone-uploader";
import { Box, Button, Grid, Paper, Stack, Typography } from "@mui/material";
import { useState, useEffect } from "react";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";
import { useDispatch, useSelector } from "react-redux";
// import HomepageModal1 from "../HomepageModal1";
// import { commonFilterUpdate } from "../../app/commonFilterSlice";
// import { destination_Admin } from "../../destinationVariables";

const Layout = ({
  input,
  previews,
  submitButton,
  dropzoneProps,
  files,
  extra: { maxFiles },
}) => {
  const [DragView, setDragView] = useState(previews);
  const dispatch = useDispatch();
  const FilterSearchForm = useSelector(
    (state) => state.commonFilter["Broadcast"]
  );
  function handleOnDragEnd(result) {
    const items = Array.from(DragView);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    setDragView(items);

    // dispatch(
    //   commonFilterUpdate({
    //     module: "Broadcast",
    //     filterData: {
    //       ...FilterSearchForm,
    //       sequence: items.map((i) => i.props.meta.name),
    //     },
    //   })
    // );
  }
  useEffect(() => {
    // setDragView(previews);
    // dispatch(
    //   commonFilterUpdate({
    //     module: "Broadcast",
    //     filterData: {
    //       ...FilterSearchForm,
    //       sequence: previews.map((i) => i.props.meta.name),
    //     },
    //   })
    // );
  }, [previews]);

  return (
    <Grid container>
      <Grid item xs={6}>
        <Stack sx={{ border: "1px solid black" }} {...dropzoneProps}>
          <Box>
            <Typography
              sx={{
                alignItems: "flex-start",
                marginTop: "50px",
                color: "#3B30C8",
                fontSize: "14px",
                fontWeight: "bolder",
              }}
            >
              Drag and Drop to Upload File
            </Typography>
          </Box>
          <Box mt={2}>
            <Typography
              sx={{ fontSize: "15px" }}
              variant="body2"
              fontWeight="bold"
              color="black"
            >
              Or
            </Typography>
          </Box>
          <Box mt={1}>{input}</Box>
          <Box mt={files.length > 0 ? 3 : 7}>
            <Typography color="#757575" variant="body2">
              Supported Files : PNG, JPG or SVG
            </Typography>
          </Box>
        </Stack>
      </Grid>
      <Grid item xs={6}>
        <Stack pl={3}>
          <Box>
            <Typography color="black" fontWeight="bold" fontSize="14px">
              Uploaded Banners
            </Typography>
          </Box>
          <Box>
            <Typography color="#757575" fontSize="12px">
              Drag to rearrange position of picture in Carousel
            </Typography>
          </Box>
          <Box>
            {console.log(previews)}
            <DragDropContext onDragEnd={handleOnDragEnd}>
              <Droppable droppableId="droppable">
                {(provided) => (
                  <Stack {...provided.droppableProps} ref={provided.innerRef}>
                    {DragView.map((item, index) => {
                      return (
                        <Draggable
                          key={item.key}
                          index={index}
                          draggableId={item.key}
                        >
                          {(provided) => (
                            <Box
                              sx={{ position: "relative" }}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              ref={provided.innerRef}
                            >
                              {item}
                              <Box
                                sx={{
                                  position: "absolute",
                                  left: 120,
                                  top: 25,
                                }}
                              >
                                <Typography variant="body2" color="black">
                                  {item.props.meta.name}
                                </Typography>
                              </Box>
                            </Box>
                          )}
                        </Draggable>
                      );
                    })}
                    {provided.placeholder}
                  </Stack>
                )}
              </Droppable>
            </DragDropContext>
          </Box>
          {files.length > 0 && submitButton}
        </Stack>
      </Grid>
    </Grid>
  );
};

const FileDropZone = (props) => {
  const [files, setfiles] = useState([]);
 
  const handleClick = () => {
  props?.uploadedFiles.map((item)=>{
    fetch(item.url).then((res) => {
      res.arrayBuffer().then((buf) => {
        const file = new File([buf], item.name, {
          type: item.type,
        });
        setfiles((prevState) => [...prevState, file]);
      });
    });
  })  
  };
useEffect(() => {
  handleClick()

  
}, [])

  return (
    <div id="imageUpload" class="dropzone mt-2">
      <Dropzone
        onSubmit={props?.handleAttachmentsSubmit}
        styles={{
          submitButton: {
            background: "#3B30C8",
            marginRight: "1.5rem",
            fontWeight: "normal",
          },
          dropzone: {
            border: "1px dashed black",
            padding: 0,
            overflow: "hidden",
            minHeight: "310px",
            flexDirection: "column",
            display: "flex",
            backgroundColor: "#eeeeee",
          },
          submitButtonContainer: { position: "fixed", bottom: 0, right: 10 },
          inputLabel: {
            alignItems: "flex-start",
            marginTop: "50px",
            color: "#3B30C8",
            fontSize: "14px",
            fontWeight: "bolder",
          },
          previewImage: {
            borderRadius: "4px",
            width: "100px",
            height: "85px",
          },
          preview: { padding: 0, border: 0 },
          inputLabelWithFiles: {
            textTransform: "none",
            backgroundColor: "#3B30C8",
            color: "white",
            fontWeight: "normal",
            padding: 0,
            margin: 0,
            padding: "6px",
          },
          inputLabel: {
            textTransform: "none",
            height: "32px",
            width: "84px",
            backgroundColor: "#3B30C8",
            color: "white",
            fontWeight: "normal",
            padding: 0,
            marginTop: 116,
            padding: "4px",
            fontSize: "14px",
            marginLeft: 115,
            borderRadius: "4px",
          },
        }}
        LayoutComponent={Layout}
        inputContent={"Browse File"}
        inputWithFilesContent={"Browse File"}
        // InputComponent={CustomInput}
        maxFiles={4}
        initialFiles={files}
        submitButtonContent={"Save"}
        // onSubmit={()=> console.log(files)}
      />
    </div>
  );
};
export default FileDropZone;
