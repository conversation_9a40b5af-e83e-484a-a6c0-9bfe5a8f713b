import { Card, CardContent, IconButton, Stack, Typography } from "@mui/material";
import { NoDataFound } from "@cw/rds/illustrations";
import { colors } from "@constant/colors";
import { GraphCard } from "@cw/rds";
import { DragIndicator } from "@mui/icons-material";

const GraphCards = ({ title, data = [], handleOpenDialog = () => {}, dragHandleProps = {} }) => {
  const hasValidChartData = (data) => {
    return !!data && Object.keys(data).length > 0;
  };

  const renderChart = () => {
    if (!hasValidChartData(data)) {
      return renderNoData();
    }
    return <GraphCard values={data} isTable={true} showDownload showGraphName />;
  };

  const renderNoData = () => (
    <div style={{ textAlign: "center" }}>
      <img alt="No Data Found" style={{ height: "250px" }} src={NoDataFound} />
      <Typography variant="h6" style={{ marginTop: "10px" }}>
        No Data Found
      </Typography>
    </div>
  );

  return (
    <Card
      sx={{
        borderRadius: "10px",
        boxShadow: 1,
        height: "auto",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <CardContent>
        <Stack justifyContent="flex-end" alignItems="center" direction="row">
          <IconButton {...dragHandleProps} size="small" style={{ cursor: "grab" }}>
            <DragIndicator />
          </IconButton>
        </Stack>
        {renderChart()}
      </CardContent>
    </Card>
  );
};

export default GraphCards;

