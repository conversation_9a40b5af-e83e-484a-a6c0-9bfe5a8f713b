import styled from "@emotion/styled";
import { IosShare, Refresh } from "@mui/icons-material";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import CampaignOutlinedIcon from "@mui/icons-material/CampaignOutlined";
import EventNoteOutlinedIcon from "@mui/icons-material/EventNoteOutlined";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import SlideshowOutlinedIcon from "@mui/icons-material/SlideshowOutlined";
import {
  BottomNavigation,
  Box,
  Button,
  Chip,
  Grid,
  IconButton,
  Paper,
  Stack,
  Tooltip,
  Typography,
} from "@mui/material";
import * as React from "react";
// import { createServiceRequestForm } from "../../functions";
import {
  button_MarginRight,
  button_Outlined,
  button_Primary,
  container_table,
  iconButton_SpacingSmall,
  outermostContainer,
  outermostContainer_Information,
} from "../common/commonStyles";

// import DocumentFilter from "../DocumentManagement/DocumentFilter";
import BroadcastFilter from "./BroadcastFilter";
import AddPhotoAlternateOutlinedIcon from "@mui/icons-material/AddPhotoAlternateOutlined";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import ManageBanner from "./ManageBanner";
import {
  destination_Admin,
} from "../../destinationVariables";
// import { commonFilterUpdate } from "../../app/commonFilterSlice";
import { useDispatch, useSelector } from "react-redux";
import axios from "axios";
import moment from "moment";
import { doAjax } from "../Common/fetchService";
import SearchBar from "../Common/SearchBar";
import ReusableTable from "../Common/ReusableTable";
import ReusableDialog from "../Common/ReusableDialog";
import ReusableSnackBar from "../Common/ReusableSnackBar";
import { ViewDetailsIcon } from "../Common/icons";
function BroadcastHome() {
  const navigate = useNavigate();
  const [Status_ServiceReqForm, setStatus_ServiceReqForm] = useState(false);
  const dispatch = useDispatch();
  const [BroadcastRows, setBroadcastRows] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const param = new Date();
  // const appSettings=useSelector((state)=> state.appSettings["Format"])

  const getAllBroadcast = () => {
    setIsLoading(true);
    let hSuccess = (data) => {
      // setIsLoading(false);
      // setBroadcastRows(data.broadcastDetailsDtoList);
    };

    let hError = () => {};
    doAjax(
      `/${destination_Admin}/broadcastManagement/getAllBroadcastDetails/${moment(
        param
      ).format("YYYY-MM-DD hh:mm:ss.000")}`,
      "get",
      hSuccess,
      hError
    );
  };
  useEffect(() => {
    getAllBroadcast();
  }, []);

  const MatDelete = ({ id }) => {
    const handleDelete = () => {
      let hSuccess = () => getAllBroadcast();
      let hError = () => {};
      doAjax(
        `/${destination_Admin}/broadcastManagement/deleteBroadcastById/${id}`,
        "delete",
        hSuccess,
        hError
      );
    };
    const [openSubmit, setOpenSubmit] = useState(false);
    const handleOpenSubmit = () => setOpenSubmit(true);
    const handleCloseSubmit = () => {
      setOpenSubmit(false);
    };
    return (
      <IconButton
        sx={{ ...iconButton_SpacingSmall }}
        onClick={handleOpenSubmit}
      >
        <ReusableDialog
          dialogState={openSubmit}
          openReusableDialog={handleOpenSubmit}
          closeReusableDialog={handleCloseSubmit}
          dialogTitle="Broadcast Deletion"
          dialogMessage={`Do you want to delete Broadcast ${id}?`}
          showInputText={false}
          handleDialogConfirm={handleDelete}
          handleDialogReject={handleCloseSubmit}
          showCancelButton={true}
          dialogCancelText="Cancel"
          dialogOkText={"Delete"}
          dialogSeverity={"danger"}
          handleOk={handleDelete}
        />
        <Tooltip title="Delete">
          <DeleteOutlinedIcon color="danger" />
        </Tooltip>
      </IconButton>
    );
  };

  const [openSnackbar, setopenSnackbar] = useState(false);
  const handleSnackbarOpen = () => {
    setopenSnackbar(true);
  };
  const handleSnackbarClose = () => {
    setopenSnackbar(false);
  };
  const handleAttachmentsSubmit = (files, allFiles) => {
    setskeleton(true);

    const formData = new FormData();
    [...files].forEach((item) => formData.append("files", item.file));

    let hSuccess = (data) => {
      handleCloseModal();
      setskeleton(false);

      if (data.status == "Success") {
        handleSnackbarOpen();
      }
    };
    let hError = () => {};
    doAjax(
      `/${destination_Admin}/broadcastManagement/upload/banner`,
      "postformdata",
      hSuccess,
      hError,
      formData
    );
  };

  const [openModal, setOpenModal] = useState(false);
  const handleOpenModal = () => setOpenModal(true);
  const handleCloseModal = () => setOpenModal(false);
  const rows = [];
  const columns = [
    {
      field: "broadcastId",
      headerName: "Broadcast ID",

      width: 200,
      editable: false,
    },
    {
      field: "broadcastCategory",
      headerName: "Broadcast Category",
      flex: 1,
      headerAlign: "left",
      renderCell: (params) => {
        switch (params.row.broadcastCategory) {
          case "Announcements":
            return (
              <Stack
                direction="row"
                sx={{ justifyContent: "center", alignItems: "center" }}
              >
                {" "}
                <CampaignOutlinedIcon
                  sx={{ color: "#0087d5", fontSize: "16px" }}
                />
                <Typography fontSize="12px">
                  {" "}
                  &nbsp;&nbsp;Announcements
                </Typography>
              </Stack>
            );
          case "Videos":
            return (
              <Stack
                direction="row"
                sx={{ justifyContent: "center", alignItems: "center" }}
              >
                {" "}
                <SlideshowOutlinedIcon
                  sx={{ color: "#0087d5", fontSize: "16px" }}
                />
                <Typography fontSize="12px"> &nbsp;&nbsp;Videos</Typography>
              </Stack>
            );
          case "Events":
            return (
              <Stack
                direction="row"
                sx={{ justifyContent: "center", alignItems: "center" }}
              >
                {" "}
                <EventNoteOutlinedIcon
                  sx={{ color: "#0087d5", fontSize: "16px" }}
                />
                <Typography fontSize="12px"> &nbsp;&nbsp;Events</Typography>
              </Stack>
            );
        }
      },
    },
    {
      field: "broadcastTitle",
      headerName: "Broadcast Title",
      width: 150,
      align: "left",
      renderCell: (params) => {
        return (
          <Stack direction="row" sx={{ alignItems: "center", width: "150px" }}>
            <Typography
              fontSize="12px"
              sx={{
                textOverflow: "ellipsis",
                overflow: "hidden",
                whiteSpace: "noWrap",
              }}
            >
              <Tooltip title={params.row.broadcastTitle}>
                <span>{params.row.broadcastTitle}</span>
              </Tooltip>
            </Typography>
          </Stack>
        );
      },
    },
    {
      field: "startDate",
      headerName: "Start Date & Time",
      flex: 1,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        // let temp = moment(params.row.startDate).format(`${appSettings.date}Thh:mm A`);

        return (
          <Stack sx={{ justifyContent: "center", alignItems: "center" }}>
            <Typography variant="body2">{temp.split("T")[0]}</Typography>
            <Typography variant="body2" sx={{ color: "#7E7E7E" }}>
              {temp.split("T")[1]}
            </Typography>
          </Stack>
        );
      },
    },
    {
      field: "endDate",
      headerName: "End Date & Time",
      flex: 1,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        // let temp = moment(params.row.endDate).format(`${appSettings.date}Thh:mm A`);

        return (
          <Stack sx={{ justifyContent: "center", alignItems: "center" }}>
            <Typography variant="body2">{temp.split("T")[0]}</Typography>
            <Typography variant="body2" sx={{ color: "#7E7E7E" }}>
              {temp.split("T")[1]}
            </Typography>
          </Stack>
        );
      },
    },
    {
      field: "supplierCategory",
      headerName: "Supplier Category",
      flex: 1,
    },

    {
      field: "status",
      headerName: "Broadcast Status",
      sortable: false,
      width: 150,
      renderCell: (cellValues) => {
        return (
          <Chip
            sx={{
              justifyContent: "flex-start",
              fontSize: "12px",
              borderRadius: "4px",
              color: "#000",
              fontSize: "12px",
              minWidth: "130px",
              backgroundColor:
                cellValues.row.status === "Active"
                  ? "#cdefd6"
                  : cellValues.row.status === "Draft"
                  ? "#FFC88787"
                  : cellValues.row.status === "Archived"
                  ? "#FAFFC0"
                  : "#cddcef",
            }}
            label={cellValues.row.status}
          />
        );
      },
    },
    {
      field: "actions",
      headerName: "Action",
      sortable: false,
      flex: 1,

      align: "center",
      align: "center",
      headerAlign: "center",
      disableClickEventBubbling: true,
      renderCell: (params) => {
        return (
          <>
            <IconButton
              sx={{ ...iconButton_SpacingSmall }}
              onClick={() =>
                navigate(`viewBroadcast/${params.row.broadcastId}`)
              }
            >
              <Tooltip title="View">{ViewDetailsIcon}</Tooltip>
            </IconButton>
            <IconButton
              sx={{ ...iconButton_SpacingSmall }}
              onClick={() =>
                navigate(`editBroadcast/${params.row.broadcastId}`)
              }
            >
              <Tooltip title="Edit">
                <EditOutlinedIcon />
              </Tooltip>
            </IconButton>
            <MatDelete id={params.row.broadcastId} />
          </>
        );
      },
    },
  ];

  const StyledGridOverlay = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    height: "100%",
  }));
  function srcToFile(src, fileName, mimeType) {
    return fetch(src)
      .then(function (res) {
        return res.arrayBuffer();
      })
      .then(function (buf) {
        return new File([buf], fileName, { type: mimeType });
      });
  }
  function dataURItoBlob(dataURI) {
    // convert base64/URLEncoded data component to raw binary data held in a string
    var byteString;
    if (dataURI.split(",")[0].indexOf("base64") >= 0)
      byteString = atob(dataURI.split(",")[1]);
    else byteString = unescape(dataURI.split(",")[1]);

    // separate out the mime component
    var mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];

    // write the bytes of the string to a typed array
    var ia = new Uint8Array(byteString.length);
    for (var i = 0; i < byteString.length; i++) {
      ia[i] = byteString.charCodeAt(i);
    }

    return new Blob([ia], { type: mimeString });
  }
  let tempFiles = [];

  const [uploadedFiles, setuploadedFiles] = useState([{}]);
  const [skeleton, setskeleton] = useState(false);

  const getBanner = () => {
    setskeleton(true);
    let hSuccess = (data) => {
      for (var key in data) {
        let temp = { name: "", url: "", type: "" };

        temp.name = data[key].headers["File Name"][0];
        temp.type = data[key].headers["Content-Type"][0];
        temp.url = `data:${data[key].headers["Content-Type"][0]};base64, ${data[key].body}`;
        tempFiles.push(temp);
      }
      setuploadedFiles(tempFiles);
      setskeleton(false);
    };
    let hError = () => {};
    doAjax(
      `/${destination_Admin}/broadcastManagement/getBanner`,
      "get",
      hSuccess,
      hError
    );
  };
  useEffect(() => {
    getBanner();
  }, []);
  const [title, setTitle] = useState("");
  const clearSearchBar = () => {
    setTitle("");
  };
  // const formcontroller_SearchBar = useSelector(
  //   (state) => state.commonSearchBar["BroadcastHome"]
  // );
  const handleSearchBar = () => {
    setIsLoading(true);
    let hSuccess = (data) => {
      setIsLoading(false);

      setBroadcastRows(data.broadcastDetailsDtoList);
    };
    let hError = () => {};
    let data = {
      // broadcastTitle: formcontroller_SearchBar.title,
    };
    doAjax(
      `/${destination_Admin}/broadcastManagement/filter/broadcast`,
      "post",
      hSuccess,
      hError,
      data
    );
  };
  // const FilterSearchForm = useSelector(
  //   (state) => state.commonFilter["BroadcastHome"]
  // );

  const handleSearch = () => {
    setIsLoading(true);
    let hSuccess = (data) => {
      setIsLoading(false);

      setBroadcastRows(data.broadcastDetailsDtoList);
    };
    let hError = () => {};
    let data = {
      broadcastId: FilterSearchForm.id,
      broadcastCategory: FilterSearchForm.category.toString(),
      startFromDate: FilterSearchForm.startDate[0],
      startToDate: FilterSearchForm.startDate[1],
      endFromDate: FilterSearchForm.endDate[0],
      endToDate: FilterSearchForm.endDate[1],
      // startDate:FilterSearchForm.startDate,
      // endDate:FilterSearchForm.endDate,
      status: FilterSearchForm.status.toString(),
      supplierCategory: FilterSearchForm.supplier.toString(),
    };
    doAjax(
      `/${destination_Admin}/broadcastManagement/filter/broadcast`,
      "post",
      hSuccess,
      hError,
      data
    );
  };
  const presentDate = new Date();
  const backDate = new Date();
  backDate.setDate(backDate.getDate() - 8);
  // const userData = useSelector((state) => state.userManagement.userData);

  const [downloadError, setdownloadError] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };
  const PresetMethod = (data) => {
    // dispatch(
    //   commonFilterUpdate({
    //     module: "BroadcastHome",
    //     filterData: {
    //       id: data.requestId || "",
    //       supplier: data.vendor || [],
    //       status: data.poStatus || [],
    //       // startFromDate: moment(data.startDate, "YYYY-MM-DD").toDate(),
    //       // startToDate: moment(data.endDate, "YYYY-MM-DD").toDate(),
    //       // endFromDate: moment(data.startPostedDate, "YYYY-MM-DD").toDate(),
    //       // endToDate: moment(data.endPostedDate, "YYYY-MM-DD").toDate(),
    //       startDate:[moment(data.startDate, "YYYY-MM-DD").toDate(),moment(data.endDate, "YYYY-MM-DD").toDate()],
    //       endDate:[moment(data.startPostedDate, "YYYY-MM-DD").toDate(), moment(data.endPostedDate, "YYYY-MM-DD").toDate()],
    //       category: data.invoicingType || [],
    //     },
    //   })
    // );
  };
  // const PresetObj = [
  //   { name: "requestId", value: FilterSearchForm.id },
  //   { name: "vendor", value: FilterSearchForm.supplier },
  //   { name: "poStatus", value: FilterSearchForm.status },
  //   {
  //     name: "fromDate",
  //     value: moment(FilterSearchForm.startDate[0]).format("YYYY-MM-DD"),
  //   },
  //   {
  //     name: "toDate",
  //     value: moment(FilterSearchForm.startDate[1]).format("YYYY-MM-DD"),
  //   },
  //   {
  //     name: "startPostedDate",
  //     value: moment(FilterSearchForm.endDate[0]).format("YYYY-MM-DD"),
  //   },
  //   {
  //     name: "endPostedDate",
  //     value: moment(FilterSearchForm.endDate[1]).format("YYYY-MM-DD"),
  //   },
  //   { name: "broadcastCategory", value: FilterSearchForm.category },
  // ];

  return (
    <>
      <div className="printScreen" id={"container_outermost"}>
        {/* <NoDataDialog DataRows={BroadcastRows} /> */}
        {downloadError && (
          <ReusableDialog
            dialogState={openMessageDialog}
            openReusableDialog={handleMessageDialogClickOpen}
            closeReusableDialog={handleMessageDialogClose}
            dialogTitle={messageDialogTitle}
            dialogMessage={messageDialogMessage}
            handleDialogConfirm={handleMessageDialogClose}
            // showOkButton={true}
            dialogOkText={"OK"}
            dialogSeverity={messageDialogSeverity}
          />
        )}
        <ReusableSnackBar
          openSnackBar={openSnackbar}
          alertMsg={`Banner uploaded Successfully`}
          handleSnackBarClose={handleSnackbarClose}
        />

        <div
          className="ServiceRequest"
          style={{
            ...outermostContainer,
            margin: "0rem 0rem",
            backgroundColor: "#FAFCFF",
          }}
        >
          <ManageBanner
            open={openModal}
            handleOpen={handleOpenModal}
            handleClose={handleCloseModal}
            handleAttachmentsSubmit={handleAttachmentsSubmit}
            uploadedFiles={uploadedFiles}
            skeleton={skeleton}
          />
          <Stack>
            {/* Information */}
            <Grid container mt={0} sx={outermostContainer_Information}>
              <Grid item md={5} xs={12}>
                <Typography variant="h3">
                  <strong>Broadcast Management</strong>
                </Typography>
                <Typography variant="body2" color="#777">
                  This view displays the list of Broadcasts
                </Typography>
              </Grid>
              <Grid
                item
                md={7}
                xs={12}
                sx={{
                  display: "flex",
                }}
              >
                <Grid
                  container
                  direction="row"
                  justifyContent="flex-end"
                  alignItems="center"
                  spacing={0}
                  mt={0}
                >
                  {/* <SearchBar
                    title="Search for titles separated by comma"
                    message={"Search Titles"}
                    handleSearchAction={handleSearchBar}
                    query={title}
                    setQuery={setTitle}
                    clearSearchBar={clearSearchBar}
                    module="BroadcastHome"
                    keyName="title"
                  /> */}

                  <Tooltip title="Reload">
                    <IconButton
                      sx={{ ...iconButton_SpacingSmall }}
                      onClick={() => getAllBroadcast()}
                    >
                      <Refresh />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Export Table">
                    <IconButton sx={{ ...iconButton_SpacingSmall }}>
                      <IosShare />
                    </IconButton>
                  </Tooltip>
                </Grid>
              </Grid>
            </Grid>
            {/* FILTER/SEARCH */}
            {/* <MyErrorBoundary fallback="Filter Crashed"> */}
                
            <BroadcastFilter
              handleSearch={handleSearch}
              setIsLoading={setIsLoading}
              setBroadcastRows={setBroadcastRows}
              setTitle={setTitle}
              PresetMethod={PresetMethod}
              // PresetObj={PresetObj}
            />
{/* </MyErrorBoundary> */}
            {/* TABLE BLOCK */}
            {/* <MyErrorBoundary fallback="Table Crashed"> */}
            <Grid container sx={container_table}>
              <Grid item md={12}>
                <ReusableTable
                  width="100%"
                  title={`List of Broadcasts (${BroadcastRows.length})`}
                  rows={BroadcastRows}
                  columns={columns}
                  hideFooter={false}
                  getRowIdValue={"broadcastId"}
                  url_onRowClick={`viewBroadcast/`}
                  status_onRowDoubleClick={true}
                  //   module = {'asn'}
                  isLoading={isLoading}
                  //   checkboxSelection={true}
                  disableSelectionOnClick={true}
                  //   onRowsSelectionHandler={onRowsSelectionHandler}
                />
              </Grid>
            </Grid>
            {/* </MyErrorBoundary> */}
            <Paper
              sx={{ position: "fixed", bottom: 0, left: 0, right: 0,zIndex:1 }}
              elevation={2}
            >
              <BottomNavigation
               className="container_BottomNav"
                showLabels
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                
                }}
              >
                <Button
                  size="small"
                  variant="outlined"
                  onClick={handleOpenModal}
                  className='btn-mr'
                  startIcon={<AddPhotoAlternateOutlinedIcon />}
                >
                  Manage Banner
                </Button>
                <Button
                  size="small"
                  variant="contained"
                  onClick={() => navigate("newBroadcast")}
                  
                  startIcon={<AddOutlinedIcon />}
                >
                  New Broadcast
                </Button>
              </BottomNavigation>
            </Paper>
          </Stack>
        </div>
      </div>
    </>
  );
}

export default BroadcastHome;
