import { container_Padding } from '@components/Common/commonStyles';
import { Box, Grid, Stack, Typography, Tooltip } from '@mui/material';
import React from 'react'
import { useSelector } from 'react-redux';
import PreviewAttachment from './PreviewAttachment';

const PreviewPage = ({ children }) => {
  const requestDetails = useSelector((state) => state.request.requestHeader);
  const requestHeaderDetails = useSelector(
    (state) => state.tabsData.requestHeaderData
  );
  const initialPayload = useSelector((state) => state.payload.payloadData);

  return (

    <Stack spacing={2}>
      {Object.entries(requestHeaderDetails).map(([key, fields]) => (
        <Grid
          item
          md={12}
          key={key}
          sx={{
            backgroundColor: "white",
            borderRadius: "8px",
            border: "1px solid #E0E0E0",
            boxShadow: "0px 1px 4px rgba(0, 0, 0, 0.1)", // Softer shadow
            // padding: 1, // Minimized padding for tighter layout
            ...container_Padding,
            pt: '10px'
          }}
        >
          {/* Section Title */}
          <Typography
            sx={{
              fontWeight: "bold",
              mb: "6px",
            }}
          >
            Request Details
          </Typography>

          {/* Fields Container */}
          <Box
            sx={{
              backgroundColor: "#FAFAFA",
              padding: "10px",
              pl: '0px',
              pr: '0px',
              borderRadius: "8px",
              boxShadow: "none",
            }}
          >
            <Grid container spacing={2}>
              {fields
                .filter((field) => field.visibility !== "Hidden")
                .sort((a, b) => a.sequenceNo - b.sequenceNo)
                .map((innerItem) => {
                  let value =
                    requestDetails?.[innerItem?.jsonName] ||
                    initialPayload?.[innerItem?.jsonName] ||
                    "";
                  let formattedValue = "";

                  if (Array.isArray(value)) {
                    formattedValue = value.join(", ");
                  } else if (value instanceof Date || (typeof value === "object" && value instanceof Object && value.toString().includes("GMT"))) {
                    formattedValue = new Date(value).toLocaleString(); // Converts Date to readable format
                  } else {
                    formattedValue = value;
                  }
                  value = formattedValue;
                  return (
                    value && value !== null &&
                    value !== "" && (
                      <Grid item md={3} key={innerItem?.id}>
                        <div
                          style={{
                            padding: "12px",
                            backgroundColor: "#ffffff",
                            borderRadius: "8px",
                            boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
                            // margin: "16px 0",
                            transition: "all 0.3s ease",
                          }}
                        >
                          {/* Field Name with Tooltip */}
                          <Tooltip title={innerItem?.fieldName || "Field Name"}>
                            <Typography
                              variant="body1"
                              sx={{
                                whiteSpace: "nowrap",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                maxWidth: "100%",
                                fontWeight: 600,
                                fontSize: "12px",
                                marginBottom: "4px",
                                display: "flex",
                                alignItems: "center",
                              }}
                            >
                              {innerItem?.fieldName || "Field Name"}
                              {(innerItem?.visibility === "Required" ||
                                innerItem?.visibility === "MANDATORY") && (
                                  <span
                                    style={{ color: "#d32f2f", marginLeft: "2px" }}
                                  >
                                    *
                                  </span>
                                )}
                            </Typography>
                          </Tooltip>

                          {/* Value with Tooltip */}
                          <Tooltip title={value || "--"}>
                            <div
                              style={{
                                fontSize: "0.8rem",
                                color: "#333333",
                                marginTop: "4px",
                                whiteSpace: "nowrap",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                maxWidth: "100%",
                              }}
                            >
                              <span
                                style={{
                                  fontWeight: 500,
                                  color: "grey",
                                  letterSpacing: "0.5px",
                                  wordSpacing: "1px",
                                }}
                              >
                                {value || "--"}
                              </span>
                            </div>
                          </Tooltip>
                        </div>
                      </Grid>
                    )
                  );
                })}
            </Grid>
          </Box>
        </Grid>
      ))}
      <Grid>
        {children}
      </Grid>
      <Grid>
        <PreviewAttachment />
      </Grid>


    </Stack>


  )
}

export default PreviewPage