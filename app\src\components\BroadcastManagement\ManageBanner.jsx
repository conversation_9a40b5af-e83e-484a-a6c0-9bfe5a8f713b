import * as React from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Modal from '@mui/material/Modal';
import FileDropZone from './FileDropZone';
import { useSelector } from 'react-redux';
import { Skeleton, Stack } from '@mui/material';
const style = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 700,
  bgcolor: 'background.paper',
 height:370,  
  p: 4,
};

const ManageBanner = ({open,handleOpen,handleClose,handleAttachmentsSubmit,uploadedFiles,skeleton}) => {
  
  //   const FilterSearchForm = useSelector(
  //   (state) => state.commonFilter["Broadcast"]
  // );
   
  
    
      return (
        <div>
         
          <Modal
            open={open}
            onClose={handleClose}
            aria-labelledby="modal-modal-title"
            aria-describedby="modal-modal-description"
          >
            <Box sx={style}>
            {skeleton ? <><Stack direction='row' spacing={3}>
               <Skeleton animation="pulse" variant="rectangular" width={400} height={300}/>
               <Stack spacing={2}>
               <Skeleton animation="pulse" variant="rectangular" width={200} height={60}/>
               <Skeleton animation="pulse" variant="rectangular" width={200} height={60}/>
               <Skeleton animation="pulse" variant="rectangular" width={200} height={60}/>
               <Skeleton animation="pulse" variant="rectangular" width={200} height={60}/>

               </Stack>
               </Stack> 
               </>:

             <FileDropZone  
             uploadedFiles={uploadedFiles}
              handleAttachmentsSubmit={handleAttachmentsSubmit}
              // TempFiles={TempFiles}
              closeModal={handleClose}
              />}
            </Box>
          </Modal>
        </div>
      );
    }
  

export default ManageBanner