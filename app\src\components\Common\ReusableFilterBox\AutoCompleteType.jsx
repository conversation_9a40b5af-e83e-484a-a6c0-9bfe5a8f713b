import React from "react";
import {
  <PERSON><PERSON>ple<PERSON>,
  <PERSON>rid,
  <PERSON>ack,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Typo<PERSON>,
} from "@mui/material";
import {
  setDisplayFields,
  setRequiredFields,
  updateMaterialData,
} from "../../../app/payloadslice";
import { useDispatch, useSelector } from "react-redux";
import { useEffect, useState } from "react";
import {
  VISIBILITY_TYPE,
  DROP_DOWN_ITEM_SIZE,
  FIELD_VISIBILITY,
  CHANGE_LOG_STATUSES,
  REGION_CODE,
} from "@constant/enum";
import { useChangeLogUpdate } from "@hooks/useChangeLogUpdate";
import { useLocation } from "react-router-dom";
import { FixedSizeList } from "react-window";
import { colors } from "@constant/colors";
import { DIRECT_SYN_TEMP, TEMPLATE_KEYS } from "@constant/changeTemplates";
import SkeletonWithFallback from "@components/Common/ui/SkeletonWithFallback";

const LISTBOX_PADDING = 2; // px

const OuterElementContext = React.createContext({});

const OuterElementType = React.forwardRef((props, ref) => {
  const outerProps = React.useContext(OuterElementContext);
  return <div ref={ref} {...props} {...outerProps} />;
});

const ListboxComponent = React.forwardRef(function ListboxComponent(
  props,
  ref
) {
  const { children, ...other } = props;
  const itemData = [];
  children.forEach((item) => {
    itemData.push(item);
  });

  const itemCount = itemData.length;
  const itemSize = DROP_DOWN_ITEM_SIZE.HEIGHT;

  const getHeight = () => {
    if (itemCount > 8) {
      return 8 * itemSize;
    }
    return itemData.length * itemSize;
  };

  return (
    <div ref={ref}>
      <OuterElementContext.Provider value={other}>
        <FixedSizeList
          itemData={itemData}
          height={getHeight() + 2 * LISTBOX_PADDING}
          width="100%"
          outerElementType={OuterElementType}
          innerElementType="ul"
          itemSize={itemSize}
          overscanCount={5}
          itemCount={itemCount}
        >
          {({ data, index, style }) => {
            const dataSet = data[index];
            const inlineStyle = {
              ...style,
              top: style.top + LISTBOX_PADDING,
            };

            return (
              <li style={{ ...inlineStyle, listStyle: "none" }}>{dataSet}</li>
            );
          }}
        </FixedSizeList>
      </OuterElementContext.Provider>
    </div>
  );
});

const isRegionOptionDisabled = (keyName, userRoles, option, valueFromPayload) => {
  // if (keyName === "TemplateName") {
  //   if (valueFromPayload?.payloadData?.Region === REGION_CODE?.EUR && option?.code === TEMPLATE_KEYS?.WARE_VIEW_2) {
  //     return true;
  //   }
  //   return (
  //     valueFromPayload?.payloadData?.DirectAllowed !== "X" &&
  //     DIRECT_SYN_TEMP?.includes(option?.code)
  //   );
  // }

  if (keyName !== "Region") return false;

  const code = option?.code?.toUpperCase();
  const hasUSAccess = userRoles.some((role) =>
    role.includes("CA-MDG-MRKTNG-US")
  );
  const hasEURAccess = userRoles.some(
    (role) =>
      role.includes("CA-MDG-MRKTNG-FERT-EUR") ||
      role.includes("CA-MDG-MRKTNG-SALES-EUR")
  );
  const hasAllAccess = hasUSAccess && hasEURAccess;

  if (hasAllAccess) return false;
  if (code === "US") return !hasUSAccess;
  if (code === "EUR") return !hasEURAccess;

  return true;
};


export default function AutoCompleteType(props) {
  const dispatch = useDispatch();
  const { updateChangeLog } = useChangeLogUpdate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const requestId = queryParams.get("RequestId");
  const initialPayload = useSelector((state) => state.payload.payloadData);
  const SAPview = location.pathname.includes("DisplayMaterialSAPView");
  let userRoles = useSelector((state) => state.userManagement.roles);
  

  const valueFromPayload = useSelector((state) => state.payload || {});
  const dropDownData = useSelector(
    (state) => state.AllDropDown?.dropDown || {}
  );
  const errorFields = useSelector((state) => state.payload?.errorFields || []);
  const [value, setValue] = useState(null);

  const initialFieldValue =
    valueFromPayload?.[props?.materialID]?.payloadData?.[props?.viewName]?.[
      props?.plantData
    ]?.[props?.keyName] ??
    valueFromPayload?.payloadData?.[props?.keyName] ??
      ((props?.details?.fieldPriority === 'ApplyBus' || props?.isRequestHeader) ? props?.details?.value :
    null);
  useEffect(() => {
    if (
      props?.details?.visibility === VISIBILITY_TYPE.MANDATORY ||
      props?.details?.visibility === "Required"
    ) {
      dispatch(setRequiredFields(props?.keyName || ""));
    }
    if (props?.details?.visibility === VISIBILITY_TYPE.DISPLAY) {
      dispatch(setDisplayFields(props?.keyName));
    }
  }, [dispatch, props?.details?.visibility, props?.keyName]);

  useEffect(() => {
    if (
      initialFieldValue !== null &&
      initialFieldValue !== undefined &&
      initialFieldValue !== ""
    ) {
      if (initialFieldValue?.code) {
        setValue(initialFieldValue);
        updateMaterialData({
          materialID: props?.materialID || "",
          keyName: props?.keyName || "",
          data: initialFieldValue,
          viewID: props?.viewName,
          itemID: props?.plantData,
        });
      } else {
        if (dropDownData?.[props?.keyName] || dropDownData?.[props?.keyName]?.[props?.plantData]) {
          if (!Array.isArray(dropDownData?.[props?.keyName]) && !Array.isArray(dropDownData?.[props?.keyName]?.[props?.plantData])) {
            setValue(null);
            return;
          }
          const matchedItem = dropDownData[props?.keyName]?.length ? dropDownData[props?.keyName]?.find(
            (item) =>
              item?.code?.trim() === initialFieldValue?.toString()?.trim()
          ) : dropDownData[props?.keyName]?.[props?.plantData]?.find(
            (item) =>
              item?.code?.trim() === initialFieldValue?.toString()?.trim()
          );
          if (matchedItem) {
            setValue({ code: matchedItem?.code, desc: matchedItem?.desc });
            dispatch(
              updateMaterialData({
                materialID: props?.materialID || "",
                keyName: props?.keyName || "",
                data:
                  { code: matchedItem?.code, desc: matchedItem?.desc } ?? null,
                viewID: props?.viewName,
                itemID: props?.plantData,
              })
            );
          } else {
            setValue(null);
            dispatch(
              updateMaterialData({
                materialID: props?.materialID || "",
                keyName: props?.keyName || "",
                data: null,
                viewID: props?.viewName,
                itemID: props?.plantData,
              })
            );
          }
        } else {
          setValue(null);
        }
      }
    } else {
      setValue(null);
    }
  }, [initialFieldValue]);

  const handleChange = (e, newValue) => {
    setValue(newValue);
    dispatch(
      updateMaterialData({
        materialID: props?.materialID || "",
        keyName: props?.keyName || "",
        data: newValue ?? null,
        viewID: props?.viewName,
        itemID: props?.plantData,
      })
    );
    
    {
      requestId && !CHANGE_LOG_STATUSES.includes(initialPayload?.RequestStatus) && updateChangeLog({
        materialID: props?.selectedMaterialNumber,
        viewName: props?.viewName,
        plantData: props?.plantData,
        fieldName: props?.details?.fieldName,
        jsonName: props?.details?.jsonName,
        currentValue: `${newValue?.code}-${newValue?.desc ?? ""}`,
        requestId: requestId,
      });
    }
  };

  return (
    <Grid item md={2} sx={{ marginBottom: "12px !important" }}>
      {props?.details?.visibility === "Hidden" ? null : (
        <Stack>
          {SAPview ? (
            <div
              style={{
                padding: "16px",
                backgroundColor: colors.primary.white,
                borderRadius: "8px",
                boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
                margin: "16px 0",
                transition: "all 0.3s ease",
              }}
            >
              <Typography
                variant="body1"
                style={{
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  maxWidth: "100%",
                  fontWeight: 600,
                  fontSize: "12px",
                  marginBottom: "4px",
                  display: "flex",
                  alignItems: "center",
                }}
                title={props?.details?.fieldName}
              >
                {props?.details?.fieldName || "Field Name"}
                {(props?.details?.visibility === VISIBILITY_TYPE.REQUIRED ||
                  props?.details?.visibility === FIELD_VISIBILITY.MANDATORY) && (   
                  <span
                    style={{
                      color: colors.error.darkRed,
                      marginLeft: "5px",
                      fontSize: "1.1rem",
                    }}
                  >
                    *
                  </span>
                )}
              </Typography>

              <div
                style={{
                  fontSize: "0.8rem",
                  color: colors.black.dark,
                  marginTop: "4px",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  width: "100%",
                  cursor: "pointer",
                }}
              >
                {value?.code || value?.desc ? (
                  <Tooltip
                    title={
                      value?.code
                        ? `${value?.code} - ${value?.desc || ""}`
                        : "--"
                    }
                    arrow
                  >
                    <span>
                      <strong
                        style={{
                          fontWeight: 600,
                          color: colors.secondary.grey,
                          marginRight: "6px",
                          letterSpacing: "0.5px",
                          wordSpacing: "1px",
                        }}
                      >
                        {value?.code}
                      </strong>
                      {value?.desc && (
                        <span
                          style={{
                            fontWeight: 500,
                            color: colors.secondary.grey,
                            letterSpacing: "0.5px",
                            wordSpacing: "1px",
                          }}
                        >
                          - {value?.desc}
                        </span>
                      )}
                    </span>
                  </Tooltip>
                ) : (
                  <SkeletonWithFallback fallback="--" />
                )}
              </div>
            </div>
          ) : (
            <>
              <Typography
                variant="body2"
                color={colors.secondary.grey}
                sx={{
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  maxWidth: "100%",
                }}
                title={props?.details?.fieldName}
              >
                {props?.details?.fieldName || "Field Name"}
                {(props?.details?.visibility === "Required" ||
                  props?.details?.visibility === VISIBILITY_TYPE.MANDATORY) && (
                  <span style={{ color: colors.error.dark }}>*</span>
                )}
              </Typography>
              <Autocomplete
                sx={{
                  height: "31px",
                  "& .MuiAutocomplete-listbox": {
                    padding: 0,
                    "& .MuiAutocomplete-option": {
                      paddingLeft: "16px",
                      paddingTop: "4px",
                      paddingBottom: "4px",
                      justifyContent: "flex-start",
                    },
                  },
                  "& .MuiAutocomplete-option": {
                    display: "flex",
                    alignItems: "center",
                    minHeight: "36px",
                  },
                  "& .MuiInputBase-root.Mui-disabled": {
                    "& > input": {
                      WebkitTextFillColor: colors.black.dark,
                      color: colors.black.dark,
                    },
                    backgroundColor: colors.hover.light,
                  },
                }}
                fullWidth
                disabled={
                  props?.disabled ||
                  props.details?.visibility === VISIBILITY_TYPE.DISPLAY
                }
                size="small"
                value={value}
                onChange={handleChange}
                options={dropDownData?.[props?.keyName]?.length ? dropDownData?.[props?.keyName] : dropDownData?.[props?.keyName]?.[props?.plantData] || []}
                required={
                  props?.details?.visibility === VISIBILITY_TYPE.MANDATORY ||
                  props?.details?.visibility === "Required"
                }
                ListboxComponent={ListboxComponent}
                getOptionLabel={(option) =>
                  option?.desc
                    ? `${option?.code || ""} - ${option?.desc || ""}`
                    : `${option?.code || ""}`
                }
                getOptionDisabled={(option) =>
                  isRegionOptionDisabled(props?.keyName, userRoles, option, valueFromPayload)
                }
                renderOption={(props, option) => (
                  <Typography
                    {...props}
                    component="li"
                    style={{
                      fontSize: 12,
                      padding: "8px 16px",
                      width: "100%",
                      cursor: "pointer",
                      display: "flex",
                      alignItems: "start",
                    }}
                  >
                    <span
                      style={{
                        whiteSpace: "nowrap",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                      }}
                      title={`${option?.code}${
                        option?.desc ? ` - ${option?.desc}` : ""
                      }`}
                    >
                      <strong>{option?.code}</strong>
                      {option?.desc ? ` - ${option?.desc}` : ""}
                    </span>
                  </Typography>
                )}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    variant="outlined"
                    placeholder={
                      props?.disabled ||
                      props.details?.visibility === VISIBILITY_TYPE.DISPLAY
                        ? ""
                        : `SELECT ${
                            props?.details?.fieldName?.toUpperCase() || ""
                          }`
                    }
                    error={errorFields.includes(props?.keyName || "")}
                    InputProps={{
                      ...params.InputProps,
                    }}
                  />
                )}
              />
            </>
          )}
        </Stack>
      )}
    </Grid>
  );
}
