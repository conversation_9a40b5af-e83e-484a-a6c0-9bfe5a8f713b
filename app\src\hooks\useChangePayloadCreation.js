import { useSelector } from 'react-redux';
import { convertToDateFormat } from '@helper/helper';
import { TEMPLATE_KEYS } from '@constant/changeTemplates';

const useChangePayloadCreation = (templateName) => {
    const changeFieldRows = useSelector((state) => state.payload.changeFieldRows);
    const changeLogData = useSelector((state) => state.payload.changeLogData);
    const requestState = useSelector((state) => state.request);
    const payloadData = useSelector((state) => state.payload);
    const dynamicData = useSelector((state) => state.payload.dynamicKeyValues)
    const selectedRows = useSelector((state) => state.payload.selectedRows)
    const template = templateName ? templateName : dynamicData?.templateName
    
    const changePayloadForTemplate = (display) => {
        if( templateName === TEMPLATE_KEYS?.LOGISTIC || 
            dynamicData?.templateName === TEMPLATE_KEYS?.LOGISTIC) {
            const result = getLogisticDataPayload(display)
            return result;
        }
        else if( templateName === TEMPLATE_KEYS?.ITEM_CAT || 
            dynamicData?.templateName === TEMPLATE_KEYS?.ITEM_CAT) {
            const result = getItemCatDataPayload(display)
            return result;
        }
        else if( templateName === TEMPLATE_KEYS?.MRP || 
            dynamicData?.templateName === TEMPLATE_KEYS?.MRP) {
            const result = getMRPDataPayload(display)
            return result;
        }
        else if( templateName === TEMPLATE_KEYS?.UPD_DESC || 
            dynamicData?.templateName === TEMPLATE_KEYS?.UPD_DESC) {
            const result = getUpdDescDataPayload(display)
            return result;
        }
        else if( templateName === TEMPLATE_KEYS?.WARE_VIEW_2 || 
            dynamicData?.templateName === TEMPLATE_KEYS?.WARE_VIEW_2) {
            const result = getWarehouseDataPayload(display)
            return result;
        }
        else if( templateName === TEMPLATE_KEYS?.CHG_STAT || 
            dynamicData?.templateName === TEMPLATE_KEYS?.CHG_STAT) {
            const result = getChangeStatusDataPayload(display)
            return result;
        }
        else if( templateName === TEMPLATE_KEYS?.SET_DNU || 
            dynamicData?.templateName === TEMPLATE_KEYS?.SET_DNU) {
            const result = getSetToDNUDataPayload(display)
            return result;
        }
    };

    const getLogisticDataPayload = (display) => {
    const groupedData = changeFieldRows.reduce((acc, current) => {
        if (selectedRows?.length !== 0 && !selectedRows?.includes(current?.id)) {
            return acc; // Skip processing for this object
        }
        const material = current?.Material;
    
        if (!acc[material]) {
        acc[material] = [];
        }
    
        // Push the current object into the respective Material group
        acc[material].push(current);
    
        return acc;
    }, {});
    
    if(display) {
        const keysToRemove = ["id", "MaterialId", "ClientId", "slNo", "ChangeLogId"];
    
        const result = Object.keys(groupedData).map((material) => {
            const materialGroup = groupedData[material];
            const { MaterialId, ClientId } = materialGroup[materialGroup?.length - 1];
            return {
              MaterialId: MaterialId,
              Material: material,
              Function: "UPD",
              TaskId: dynamicData?.otherPayloadData["TaskId"] || "",
              TaskName: dynamicData?.otherPayloadData["TaskName"] || "",
              creationTime: dynamicData?.otherPayloadData["CreationTime"] || "",
              dueDate: dynamicData?.otherPayloadData["DueDate"] || "",
              IsFirstCreate: false,
              MassEditId: dynamicData?.otherPayloadData["MassEditId"],
              TotalIntermediateTasks: dynamicData?.otherPayloadData["TotalIntermediateTasks"],
              IntermediateTaskCount: dynamicData?.otherPayloadData["IntermediateTaskCount"],
              Toclientdata: {
                ClientId: ClientId,
                Material: material,
                Function: "UPD",
              },
              Touomdata: materialGroup.map((item) => {
                const newItem = { ...item, Function: "UPD" };
                keysToRemove.forEach((key) => delete newItem[key]);
                return newItem;
              }),
              Torequestheaderdata: dynamicData?.requestHeaderData || {},
              Tomaterialerrordata: dynamicData?.errorData[material] || {},
              TemplateName: template,
              changeLogData: changeLogData[material] ? {
                RequestId: changeLogData?.RequestId,
                changeLogId: changeLogData?.changeLogId,
                ...changeLogData[material],  
                }:null,
              ...(dynamicData?.Comments && { Comments: dynamicData?.Comments || "" })
            };
          });
        
        return result;
    }
    else {
        const keysToRemove = ["id", "slNo"];
    
        const result = Object.keys(groupedData).map((material) => ({
            Touomdata: groupedData[material].map((item) => {
            const newItem = { ...item, Function: "UPD" };
            keysToRemove.forEach((key) => delete newItem[key]);
            return newItem;
            }),
            Torequestheaderdata: {
                RequestId: requestState?.requestHeader?.requestId,
                ReqCreatedBy: requestState?.requestHeader?.reqCreatedBy,
                ReqCreatedOn: convertToDateFormat(requestState?.requestHeader?.reqCreatedOn),
                ReqUpdatedOn: convertToDateFormat(requestState?.requestHeader?.reqCreatedOn),
                RequestType: requestState?.requestHeader?.requestType,
                RequestPriority: requestState?.requestHeader?.requestPriority,
                RequestDesc: requestState?.requestHeader?.requestDesc,
                RequestStatus: requestState?.requestHeader?.requestStatus,
                FirstProd: payloadData?.payloadData?.FirstProductionDate || null,
                LaunchDate: payloadData?.payloadData?.LaunchDate || null,
                LeadingCat: requestState?.requestHeader?.leadingCat,
                Division: requestState?.requestHeader?.division,
                Region: requestState?.requestHeader?.region,
                TemplateName: requestState?.requestHeader?.templateName,
                FieldName: requestState?.requestHeader?.fieldName,
            },
            Toclientdata: {
                ClientId: null,
                Function: "UPD"
            },
            Material: material,
            TemplateName: template,
            IsFirstCreate: true,
            Function: "UPD",
            changeLogData: changeLogData[material] ? {
                RequestId: changeLogData?.RequestId,
                changeLogId: changeLogData?.changeLogId,
                ...changeLogData[material],  
            }:null,
            ...(dynamicData?.Comments && { Comments: dynamicData?.Comments || "" })
        }));
        return result;
    }
    
    };
    const getItemCatDataPayload = (display) => {
    const groupedData = changeFieldRows.reduce((acc, current) => {
        if (selectedRows?.length !== 0 && !selectedRows?.includes(current?.id)) {
            return acc; // Skip processing for this object
        }
        const material = current?.Material;
    
        if (!acc[material]) {
        acc[material] = [];
        }
    
        // Push the current object into the respective Material group
        acc[material].push(current);
    
        return acc;
    }, {});
    
    if(display) {
        const keysToRemove = ["id", "MaterialId", "ClientId", "slNo", "ChangeLogId"];
    
        const result = Object.keys(groupedData).map((material) => {
            const materialGroup = groupedData[material];
            const { MaterialId, ClientId } = materialGroup[0];
        
            return {
              MaterialId: MaterialId,
              Material: material,
              Function: "UPD",
              TaskId: dynamicData?.otherPayloadData["TaskId"] || "",
              TaskName: dynamicData?.otherPayloadData["TaskName"] || "",
              creationTime: dynamicData?.otherPayloadData["CreationTime"] || "",
              dueDate: dynamicData?.otherPayloadData["DueDate"] || "",
              IsFirstCreate: false,
              MassEditId: dynamicData?.otherPayloadData["MassEditId"],
              TotalIntermediateTasks: dynamicData?.otherPayloadData["TotalIntermediateTasks"],
              IntermediateTaskCount: dynamicData?.otherPayloadData["IntermediateTaskCount"],
              Tosalesdata: materialGroup.map((item) => {
                const newItem = { ...item, Function: "UPD" };
                keysToRemove.forEach((key) => delete newItem[key]);
                return newItem;
              }),
              Torequestheaderdata: dynamicData?.requestHeaderData || {},
              Tomaterialerrordata: dynamicData?.errorData[material] || {},
              TemplateName: template,
              changeLogData: changeLogData[material] ? {
                RequestId: changeLogData?.RequestId,
                changeLogId: changeLogData?.changeLogId,
                ...changeLogData[material],  
                }:null,
              ...(dynamicData?.Comments && { Comments: dynamicData?.Comments || "" })
            };
          });
        
        return result;
    }
    else {
        const keysToRemove = ["id", "slNo"];
    
        const result = Object.keys(groupedData).map((material) => ({
            Tosalesdata: groupedData[material].map((item) => {
            const newItem = { ...item, Function: "UPD" };
            keysToRemove.forEach((key) => delete newItem[key]);
            return newItem;
            }),
            Torequestheaderdata: {
                RequestId: requestState?.requestHeader?.requestId,
                ReqCreatedBy: requestState?.requestHeader?.reqCreatedBy,
                ReqCreatedOn: convertToDateFormat(requestState?.requestHeader?.reqCreatedOn),
                ReqUpdatedOn: convertToDateFormat(requestState?.requestHeader?.reqCreatedOn),
                RequestType: requestState?.requestHeader?.requestType,
                RequestPriority: requestState?.requestHeader?.requestPriority,
                RequestDesc: requestState?.requestHeader?.requestDesc,
                RequestStatus: requestState?.requestHeader?.requestStatus,
                FirstProd: payloadData?.payloadData?.FirstProductionDate || null,
                LaunchDate: payloadData?.payloadData?.LaunchDate || null,
                LeadingCat: requestState?.requestHeader?.leadingCat,
                Division: requestState?.requestHeader?.division,
                Region: requestState?.requestHeader?.region,
                TemplateName: requestState?.requestHeader?.templateName,
                FieldName: requestState?.requestHeader?.fieldName,
            },
            Material: material,
            TemplateName: template,
            IsFirstCreate: true,
            Function: "UPD",
            changeLogData: changeLogData[material] ? {
                RequestId: changeLogData?.RequestId,
                changeLogId: changeLogData?.changeLogId,
                ...changeLogData[material],  
            }:null,
            ...(dynamicData?.Comments && { Comments: dynamicData?.Comments || "" })
        }));
        
        return result;
    }
    
    };
    const getMRPDataPayload = (display) => {
    if(display) {
        const groupedData = {};
        const clientKeysToRemove = ["id", "slNo", "type", "MaterialId", "Plant", "ChangeLogId"];
        const plantKeysToRemove = ["id", "slNo", "type", "MaterialId", "ChangeLogId"];

        Object.keys(changeFieldRows).forEach((type) => {
            changeFieldRows[type].forEach((item) => {
                const { Material, MaterialId } = item;
                if (!groupedData[Material]) {
                    groupedData[Material] = { Toclientdata: null, Toplantdata: [], MaterialId: MaterialId };
                }

                const newItem = { ...item };

                if (type === "Basic Data" && !groupedData[Material].Toclientdata) {
                    clientKeysToRemove.forEach((key) => delete newItem[key]);
                    groupedData[Material].Toclientdata = newItem;
                } else if (type === "Plant Data") {
                    plantKeysToRemove.forEach((key) => delete newItem[key]);
                    groupedData[Material].Toplantdata.push(newItem);
                }
            });
        });

        // Construct final payload
        const result = Object.keys(groupedData).map((material) => ({
            ...groupedData[material],
            Material: material,
            Function: "UPD",
            TaskId: dynamicData?.otherPayloadData["TaskId"] || "",
            TaskName: dynamicData?.otherPayloadData["TaskName"] || "",
            creationTime: dynamicData?.otherPayloadData["CreationTime"] || "",
            dueDate: dynamicData?.otherPayloadData["DueDate"] || "",
            IsFirstCreate: false,
            MassEditId: dynamicData?.otherPayloadData["MassEditId"],
            TotalIntermediateTasks: dynamicData?.otherPayloadData["TotalIntermediateTasks"],
            IntermediateTaskCount: dynamicData?.otherPayloadData["IntermediateTaskCount"],
            Torequestheaderdata: dynamicData?.requestHeaderData || {},
            Tomaterialerrordata: dynamicData?.errorData[material] || {},
            TemplateName: template,
            changeLogData: changeLogData[material] ? {
                RequestId: changeLogData?.RequestId,
                changeLogId: changeLogData?.changeLogId,
                ...changeLogData[material],  
                }:null,
            ...(dynamicData?.Comments && { Comments: dynamicData?.Comments || "" })
        }));

        return result;
        }
    else {
        const groupedData = {};
        const clientKeysToRemove = ["id", "slNo", "type", "Plant"];
        const plantKeysToRemove = ["id", "slNo", "type"];

        Object.keys(changeFieldRows).forEach((type) => {
            changeFieldRows[type].forEach((item) => {
                const { Material } = item;
                if (!groupedData[Material]) {
                    groupedData[Material] = { Toclientdata: null, Toplantdata: [] };
                }

                const newItem = { ...item };

                if (type === "Basic Data" && !groupedData[Material].Toclientdata) {
                    clientKeysToRemove.forEach((key) => delete newItem[key]);
                    groupedData[Material].Toclientdata = {...newItem, Function: "UPD"};
                } else if (type === "Plant Data") {
                    plantKeysToRemove.forEach((key) => delete newItem[key]);
                    groupedData[Material].Toplantdata.push({...newItem, Function: "UPD"});
                }
            });
        });

        // Construct final payload
        const result = Object.keys(groupedData).map((material) => ({
            ...groupedData[material],
            Torequestheaderdata: {
                RequestId: requestState?.requestHeader?.requestId,
                ReqCreatedBy: requestState?.requestHeader?.reqCreatedBy,
                ReqCreatedOn: convertToDateFormat(requestState?.requestHeader?.reqCreatedOn),
                ReqUpdatedOn: convertToDateFormat(requestState?.requestHeader?.reqCreatedOn),
                RequestType: requestState?.requestHeader?.requestType,
                RequestPriority: requestState?.requestHeader?.requestPriority,
                RequestDesc: requestState?.requestHeader?.requestDesc,
                RequestStatus: requestState?.requestHeader?.requestStatus,
                FirstProd: payloadData?.payloadData?.FirstProductionDate || null,
                LaunchDate: payloadData?.payloadData?.LaunchDate || null,
                LeadingCat: requestState?.requestHeader?.leadingCat,
                Division: requestState?.requestHeader?.division,
                Region: requestState?.requestHeader?.region,
                TemplateName: requestState?.requestHeader?.templateName,
                FieldName: requestState?.requestHeader?.fieldName,
            },
            Material: material,
            TemplateName: requestState?.requestHeader?.templateName,
            IsFirstCreate: true,
            Function: "UPD",
            MassEditId: requestState?.requestHeader?.requestId,
            changeLogData: changeLogData[material] ? {
                RequestId: changeLogData?.RequestId,
                changeLogId: changeLogData?.changeLogId,
                ...changeLogData[material],  
            }:null,
            ...(dynamicData?.Comments && { Comments: dynamicData?.Comments || "" })
        }));

        return result;
        }
    
    };
    const getChangeStatusDataPayload = (display) => {
    if(display) {
        const groupedData = {};
        const basicKeysToRemove = ["id", "slNo", "type", "MaterialId", "Plant", "ChangeLogId"];
        const plantKeysToRemove = ["id", "slNo", "type", "MaterialId", "ChangeLogId"];
        const salesKeysToRemove = ["id", "slNo", "type", "MaterialId", "ChangeLogId"];

        Object.keys(changeFieldRows).forEach((type) => {
            changeFieldRows[type].forEach((item) => {
                const { Material, MaterialId } = item;
                if (!groupedData[Material]) {
                    groupedData[Material] = { Toclientdata: null, Toplantdata: [], Tosalesdata: [], MaterialId: MaterialId };
                }

                const newItem = { ...item };

                if (type === "Basic Data" && !groupedData[Material].Toclientdata) {
                    basicKeysToRemove.forEach((key) => delete newItem[key]);
                    groupedData[Material].Toclientdata = newItem;
                } else if (type === "Plant Data") {
                    plantKeysToRemove.forEach((key) => delete newItem[key]);
                    groupedData[Material].Toplantdata.push(newItem);
                } else if (type === "Sales Data") {
                    salesKeysToRemove.forEach((key) => delete newItem[key]);
                    groupedData[Material].Tosalesdata.push(newItem);
                }
            });
        });
        // Construct final payload
        const result = Object.keys(groupedData).map((material) => ({
            ...groupedData[material],
            Material: material,
            Function: "UPD",
            TaskId: dynamicData?.otherPayloadData["TaskId"] || "",
            TaskName: dynamicData?.otherPayloadData["TaskName"] || "",
            creationTime: dynamicData?.otherPayloadData["CreationTime"] || "",
            dueDate: dynamicData?.otherPayloadData["DueDate"] || "",
            IsFirstCreate: false,
            MassEditId: dynamicData?.otherPayloadData["MassEditId"],
            TotalIntermediateTasks: dynamicData?.otherPayloadData["TotalIntermediateTasks"],
            IntermediateTaskCount: dynamicData?.otherPayloadData["IntermediateTaskCount"],
            Torequestheaderdata: dynamicData?.requestHeaderData || {},
            Tomaterialerrordata: dynamicData?.errorData[material] || {},
            TemplateName: template,
            changeLogData: changeLogData[material] ? {
                RequestId: changeLogData?.RequestId,
                changeLogId: changeLogData?.changeLogId,
                ...changeLogData[material],  
            }:null,
            ...(dynamicData?.Comments && { Comments: dynamicData?.Comments || "" })
        }));
        return result;
        }
    else {
        const groupedData = {};
        const basicKeysToRemove = ["id", "slNo", "type", "Plant"];
        const plantKeysToRemove = ["id", "slNo", "type"];
        const salesKeysToRemove = ["id", "slNo", "type",];

        Object.keys(changeFieldRows).forEach((type) => {
            changeFieldRows[type].forEach((item) => {
                const { Material } = item;
                if (!groupedData[Material]) {
                    groupedData[Material] = { Toclientdata: null, Toplantdata: [], Tosalesdata: [] };
                }

                const newItem = { ...item };

                if (type === "Basic Data" && !groupedData[Material].Toclientdata) {
                    basicKeysToRemove.forEach((key) => delete newItem[key]);
                    groupedData[Material].Toclientdata = {...newItem, Function: "UPD"};
                } else if (type === "Plant Data") {
                    plantKeysToRemove.forEach((key) => delete newItem[key]);
                    groupedData[Material].Toplantdata.push({...newItem, Function: "UPD"});
                } else if (type === "Sales Data") {
                    salesKeysToRemove.forEach((key) => delete newItem[key]);
                    groupedData[Material].Tosalesdata.push({...newItem, Function: "UPD"});
                }
            });
        });

        // Construct final payload
        const result = Object.keys(groupedData).map((material) => ({
            ...groupedData[material],
            Torequestheaderdata: {
                RequestId: requestState?.requestHeader?.requestId,
                ReqCreatedBy: requestState?.requestHeader?.reqCreatedBy,
                ReqCreatedOn: convertToDateFormat(requestState?.requestHeader?.reqCreatedOn),
                ReqUpdatedOn: convertToDateFormat(requestState?.requestHeader?.reqCreatedOn),
                RequestType: requestState?.requestHeader?.requestType,
                RequestPriority: requestState?.requestHeader?.requestPriority,
                RequestDesc: requestState?.requestHeader?.requestDesc,
                RequestStatus: requestState?.requestHeader?.requestStatus,
                FirstProd: payloadData?.payloadData?.FirstProductionDate || null,
                LaunchDate: payloadData?.payloadData?.LaunchDate || null,
                LeadingCat: requestState?.requestHeader?.leadingCat,
                Division: requestState?.requestHeader?.division,
                Region: requestState?.requestHeader?.region,
                TemplateName: requestState?.requestHeader?.templateName,
                FieldName: requestState?.requestHeader?.fieldName,
            },
            Material: material,
            TemplateName: requestState?.requestHeader?.templateName,
            IsFirstCreate: true,
            Function: "UPD",
            MassEditId: requestState?.requestHeader?.requestId,
            changeLogData: changeLogData[material] ? {
                RequestId: changeLogData?.RequestId,
                changeLogId: changeLogData?.changeLogId,
                ...changeLogData[material],  
            }:null,
            ...(dynamicData?.Comments && { Comments: dynamicData?.Comments || "" })
        }));

        return result;
        }
    
    };
    const getSetToDNUDataPayload = (display) => {
    if(display) {
        const groupedData = {};
        const basicKeysToRemove = ["id", "slNo", "type", "MaterialId", "Plant", "ChangeLogId"];
        const plantKeysToRemove = ["id", "slNo", "type", "MaterialId", "ChangeLogId"];
        const salesKeysToRemove = ["id", "slNo", "type", "MaterialId", "ChangeLogId"];
        const descKeysToRemove = ["id", "slNo", "type", "MaterialId", "ChangeLogId"];

        Object.keys(changeFieldRows).forEach((type) => {
            changeFieldRows[type].forEach((item) => {
                const { Material, MaterialId } = item;
                if (!groupedData[Material]) {
                    groupedData[Material] = { Toclientdata: null, Toplantdata: [], Tosalesdata: [], Tomaterialdescription: [], MaterialId: MaterialId };
                }

                const newItem = { ...item };

                if (type === "Basic Data" && !groupedData[Material].Toclientdata) {
                    basicKeysToRemove.forEach((key) => delete newItem[key]);
                    groupedData[Material].Toclientdata = newItem;
                } else if (type === "Plant Data") {
                    plantKeysToRemove.forEach((key) => delete newItem[key]);
                    groupedData[Material].Toplantdata.push(newItem);
                } else if (type === "Sales Data") {
                    salesKeysToRemove.forEach((key) => delete newItem[key]);
                    groupedData[Material].Tosalesdata.push(newItem);
                } else if (type === "Description") {
                    descKeysToRemove.forEach((key) => delete newItem[key]);
                    groupedData[Material].Tomaterialdescription.push(newItem);
                }
            });
        });

        // Construct final payload
        const result = Object.keys(groupedData).map((material) => ({
            ...groupedData[material],
            Material: material,
            Function: "UPD",
            TaskId: dynamicData?.otherPayloadData["TaskId"] || "",
            TaskName: dynamicData?.otherPayloadData["TaskName"] || "",
            creationTime: dynamicData?.otherPayloadData["CreationTime"] || "",
            dueDate: dynamicData?.otherPayloadData["DueDate"] || "",
            IsFirstCreate: false,
            MassEditId: dynamicData?.otherPayloadData["MassEditId"],
            TotalIntermediateTasks: dynamicData?.otherPayloadData["TotalIntermediateTasks"],
            IntermediateTaskCount: dynamicData?.otherPayloadData["IntermediateTaskCount"],
            Torequestheaderdata: dynamicData?.requestHeaderData || {},
            Tomaterialerrordata: dynamicData?.errorData[material] || {},
            TemplateName: template,
            changeLogData: changeLogData[material] ? {
                RequestId: changeLogData?.RequestId,
                changeLogId: changeLogData?.changeLogId,
                ...changeLogData[material],  
            }:null,
            ...(dynamicData?.Comments && { Comments: dynamicData?.Comments || "" })
        }));

        return result;
        }
    else {
        const groupedData = {};
        const basicKeysToRemove = ["id", "slNo", "type", "Plant"];
        const plantKeysToRemove = ["id", "slNo", "type"];
        const salesKeysToRemove = ["id", "slNo", "type",];
        const descKeysToRemove = ["id", "slNo", "type"];

        Object.keys(changeFieldRows).forEach((type) => {
            changeFieldRows[type].forEach((item) => {
                const { Material } = item;
                if (!groupedData[Material]) {
                    groupedData[Material] = { Toclientdata: null, Toplantdata: [], Tosalesdata: [], Tomaterialdescription: [] };
                }

                const newItem = { ...item };

                if (type === "Basic Data" && !groupedData[Material].Toclientdata) {
                    basicKeysToRemove.forEach((key) => delete newItem[key]);
                    groupedData[Material].Toclientdata = {...newItem, Function: "UPD"};
                } else if (type === "Plant Data") {
                    plantKeysToRemove.forEach((key) => delete newItem[key]);
                    groupedData[Material].Toplantdata.push({...newItem, Function: "UPD"});
                } else if (type === "Sales Data") {
                    salesKeysToRemove.forEach((key) => delete newItem[key]);
                    groupedData[Material].Tosalesdata.push({...newItem, Function: "UPD"});
                } else if (type === "Description") {
                    descKeysToRemove.forEach((key) => delete newItem[key]);
                    groupedData[Material].Tomaterialdescription.push({...newItem, Function: "UPD"});
                }
            });
        });

        // Construct final payload
        const result = Object.keys(groupedData).map((material) => ({
            ...groupedData[material],
            Torequestheaderdata: {
                RequestId: requestState?.requestHeader?.requestId,
                ReqCreatedBy: requestState?.requestHeader?.reqCreatedBy,
                ReqCreatedOn: convertToDateFormat(requestState?.requestHeader?.reqCreatedOn),
                ReqUpdatedOn: convertToDateFormat(requestState?.requestHeader?.reqCreatedOn),
                RequestType: requestState?.requestHeader?.requestType,
                RequestPriority: requestState?.requestHeader?.requestPriority,
                RequestDesc: requestState?.requestHeader?.requestDesc,
                RequestStatus: requestState?.requestHeader?.requestStatus,
                FirstProd: payloadData?.payloadData?.FirstProductionDate || null,
                LaunchDate: payloadData?.payloadData?.LaunchDate || null,
                LeadingCat: requestState?.requestHeader?.leadingCat,
                Division: requestState?.requestHeader?.division,
                Region: requestState?.requestHeader?.region,
                TemplateName: requestState?.requestHeader?.templateName,
                FieldName: requestState?.requestHeader?.fieldName,
            },
            Material: material,
            TemplateName: requestState?.requestHeader?.templateName,
            IsFirstCreate: true,
            Function: "UPD",
            MassEditId: requestState?.requestHeader?.requestId,
            changeLogData: changeLogData[material] ? {
                RequestId: changeLogData?.RequestId,
                changeLogId: changeLogData?.changeLogId,
                ...changeLogData[material],  
            }:null,
            ...(dynamicData?.Comments && { Comments: dynamicData?.Comments || "" })
        }));

        return result;
        }
    
    };
    const getUpdDescDataPayload = (display) => {
    const groupedData = changeFieldRows.reduce((acc, current) => {
        if (selectedRows?.length !== 0 && !selectedRows?.includes(current?.id)) {
            return acc;
        }
        const material = current?.Material;
    
        if (!acc[material]) {
        acc[material] = [];
        }
    
        acc[material].push(current);
        return acc;
    }, {});
    
    if(display) {
        const keysToRemove = ["id", "MaterialId", "ClientId", "slNo", "ChangeLogId"];
    
        const result = Object.keys(groupedData).map((material) => {
            const materialGroup = groupedData[material];
            const { MaterialId, ClientId } = materialGroup[materialGroup?.length - 1];
        
            return {
                MaterialId: MaterialId,
                Material: material,
                Function: "UPD",
                TaskId: dynamicData?.otherPayloadData["TaskId"] || "",
                TaskName: dynamicData?.otherPayloadData["TaskName"] || "",
                creationTime: dynamicData?.otherPayloadData["CreationTime"] || "",
                dueDate: dynamicData?.otherPayloadData["DueDate"] || "",
                IsFirstCreate: false,
                MassEditId: dynamicData?.otherPayloadData["MassEditId"],
                TotalIntermediateTasks: dynamicData?.otherPayloadData["TotalIntermediateTasks"],
                IntermediateTaskCount: dynamicData?.otherPayloadData["IntermediateTaskCount"],
                Toclientdata: {
                ClientId: ClientId,
                Material: material,
                Function: "UPD",
                },
                Tomaterialdescription: materialGroup.map((item) => {
                const newItem = { ...item, Function: "UPD" };
                keysToRemove.forEach((key) => delete newItem[key]);
                return newItem;
                }),
                Torequestheaderdata: dynamicData?.requestHeaderData || {},
                Tomaterialerrordata: dynamicData?.errorData[material] || {},
                TemplateName: template,
                ...(dynamicData?.Comments && { Comments: dynamicData?.Comments || "" }),
                changeLogData: changeLogData[material] ? {
                    RequestId: changeLogData?.RequestId,
                    changeLogId: changeLogData?.changeLogId,
                    ...changeLogData[material],  
                }:null
            };
            });      
        return result;
    }
    else {
        const keysToRemove = ["id", "slNo"];
    
        const result = Object.keys(groupedData).map((material) => ({
            Tomaterialdescription: groupedData[material].map((item) => {
            const newItem = { ...item, Function: "UPD" };
            keysToRemove.forEach((key) => delete newItem[key]);
            return newItem;
            }),
            Torequestheaderdata: {
                RequestId: requestState?.requestHeader?.requestId,
                ReqCreatedBy: requestState?.requestHeader?.reqCreatedBy,
                ReqCreatedOn: convertToDateFormat(requestState?.requestHeader?.reqCreatedOn),
                ReqUpdatedOn: convertToDateFormat(requestState?.requestHeader?.reqCreatedOn),
                RequestType: requestState?.requestHeader?.requestType,
                RequestPriority: requestState?.requestHeader?.requestPriority,
                RequestDesc: requestState?.requestHeader?.requestDesc,
                RequestStatus: requestState?.requestHeader?.requestStatus,
                FirstProd: payloadData?.payloadData?.FirstProductionDate || null,
                LaunchDate: payloadData?.payloadData?.LaunchDate || null,
                LeadingCat: requestState?.requestHeader?.leadingCat,
                Division: requestState?.requestHeader?.division,
                Region: requestState?.requestHeader?.region,
                TemplateName: requestState?.requestHeader?.templateName,
                FieldName: requestState?.requestHeader?.fieldName,
            },
            Toclientdata: {
                ClientId: null,
                Function: "UPD"
            },
            Material: material,
            TemplateName: template,
            IsFirstCreate: true,
            Function: "UPD",
            ...(dynamicData?.Comments && { Comments: dynamicData?.Comments || "" }),
            changeLogData: changeLogData[material] ? {
                RequestId: changeLogData?.RequestId,
                changeLogId: changeLogData?.changeLogId,
                ...changeLogData[material],  
            }:null
        }));
        return result;
    }
    
    };
    const getWarehouseDataPayload = (display) => {
    const groupedData = changeFieldRows.reduce((acc, current) => {
        if (selectedRows?.length !== 0 && !selectedRows?.includes(current?.id)) {
            return acc;
        }
        const material = current?.Material;
    
        if (!acc[material]) {
        acc[material] = [];
        }
    
        acc[material].push(current);
    
        return acc;
    }, {});
    
    if(display) {
        const keysToRemove = ["id", "MaterialId", "ClientId", "slNo", "ChangeLogId"];
    
        const result = Object.keys(groupedData).map((material) => {
            const materialGroup = groupedData[material];
            const { MaterialId } = materialGroup[0];
        
            return {
                MaterialId: MaterialId,
                Material: material,
                Function: "UPD",
                TaskId: dynamicData?.otherPayloadData["TaskId"] || "",
                TaskName: dynamicData?.otherPayloadData["TaskName"] || "",
                creationTime: dynamicData?.otherPayloadData["CreationTime"] || "",
                dueDate: dynamicData?.otherPayloadData["DueDate"] || "",
                IsFirstCreate: false,
                MassEditId: dynamicData?.otherPayloadData["MassEditId"],
                TotalIntermediateTasks: dynamicData?.otherPayloadData["TotalIntermediateTasks"],
                IntermediateTaskCount: dynamicData?.otherPayloadData["IntermediateTaskCount"],
                Towarehousedata: materialGroup.map((item) => {
                const newItem = { ...item, Function: "UPD" };
                keysToRemove.forEach((key) => delete newItem[key]);
                return newItem;
                }),
                Torequestheaderdata: dynamicData?.requestHeaderData || {},
                Tomaterialerrordata: dynamicData?.errorData[material] || {},
                TemplateName: template,
                changeLogData: changeLogData[material] ? {
                    RequestId: changeLogData?.RequestId,
                    changeLogId: changeLogData?.changeLogId,
                    ...changeLogData[material],  
                }:null,
                ...(dynamicData?.Comments && { Comments: dynamicData?.Comments || "" })
            };
            });
        
        return result;
    }
    else {
        const keysToRemove = ["id", "slNo"];
    
        const result = Object.keys(groupedData).map((material) => ({
            Towarehousedata: groupedData[material].map((item) => {
            const newItem = { ...item, Function: "UPD" };
            keysToRemove.forEach((key) => delete newItem[key]);
            return newItem;
            }),
            Torequestheaderdata: {
                RequestId: requestState?.requestHeader?.requestId,
                ReqCreatedBy: requestState?.requestHeader?.reqCreatedBy,
                ReqCreatedOn: convertToDateFormat(requestState?.requestHeader?.reqCreatedOn),
                ReqUpdatedOn: convertToDateFormat(requestState?.requestHeader?.reqCreatedOn),
                RequestType: requestState?.requestHeader?.requestType,
                RequestPriority: requestState?.requestHeader?.requestPriority,
                RequestDesc: requestState?.requestHeader?.requestDesc,
                RequestStatus: requestState?.requestHeader?.requestStatus,
                FirstProd: payloadData?.payloadData?.FirstProductionDate || null,
                LaunchDate: payloadData?.payloadData?.LaunchDate || null,
                LeadingCat: requestState?.requestHeader?.leadingCat,
                Division: requestState?.requestHeader?.division,
                Region: requestState?.requestHeader?.region,
                TemplateName: requestState?.requestHeader?.templateName,
                FieldName: requestState?.requestHeader?.fieldName,
            },
            Material: material,
            TemplateName: template,
            IsFirstCreate: true,
            Function: "UPD",
            ...(dynamicData?.Comments && { Comments: dynamicData?.Comments || "" }),
            changeLogData: changeLogData[material] ? {
                RequestId: changeLogData?.RequestId,
                changeLogId: changeLogData?.changeLogId,
                ...changeLogData[material],  
            }:null
        }));
        return result;
    }
    
    };
    
    return { changePayloadForTemplate };
}

export default useChangePayloadCreation