import React, { Component } from "react";
import { Box, Button, Typography, Collapse } from "@mui/material";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import { ERROR_MESSAGES } from "@constant/enum";
import { colors } from "@constant/colors";
class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null, showDetails: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({ errorInfo });
    console.error("Error caught by ErrorBoundary:", error, errorInfo);
  }

  handleReload = () => {
    window.history.back();
    setTimeout(() => window.location.reload(), 200);
  };

  toggleDetails = () => {
    this.setState((prevState) => ({ showDetails: !prevState.showDetails }));
  };

  render() {
    if (this.state.hasError) {
      return (
        <Box
          sx={{
            height: "100vh",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            textAlign: "center",
            backgroundColor: colors?.black?.lightGrey,
            color: colors?.black?.light,
            padding: "20px",
            position: "relative",
            overflow: "hidden",
            backgroundImage: `url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 500 500"><circle cx="100" cy="100" r="80" fill="%23FFCDD2"/><circle cx="400" cy="350" r="60" fill="%23BBDEFB"/><rect x="175" y="200" width="150" height="100" rx="15" fill="%23E0E0E0"/></svg>')`,
            backgroundRepeat: "no-repeat",
            backgroundSize: "cover",
            backgroundPosition: "center",
          }}
        >
          <ErrorOutlineIcon sx={{ fontSize: 80, color: colors?.error?.red, marginBottom: "10px" }} />

          <Typography variant="h4" sx={{ fontWeight: "bold", marginBottom: "10px" }}>
          {ERROR_MESSAGES.WENT_WRONG}
          </Typography>

          <Typography variant="body1" sx={{ maxWidth: "500px", marginBottom: "20px", color: colors?.primary?.deepDark }}>
            {ERROR_MESSAGES.UNEXPECTED_ERROR}
          </Typography>

          <Button
            variant="contained"
            color="primary"
            onClick={this.handleReload}
            sx={{ textTransform: "none", marginBottom: "10px" }}
          >
            Go Back & Reload 🔄
          </Button>

          {process.env.NODE_ENV === "development" && this.state.error && (
            <Button
              variant="outlined"
              color="error"
              onClick={this.toggleDetails}
              sx={{ textTransform: "none", marginBottom: "10px" }}
            >
              {this.state.showDetails ? "Hide Details" : "Show Details"}
            </Button>
          )}

          <Collapse in={this.state.showDetails}>
            <Box
              sx={{
                marginTop: "20px",
                padding: "15px",
                borderRadius: "8px",
                backgroundColor: colors?.error?.lightPink,
                color: colors?.error?.darkRed,
                textAlign: "left",
                maxWidth: "600px",
                wordWrap: "break-word",
                fontSize: "14px",
                overflowY: "auto", // Enables scrolling
                maxHeight: "300px", // Prevents it from being too long
                border: "1px solid #d32f2f",
                boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
              }}
            >
              <Typography variant="body2">
                <strong>Error:</strong> {this.state.error?.toString()}
              </Typography>
              <Typography variant="body2">
                <strong>Details:</strong> {this.state.errorInfo?.componentStack}
              </Typography>
            </Box>
          </Collapse>
        </Box>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
