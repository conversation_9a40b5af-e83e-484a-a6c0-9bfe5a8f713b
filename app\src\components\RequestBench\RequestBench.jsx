import React from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { DeleteForeverOutlined, ErrorOutline, Refresh, TrackChangesOutlined } from "@mui/icons-material";
import SaveAltIcon from "@mui/icons-material/SaveAlt";
import PreviewIcon from "@mui/icons-material/Preview";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { setChangeFieldRows, setChangeFieldRowsDisplay, setIsSubmitDisabled } from "@app/payloadSlice";
import { clearPaginationData } from "@app/paginationSlice";
import InfoIcon from "@mui/icons-material/Info";
import useLogger from "@hooks/useLogger";
import { Workflow } from "@cw/rds/icons";
import { Button, Checkbox, Grid, Paper, IconButton, Typography, TextField, Box, Tooltip, Accordion, AccordionSummary, AccordionDetails, Chip, ListItemText, tooltipClasses, Autocomplete, FormControlLabel, FormGroup, CircularProgress, Popover, Tabs, Tab, Dialog, DialogTitle, DialogContent, DialogActions } from "@mui/material";
import moment from "moment/moment";
import SelectionSummary from "./SelectionSummary";
import { Stack } from "@mui/system";
import Select from "@mui/material/Select";
import { FormControl, MenuItem } from "@mui/material";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import styled from "@emotion/styled";
import html2canvas from "html2canvas";
import { commonFilterClear, commonFilterUpdate } from "../../app/commonFilterSlice";
import { v4 as uuidv4 } from "uuid";
import SummarizeOutlinedIcon from "@mui/icons-material/SummarizeOutlined";
import { button_Marginleft, button_Outlined, button_Primary, container_filter, container_table, font_Small, iconButton_SpacingSmall, outerContainer_Information, outermostContainer, outermostContainer_Information } from "../Common/commonStyles";
import { DatePicker, DesktopDatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import DateRange from "../Common/DateRangePicker";
import { destination_BankKey, destination_CostCenter, destination_DocumentManagement, destination_GeneralLedger, destination_MaterialMgmt, destination_ProfitCenter } from "../../destinationVariables";
import { doAjax, promiseAjax } from "../Common/fetchService";
import { initialDataUpdate, setIwmMyTask } from "../../app/initialDataSlice";
import ReusableTable from "../Common/ReusableTable";
import { accountingDataTabs, basicDataTabs, mrpDataTabs, purchasingDataTabs, salesDataTabs } from "../../app/tabsDetailsSlice";
import PersonIcon from "@mui/icons-material/Person";
import { setDropDown } from "../../app/dropDownDataSlice";
import { setProfitCenterAddressTab, setProfitCenterBasicDataTab, setProfitCenterCommunicationTab, setProfitCenterCompCodesTab, setProfitCenterHistoryTab, setProfitCenterIndicatorsTab } from "../../app/profitCenterTabsSlice";
import { setCostCenterAddressTab, setCostCenterBasicDataTab, setCostCenterCommunicationTab, setCostCenterControlTab, setCostCenterHistoryTab, setCostCenterTemplatesTab } from "../../app/costCenterTabsSlice";
import { commonSearchBarClear, commonSearchBarUpdate } from "../../app/commonSearchBarSlice";
import { setTaskData } from "../../app/userManagementSlice";
import ReusableIcon from "../Common/ReusableIcon";
import EmailIcon from "@mui/icons-material/Email";
import { checkIwaAccess, exportAsPDF, saveExcel, savePDF } from "../../functions";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ReusableDialogForAllData from "../Common/ReusableDialogForAllData";
import { Timeline, TimelineConnector, TimelineContent, TimelineDot, TimelineItem, TimelineSeparator, timelineItemClasses } from "@mui/lab";
import CalendarMonthOutlinedIcon from "@mui/icons-material/CalendarMonthOutlined";
import AccessTimeFilledRoundedIcon from "@mui/icons-material/AccessTimeFilledRounded";
import { attachments } from "../ConfigCockpit/UserManagement/Data/data";
import jsPDF from "jspdf";
import CustomDialog from "@components/Common/ui/CustomDialog";
import { API_CODE, DELETE_MODAL_BUTTONS_NAME, DIALOUGE_BOX_MESSAGES, LOCAL_STORAGE_KEYS, REQUEST_STATUS, ROLES, ERROR_MESSAGES } from "@constant/enum";
import SearchBar from "../Common/SearchBar";
import { END_POINTS } from "@constant/apiEndPoints";
import SearchIcon from '@mui/icons-material/Search';
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import ClearIcon from '@mui/icons-material/Clear';
import FilterListIcon from '@mui/icons-material/FilterList';
import { colors } from "@constant/colors";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import MaterialDropdown from "@components/Common/ui/dropdown/MaterialDropdown";
import AutoCompleteSimpleDropDown from "@components/Common/ui/dropdown/AutoCompleteSimpleDropDown";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";
import { setLocalStorage } from "@helper/helper";
import BifurcationPopup from "@components/Common/BifurcationPopup";
//import { PDFDocument } from "pdf-lib";


const StyledAccordion = styled(Accordion)(({ theme }) => ({
  marginTop: "0px !important",
  border: `1px solid ${colors.primary.border}`,
  borderRadius: '8px',
  boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
  '&:not(:last-child)': {
    borderBottom: 0,
  },
  '&:before': {
    display: 'none',
  },
}));

const StyledAccordionSummary = styled(AccordionSummary)(({ theme }) => ({
  minHeight: "2rem !important",
  margin: "0px !important",
  backgroundColor: colors.primary.ultraLight,
  borderRadius: '8px 8px 0 0',
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    backgroundColor: `${colors.primary.light}20`,
  },
}));

const StyledTextField = styled(TextField)({
  '& .MuiOutlinedInput-root': {
    borderRadius: '4px',
    '&:hover fieldset': {
      borderColor: colors.primary.main,
    },
  },
});

const FilterContainer = styled(Grid)({
  padding: '0.75rem',
  gap: '0.5rem',
});

const ButtonContainer = styled(Grid)({
  display: 'flex',
  justifyContent: 'flex-end',
  paddingRight: '0.75rem',
  paddingBottom: '0.75rem',
  paddingTop: '0rem',
  gap: '0.5rem',

});

const ActionButton = styled(Button)({
  borderRadius: '4px',
  padding: '4px 12px',
  textTransform: 'none',
  fontSize: '0.875rem',
});

const LabelTypography = styled(Typography)({
  fontSize: '0.75rem',
  color: colors.primary.dark,
  marginBottom: '0.25rem',
  fontWeight: 500,
});

const HtmlTooltip = styled(({ className, ...props }) => <Tooltip {...props} classes={{ popper: className }} />)(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: "#f5f5f9",
    color: "rgba(0, 0, 0, 0.87)",
    maxWidth: 250,
    border: "1px solid #dadde9",
  },
}));
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};
const exportAsPicture = () => {
  const html = document.getElementsByTagName("HTML")[0];
  const body = document.getElementsByTagName("BODY")[0];
  let htmlWidth = html.clientWidth;
  let bodyWidth = body.clientWidth;

  const data = document.getElementById("e-invoice-export"); //CHANGE THIS ID WITH ID OF OUTERMOST DIV CONTAINER
  const newWidth = data.scrollWidth - data.clientWidth;

  if (newWidth > data.clientWidth) {
    htmlWidth += newWidth;
    bodyWidth += newWidth;
  }

  html.style.width = htmlWidth + "px";
  body.style.width = bodyWidth + "px";

  html2canvas(data)
    .then((canvas) => {
      return canvas.toDataURL("image/png", 1.0);
    })
    .then((image) => {
      saveAs(image, "E-InvoiceReport.png"); //CHANGE THE NAME OF THE FILE
      html.style.width = null;
      body.style.width = null;
    });
};

const saveAs = (blob, fileName) => {
  const elem = window.document.createElement("a");
  elem.href = blob;
  elem.download = fileName;
  (document.body || document.documentElement).appendChild(elem);
  if (typeof elem.click === "function") {
    elem.click();
  } else {
    elem.target = "_blank";
    elem.dispatchEvent(
      new MouseEvent("click", {
        view: window,
        bubbles: true,
        cancelable: true,
      })
    );
  }
  URL.revokeObjectURL(elem.href);
  elem.remove();
};

export let getFilter;

const RequestBench = () => {
  const appSettings = useSelector((state) => state.appSettings);
  const dropDownData = useSelector((state) => state.AllDropDown.dropDown);
  let userData = useSelector((state) => state.userManagement.userData);
  let userRoles = useSelector((state) => state.userManagement.roles);
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [clearClicked, setClearClicked] = useState(false);
  const [alertType, setAlertType] = useState("success");
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { customError } = useLogger();
  const ITEM_HEIGHT = 48;
  const ITEM_PADDING_TOP = 8;
  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
        width: 250,
      },
    },
  };
  const MultipleMaterial = useSelector((state) => state.initialData.MultipleMaterial);
  const handleOnClick = (materialNumber) => {
    // setViewDetailpage(true);
    //     dispatch(setHistoryPath({url:window.location.pathname, module:"po workbench"}));

    navigate("/masterDataCockpit/RequestBench/displayMaterialDetail/" + materialNumber);
  };

  const HtmlTooltip = styled(({ className, ...props }) => <Tooltip {...props} classes={{ popper: className }} />)(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
      backgroundColor: "#f5f5f9",

      color: "rgba(0, 0, 0, 0.87)",

      maxWidth: 250,

      border: "1px solid #dadde9",
    },
  }));
  const [Status_ServiceReqForm, setStatus_ServiceReqForm] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [openPopup, setOpenPopup] = useState(false);
  const [apiData,setApiData]=useState([])
  const [selectedReqType, setSelectedReqType] = useState([]);
  const [selectedDivision, setSelectedDivision] = useState([]);
  const [selectedCreatedBy, setSelectedCreatedBy] = useState([]);
  const [createdByOptions, setCreatedByOptions] = useState([]);
  const [selectedMaterial, setSelectedMaterial] = useState([]);
  const [materialOptions, setMaterialOptions] = useState([]);
  const [matInputValue, setMatInputValue] = useState("");
  const [timerId, setTimerId] = useState(null);
  const [isDropDownLoading, setIsDropDownLoading] = useState(false);
  const [value, setValue] = useState(null);
  const [popoverAnchorEl, setPopoverAnchorEl] = useState(null);
  const [popoverContent, setPopoverContent] = useState("");
  const [isPopoverVisible, setIsPopoverVisible] = useState(false);
  const popoverRef = useRef(null);
  const ariaLabel = { "aria-label": "description" };
  const [rmDataRows, setRmDataRows] = useState([]);
  const [UserName, setUserName] = React.useState("");
  const [tableData, setTableData] = useState([...rmDataRows]);
  const [openSnackBaraccept, setOpenSnackBaraccept] = useState(false);
  const [confirmingid, setConfirmingid] = useState("");
  // const [materialNumber, setMaterialNumber] = useState("");
  const [confirmStatus, setConfirmStatus] = useState(true);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [reqCreatedBy, setReqCreatedBy] = useState("");
  const [companyCodeSet, setCompanyCodeSet] = useState([]);
  const [plantCodeSet, setPlantCodeSet] = useState([]);
  const [vendorDetailsSet, setVendorDetailsSet] = useState([]);
  const [taskstatusSet, setTasksttusSet] = useState([]);
  const [disableButton, setDisableButton] = useState(true);
  const [selectedRow, setSelectedRow] = useState([]);
  const [selectedDetails, setSelectedDetails] = useState([]);
  const [downloadError, setdownloadError] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [displayedFields, setDisplayedFields] = useState([]);
  const [filterFieldData, setFilterFieldData] = useState({});
  const [matType, setMatType] = useState([]);
  const [matGroup, setMatGroup] = useState([]);
  const [viewDetailpage, setViewDetailpage] = useState(false);
  const [matNumber, setMatNumber] = useState([]);
  const [reqStatus, setReqStatus] = useState([]);
  const [activeTab, setActiveTab] = useState(0);
  const [attachmentData, setattachmentData] = useState([]);
  const [jsPdfblob, setJsPdfBlob] = useState(null);
  const [pdfBlob, setPdfBlob] = useState(null);
  const [userList, setUserList] = useState([]);
  const [confirmation, setconfirmation] = useState([]);
  const [confirmationText, setConfirmationText] = useState(null);
  const [poHeader, setPoHeader] = useState(null);
  const [roCount, setroCount] = useState(0);
  const [showBtmNav, setShowBtmNav] = useState(false);
  const [opendialog, setOpendialog] = useState(false);
  const [openSnackbarDialog, setOpenSnackbarDialog] = useState(false);
  const [opendialog2, setOpendialog2] = useState(false);
  const [opendialog3, setOpendialog3] = useState(false);
  const [openforwarddialog, setOpenforwarddialog] = useState(false);
  const [rejectInputText, setRejectInputText] = useState("");
  const [acceptInputText, setAcceptInputText] = useState("");
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");
  const [requestBenchType, setRequestBenchType] = useState("Material");
  const [anchorEl_Preset, setAnchorEl] = useState(null);
  const [initialLoad, setinitialLoad] = useState(true);
  const [openAlldataDialog, setOpenAllDataDialog] = useState(false);
  const priortyOptions = ["Low", "Medium", "High"];

  const [displayData, setDisplayData] = useState({});
  const [firstDisplayData, setFirstDisplayData] = useState({});
  const [secondDisplayData, setSecondDisplayData] = useState({});
  const [costCenterCount, setCostCenterCount] = useState([]);
  const [profitCenterCount, setProfitCenterCount] = useState([]);
  const [bankKeyCount, setBankKeyCount] = useState([]);
  const [glCount, setGlCount] = useState([]);
  const [materialCount, setMaterialCount] = useState([]);
  const [isDeleteDialogVisible, setIsDeleteDialogVisible] = useState({ data: {}, isVisible: false });

  const rbSearchForm = useSelector((state) => state.commonFilter["RequestBench"]);

  const dashboardSearchForm = useSelector((state) => state?.commonFilter["Dashboard"]);

  const handleSnackBarOpen = () => {
    setOpenSnackbar(true);
  };

  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };

  const allOptions = ["Create", "Change", "Extend", "Create with Upload", "Change with Upload", "Extend with Upload", "Finance Costing"];

  const names = ["Draft", "Data Entry Pending", "Sent Back By Intermediate User", "Submitted For Review", "Validated-MDM", "Validated-Requestor", "Validation Failed-MDM", "Validation Failed-Requestor", "Rejected", "Sent Back By MDM Team", "Syndicated In SAP", "Syndicated In SAP(Direct)", "Canceled", "Syndication Failed", "Syndication Failed(Direct)", "Syndicated-Partially", "Syndicated-Partially(Direct)", "Upload Failed", "Upload Successful"];

  const tempNames = ["Logistic Data", "MRP Data", "Warehouse View 2", "Item Cat Group", "Set to DNU", "Update Descriptions", "Change Status"];

  const reqStatusString = rbSearchForm?.reqStatus.join("$^$");
  const reqTempNameString = rbSearchForm?.tempName.join("$^$");
  const getRequestStatus = () => {
    const hSuccess = (data) => {
      setReqStatus(data.body);
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getRequestStatus`, "get", hSuccess, hError);
  };
  let tabsArray = [
    "Material",
    // "Cost Center",
    // "Profit Center",
    // "Bank Key",
    // "General Ledger",
  ];
  let tabsArrayHashMap = {
    0: "Material",
    // "1":"Cost Center",
    // "2":"Profit Center",
    // "3":"Bank Key",
    // "4":"General Ledger"
  };
  useEffect(() => {
    if (clearClicked) {
      getFilter();
      setClearClicked(false);
    }
  }, [clearClicked]);
  useEffect(() => {
    getRequestStatus();
    dispatch(setTaskData({}));
    dispatch(setIwmMyTask({}));
    dispatch(setIsSubmitDisabled(true));
    dispatch(clearPaginationData());
    dispatch(setChangeFieldRows([]));
    dispatch(setChangeFieldRowsDisplay({}));
  }, []);
  useEffect(() => setTableData([...rmDataRows]), [rmDataRows]);

  const handleRequestId = (e) => {
    if (e.target.value !== null) {
      var tempRequestId = e.target.value;

      let tempFilterData = {
        ...rbSearchForm,
        requestId: tempRequestId,
      };
      dispatch(
        commonFilterUpdate({
          module: "RequestBench",
          filterData: tempFilterData,
        })
      );
    }
  };

  const handleChildRequestId = (e) => {
    if (e.target.value !== null) {
      var tempRequestId = e.target.value;

      let tempFilterData = {
        ...rbSearchForm,
        childRequestId: tempRequestId,
      };
      dispatch(
        commonFilterUpdate({
          module: "RequestBench",
          filterData: tempFilterData,
        })
      );
    }
  };

  const handlePopoverOpen = (event, content) => {
    setPopoverAnchorEl(event.currentTarget);
    setPopoverContent(content);
    setIsPopoverVisible(true);
  };

  const handlePopoverClose = () => {
    setIsPopoverVisible(false);
  };

  const handleMouseEnterPopover = () => {
    setIsPopoverVisible(true);
  };

  const handleMouseLeavePopover = () => {
    setIsPopoverVisible(false);
  };

  const popoverOpen = Boolean(popoverAnchorEl);
  const popoverId = popoverOpen ? "custom-popover" : undefined;

  const handleStatus = (e) => {
    if (e.target.value !== null) {
      let tempStatus = e.target.value;

      if (tempStatus.includes("Select All")) {
        if (tempStatus.length === names.length + 1) {
          tempStatus = [];
        } else {
          tempStatus = [...names];
        }
      }

      let tempFilterData = {
        ...rbSearchForm,
        reqStatus: tempStatus,
      };

      dispatch(
        commonFilterUpdate({
          module: "RequestBench",
          filterData: tempFilterData,
        })
      );
    }
  };

  const handletempName = (e) => {
    if (e.target.value !== null) {
      let tempName = e.target.value;

      if (tempName.includes("Select All")) {
        if (tempName.length === tempNames.length + 1) {
          tempName = [];
        } else {
          tempName = [...tempNames];
        }
      }

      let tempFilterData = {
        ...rbSearchForm,
        tempName: tempName,
      };

      dispatch(
        commonFilterUpdate({
          module: "RequestBench",
          filterData: tempFilterData,
        })
      );
    }
  };

  const handleCancel = (requestId, requestType) => {
    setLoaderMessage("Request processing please wait.");
    setBlurLoading(true);
    let cancelPayload = {
      requestId: requestId,
      requestType: requestType,
    };

    const hSuccess = (data) => {
      setBlurLoading(false);
      setLoaderMessage("");
      if (data.statusCode === 200) {
        setMessageDialogTitle("Create");
        setMessageDialogMessage(`Request Cancelled `);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
      } else {
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Failed Cancelling Requests");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        //setTestrunStatus(true);
      }
      handleClose();
      getFilter();
      setIsDeleteDialogVisible({ ...isDeleteDialogVisible, isVisible: false });
    };
    const hError = () => {
      setLoaderMessage("");
      setBlurLoading(false);
      setIsDeleteDialogVisible({ ...isDeleteDialogVisible, isVisible: false });
    };

    doAjax(`/${destination_MaterialMgmt}/workflow/cancelWorkflow`, "post", hSuccess, hError, cancelPayload);
  };

  const handleMassCancel = () => {
    if (selectedRows.length > 10) {
      setOpenSnackbar(true);
      setMessageDialogMessage(DIALOUGE_BOX_MESSAGES.MAX_CANCEL_LIMIT);
      setAlertType("warning");
      return;
    }
    setBlurLoading(true);
    const cancelPayload = selectedRows.map(id => {
      const selectedRow = rmDataRows.find(row => row.id === id);
      return {
        requestId: selectedRow.requestId,
        requestType: selectedRow.requestType
      };
    });
  
    const hSuccess = (data) => {
      setBlurLoading(false);
      setLoaderMessage("");
      if (data.statusCode === API_CODE.STATUS_200) {
        setOpenSnackbar(true);
        setMessageDialogMessage(DIALOUGE_BOX_MESSAGES.CANCEL_SUCCESS);
        setAlertType("success");
        setSelectedRows([]);
      } else {
        setOpenSnackbar(true);
        setMessageDialogMessage(DIALOUGE_BOX_MESSAGES.CANCEL_FAILED);
        setAlertType("error");
      }
      getFilter();
    };
  
    const hError = () => {
      setLoaderMessage("");
      setBlurLoading(false);
    };
  
    doAjax(`/${destination_MaterialMgmt}${END_POINTS.WORK_FLOW.CANCEL_WORKFLOWS}`, "post", hSuccess, hError, cancelPayload);
  };

  const handleChangePriority = (event) => {
    const value = event.target.value;
    setSelectedOptions(typeof value === "string" ? value.split(",") : value);
    if (value !== null) {
      var tempPriority = value;

      let tempFilterData = {
        ...rbSearchForm,
        reqPriority: tempPriority,
      };
      dispatch(
        commonFilterUpdate({
          module: "RequestBench",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleRequestType = (e) => {
    if (e.target.value !== null) {
      var tempRequestType = e.target.value;

      let tempFilterData = {
        ...rbSearchForm,
        requestType: tempRequestType,
      };
      dispatch(
        commonFilterUpdate({
          module: "RequestBench",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleCreatedBy = (e) => {
    if (true) {
      var tempCreatedBy = e.target.value;

      let tempFilterData = {
        ...rbSearchForm,
        createdBy: tempCreatedBy,
      };
      dispatch(
        commonFilterUpdate({
          module: "RequestBench",
          filterData: tempFilterData,
        })
      );
    }
  };

  /* Setting Default Dates */
  const presentDate = new Date();
  const backDate = new Date();
  backDate.setDate(backDate.getDate() - 15);
  const [date, setDate] = useState([backDate, presentDate]);
  const [date1, setDate1] = useState([backDate, presentDate]);

  const handleDate = (e) => {
    // if (e !== null) setDate(e.reverse());
    if (e !== null) {
      var createdOn = e;
      dispatch(
        commonFilterUpdate({
          module: "RequestBench",
          filterData: {
            ...rbSearchForm,
            createdOn: createdOn,
          },
        })
      );
    }
  };

  const handleSelectAllDivision = () => {
    if (selectedDivision.length === dropDownData?.reqBenchDivision?.length) {
      setSelectedDivision([]);
      // TO BE USED LATER
      //setselectedPresetDescription([]);
    } else {
      setSelectedDivision(dropDownData?.reqBenchDivision);
    }
  };

  const handleSelectAllCreatedBy = () => {
    if (selectedCreatedBy.length === createdByOptions.length) {
      setSelectedCreatedBy([]);
      // TO BE USED LATER
      //setselectedPresetDescription([]);
    } else {
      setSelectedCreatedBy(createdByOptions);
    }
  };

  const handleSelectAllMateriaL = () => {
    if (selectedMaterial.length === materialOptions.length) {
      setSelectedMaterial([]);
      //TO BE USED LATER
      //setselectedPresetDescription([]);
    } else {
      setSelectedMaterial(materialOptions);
    }
  };

  const handleSelectAllReqType = () => {
    if (selectedReqType.length === allOptions.length) {
      setSelectedReqType([]);
      // TO BE USED LATER
      //setselectedPresetDescription([]);
    } else {
      setSelectedReqType(allOptions);
    }
  };
  const isMaterialSelected = (option) => {
    return selectedMaterial.some((selectedOption) => selectedOption?.desc === option?.desc);
  };

  const isReqTypeSelected = (option) => {
    return selectedReqType.some((selectedOption) => selectedOption === option);
  };
  const isDivisionSelected = (option) => {
    return selectedDivision.some((selectedOption) => selectedOption?.code === option?.code);
  };

  const isCreatedBySelected = (option) => {
    return selectedCreatedBy.some((selectedOption) => selectedOption?.code === option?.code);
  };

  const handleDate1 = (e) => {
    if (e !== null) setDate1(e.reverse());
  };
  // let [rbSearchForm, setrbSearchForm] = useState({
  //   companyCode: "",
  //   vendorNo: "",
  //   paymentStatus: "",
  // });
  //Checked PO rows

  // const clearSearchBar = () => {
  //   setMaterialNumber("");
  // };
  const handleSearchAction = (value) => {
    if (!value) {
      setTableData([...rmDataRows]);
      setCount(materialCount);
      return;
    }
    const selected = rmDataRows.filter((row) => {
      let rowMatched = false;
      let keys = Object.keys(row);

      for (let k = 0; k < keys.length; k++) {
        rowMatched = !row[keys[k]] ? false : row?.[keys?.[k]] && row?.[keys?.[k]].toString().toLowerCase()?.indexOf(value?.toLowerCase()) != -1;

        if (rowMatched) break;
      }
      return rowMatched;
    });

    setTableData([...selected]);
    setCount(selected?.length);
  };

  const handleSnackBarClickaccept = () => {
    setOpenSnackBaraccept(true);
  };

  const handleSnackBarCloseaccept = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }

    setOpenSnackBaraccept(false);
  };

  const handleUserName = (e) => {
    setUserName(e.target.value);
  };
  const [count, setCount] = useState(0);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [skip, setSkip] = useState(0);
  const handlePageChange = (event, newPage) => {
    setPage(isNaN(newPage) ? 0 : newPage);
  };
  const handlePageSizeChange = (event) => {
    const newPageSize = event.target.value;
    setPageSize(newPageSize);
    setPage(0);
  };

  useEffect(() => {
    if (page !== 0) {
      const requiredDataCount = pageSize * (page + 1);
      if (requiredDataCount > rmDataRows.length && rmDataRows.length % pageSize === 0) {
        getFilterBasedOnPagination();
      }
    }
  }, [page]);

  //   if(dashboardSearchForm?.dashBoardModuleName === 'Material'){
  //     setActiveTab(0)
  //   }else if(dashboardSearchForm?.dashBoardModuleName === 'Cost Center'){
  //     setActiveTab(1)
  //   }else if(dashboardSearchForm?.dashBoardModuleName === 'Profit Center'){
  //     setActiveTab(2)
  //   }else if(dashboardSearchForm?.dashBoardModuleName === 'General Ladger'){
  //     setActiveTab(4)
  //   }else if(dashboardSearchForm?.dashBoardModuleName === 'Bank Key'){
  //     setActiveTab(5)
  //   }else{
  //     //setActiveTab(0)
  //   }
  // }, [page, pageSize,costCenterCount,profitCenterCount,]);
  // Get Filter Data

  const getModuleCountData = (fetchSkip) => {
    let payload = {
      fromDate: moment(dashboardSearchForm?.dashboardDate[1] ? dashboardSearchForm?.dashboardDate[1] : rbSearchForm?.createdOn[0]).format("YYYY-MM-DDT00:00:00") ?? "",
      toDate: moment(dashboardSearchForm?.dashboardDate[0] ? dashboardSearchForm?.dashboardDate[0] : rbSearchForm?.createdOn[1]).format("YYYY-MM-DDT00:00:00") ?? "",
      requestId: rbSearchForm?.requestId,
      reqStatus: reqStatusString,
      reqType: rbSearchForm?.requestType,
      createdBy: rbSearchForm?.createdBy,
      top: 1000,
      skip: fetchSkip ?? 0,
      userId: userData?.user_id,
      fetchCount: true,
    };
    const hSuccess1 = (data) => {
      setIsLoading(false);
      setMaterialCount(data?.body?.count);
    };
    const hSuccess2 = (data) => {
      setIsLoading(false);
      console.log(data.body.count, "coming in cost");
      setCostCenterCount(data?.body?.count);
    };
    const hSuccess3 = (data) => {
      setIsLoading(false);
      setProfitCenterCount(data?.body?.count);
    };
    const hSuccess4 = (data) => {
      setIsLoading(false);
      setBankKeyCount(data?.body?.count);
    };
    const hSuccess5 = (data) => {
      setIsLoading(false);
      setGlCount(data?.body?.count);
    };
    const hError5 = (error) => {
      console.log(error);
    };
    const hError4 = (error) => {
      console.log(error);
    };
    const hError2 = (error) => {
      console.log(error);
    };
    const hError3 = (error) => {
      console.log(error);
    };
    const hError1 = (error) => {
      console.log(error);
    };

    tabsArray.forEach((item) => {
      console.log(item, "itemForSearch");
      if (item === "Material") {
        //alert("coming")
        var url = `/${destination_MaterialMgmt}/data/searchForRequestBench`;
        doAjax(url, "post", hSuccess1, hError1, payload);
      } else if (item === "Cost Center") {
        var url = `/${destination_CostCenter}/data/searchForRequestBench`;
        doAjax(url, "post", hSuccess2, hError2, payload);
      } else if (item === "Profit Center") {
        var url = `/${destination_ProfitCenter}/data/searchForRequestBench`;
        doAjax(url, "post", hSuccess3, hError3, payload);
      } else if (item === "Bank Key") {
        var url = `/${destination_BankKey}/data/searchForRequestBench`;
        doAjax(url, "post", hSuccess4, hError4, payload);
      } else if (item === "General Ledger") {
        var url = `/${destination_GeneralLedger}/data/searchForRequestBench`;
        doAjax(url, "post", hSuccess5, hError5, payload);
      } else {
        return false;
      }
    });
  };

  getFilter = (fetchSkip=0, autoRefresh=false) => {
    setIsLoading(true);
    setPage(0);

    let payload = {
      fromDate: moment(rbSearchForm?.createdOn[0] ? rbSearchForm?.createdOn[0] : dashboardSearchForm?.dashboardDate[1]).format("YYYY-MM-DDT00:00:00") ?? "",
      toDate: moment(rbSearchForm?.createdOn[1] ? rbSearchForm?.createdOn[1] : dashboardSearchForm?.dashboardDate[0]).format("YYYY-MM-DDT00:00:00") ?? "",
      requestId: rbSearchForm?.requestId,
      childRequestId: rbSearchForm?.childRequestId,
      requestPriority: rbSearchForm?.reqPriority ? rbSearchForm?.reqPriority.join(",") : "",
      reqStatus: reqStatusString,
      reqType: rbSearchForm?.requestType,
      createdBy: userRoles.includes(`${ROLES.SUPER_USER}`) ? rbSearchForm?.createdBy : userData?.emailId,
      division: rbSearchForm?.division,
      material: rbSearchForm?.number,
      top: pageSize,
      skip: fetchSkip ?? 0,
      userId: userData?.user_id,
      templateName: reqTempNameString,
      fetchCount: false,
    };

    let headers = autoRefresh ? {
      'X-RateLimit-Enabled': 'true'
    } : {}

    if (activeTab === 0) {
      var url = `/${destination_MaterialMgmt}/data/searchForRequestBench`;
    } else if (activeTab === 1) {
      var url = `/${destination_CostCenter}/data/searchForRequestBench`;
    } else if (activeTab === 2) {
      var url = `/${destination_ProfitCenter}/data/searchForRequestBench`;
    } else if (activeTab === 3) {
      var url = `/${destination_BankKey}/data/searchForRequestBench`;
    } else if (activeTab === 4) {
      var url = `/${destination_GeneralLedger}/data/searchForRequestBench`;
    } else {
      return false;
    }
    const hSuccess = (data) => {
      setIsLoading(false);

      if (data?.statusCode === API_CODE?.STATUS_200) {
        dispatch(commonSearchBarClear({ module: "RequestBench" }));
        setMaterialCount(data?.body?.count);
        var rows = [];
        for (let index = 0; index < data?.body?.list.length; index++) {
          var tempObj = data?.body?.list[index];
          if (true) {
            var tempRow = {
              id: tempObj?.id === "" ? tempObj.requestId : tempObj?.id,
              requestId: tempObj?.requestId,
              requestType: tempObj?.requestType,
              createdOn: moment(tempObj.creationDate).format("DD MMM YYYY") ?? "",
              changedOn: moment(tempObj.lastChangeDate).format("DD MMM YYYY") ?? "",
              createdBy: tempObj?.createdByUser,
              reqStatus: tempObj?.reqStatus ? tempObj?.reqStatus : "-",
              salesOrg: tempObj?.salesOrg ? tempObj?.salesOrg : "-",
              distChnl: tempObj?.distChnl ? tempObj?.distChnl : "Not Available",
              plant: tempObj?.plantOrg,
              tempName: tempObj?.templateName,
              requestPriority: tempObj?.requestPriority ? tempObj?.requestPriority : "Not Available",
              material: tempObj["materialNos"].length > 0 ? `${tempObj["materialNos"]}` : "Not Available",
              costCenter: tempObj?.costCenter,
              changedOnAct: tempObj.lastChangeDate,
              childRequestIds: tempObj["childRequestIds"].length > 0 ? `${tempObj["childRequestIds"]}` : "Not Available",
              isBifurcated: tempObj?.bifurcated,
            };
            rows.push(tempRow);
          }
        }
        setRmDataRows(rows);
        setIsLoading(false);
        setroCount(rows.length);
        setCount(data?.body?.count);
      } else if (data?.statusCode === API_CODE?.STATUS_429) {
        setMessageDialogMessage(data?.message);
        setAlertType("error");
        handleSnackBarOpen();
      }
    };
    const hError = (error) => {
      setIsLoading(false);
    };
    doAjax(url, "post", hSuccess, hError, payload, headers);
  };

  const getFilterBasedOnPagination = () => {
    setIsLoading(true);
    let payload = {
      fromDate: moment(rbSearchForm?.createdOn[0] ? rbSearchForm?.createdOn[0] : dashboardSearchForm?.dashboardDate[1]).format("YYYY-MM-DDT00:00:00") ?? "",
      toDate: moment(rbSearchForm?.createdOn[1] ? rbSearchForm?.createdOn[1] : dashboardSearchForm?.dashboardDate[0]).format("YYYY-MM-DDT00:00:00") ?? "",
      requestId: rbSearchForm?.requestId,
      childRequestId: rbSearchForm?.childRequestId,
      requestPriority: rbSearchForm?.reqPriority ? rbSearchForm?.reqPriority.join(",") : "",
      reqStatus: reqStatusString,
      reqType: rbSearchForm?.requestType,
      createdBy: userRoles.includes(`${ROLES.SUPER_USER}`) ? rbSearchForm?.createdBy : userData?.emailId,
      division: rbSearchForm?.division,
      material: rbSearchForm?.number,
      top: pageSize,
      skip: pageSize * page ?? 0,
      templateName: reqTempNameString,
      userId: userData?.user_id,
      fetchCount: false,
    };

    if (activeTab === 0) {
      var url = `/${destination_MaterialMgmt}/data/searchForRequestBench`;
    } else if (activeTab === 1) {
      var url = `/${destination_CostCenter}/data/searchForRequestBench`;
    } else if (activeTab === 2) {
      var url = `/${destination_ProfitCenter}/data/searchForRequestBench`;
    } else if (activeTab === 3) {
      var url = `/${destination_BankKey}/data/searchForRequestBench`;
    } else if (activeTab === 4) {
      var url = `/${destination_GeneralLedger}/data/searchForRequestBench`;
    } else {
      return false;
    }
    const hSuccess = (data) => {
      setIsLoading(false);
      var rows = [];
      for (let index = 0; index < data?.body?.list.length; index++) {
        var tempObj = data?.body?.list[index];
        if (true) {
          var tempRow = {
            id: tempObj?.id === "" ? tempObj.requestId : tempObj?.id,
            requestId: tempObj?.requestId,
            requestType: tempObj?.requestType,
            createdOn: moment(tempObj.creationDate).format("DD MMM YYYY") ?? "",
            changedOn: moment(tempObj.lastChangeDate).format("DD MMM YYYY") ?? "",
            createdBy: tempObj?.createdByUser,
            reqStatus: tempObj?.reqStatus,
            salesOrg: tempObj?.salesOrg,
            distChnl: tempObj?.distChnl,
            childRequestIds: tempObj["childRequestIds"].length > 0 ? `${tempObj["childRequestIds"]}` : "Not Available",
            isBifurcated: tempObj?.bifurcated,
            material: tempObj["materialNos"].length > 0 ? `${tempObj["materialNos"]}` : "Not Available",
            plant: tempObj?.plantOrg,
            requestPriority: tempObj?.requestPriority ? tempObj?.requestPriority : "Not Available",
            controllingArea: tempObj?.controllingArea,
            costCenter: tempObj?.costCenter,
            profitCenter: tempObj?.profitCenter,
            profitCenterName: tempObj?.profitCenterName,
            bankKey: tempObj?.bankKey,
            bankCtryReg: tempObj?.bankCtry,
            tempName: tempObj?.templateName,
            glAccount: tempObj?.glAccount,
            compCode: tempObj?.companyCode,
            chartOfAccount: tempObj?.coa,
          };
          rows.push(tempRow);
        }
      }
      // rows.sort(
      //   (a, b) =>
      //     moment(a.createdOn, "DD MMM YYYY HH:mm") -
      //     moment(b.changedOn, "DD MMM YYYY HH:mm")
      // );
      setRmDataRows((prevRows) => [...prevRows, ...rows]);
      setIsLoading(false);
      //setSkip(pageSize * (page +1));
      //setroCount(rows.length);
      setCount(data?.body?.count);
    };
    const hError = (error) => {
      setIsLoading(false);
      console.log(error);
    };
    doAjax(url, "post", hSuccess, hError, payload);
  };

  const handleTable = () => {
    setIsLoading(true);
    let payload = {
      top: 1000,
      skip: 0,
      userId: userData?.user_id,
    };

    if (activeTab === 0) {
      var url = `/${destination_MaterialMgmt}/data/getItemsForRequestBench`;
    } else if (activeTab === 1) {
      var url = `/${destination_CostCenter}/data/getItemsForRequestBench`;
    } else if (activeTab === 2) {
      var url = `/${destination_ProfitCenter}/data/getItemsForRequestBench`;
    } else if (activeTab === 3) {
      var url = `/${destination_BankKey}/data/getItemsForRequestBench`;
    } else if (activeTab === 4) {
      var url = `/${destination_GeneralLedger}/data/getItemsForRequestBench`;
    } else {
      return false;
    }

    const hSuccess = (data) => {
      var rows = [];
      for (let index = 0; index < data?.body?.list.length; index++) {
        var tempObj = data?.body?.list[index];
        console.log("tempObj", tempObj);
        if (true) {
          var tempRow = {
            id: tempObj?.id,
            requestId: tempObj?.requestId,
            requestType: tempObj?.requestType,
            createdOn: moment(tempObj.creationDate).format("DD MMM YYYY") ?? "",
            changedOn: moment(tempObj.lastChangeDate).format("DD MMM YYYY") ?? "",
            createdBy: tempObj?.createdByUser,
            reqStatus: tempObj?.reqStatus,
            salesOrg: tempObj?.salesOrg,
            requestPriority: tempObj?.requestPriority,
            distChnl: tempObj?.distChnl,
            plantOrg: tempObj?.plantOrg,
            ControllingArea: tempObj?.ControllingArea,
            CostCenter: tempObj?.CostCenter,
            profitCenter: tempObj?.profitCenter,
            profitCenterName: tempObj?.profitCenterName,
          };
          rows.push(tempRow);
        }
      }
      // rows.sort(
      //   (a, b) =>
      //     moment(a.createdOn, "DD MMM YYYY HH:mm") -
      //     moment(b.createdOn, "DD MMM YYYY HH:mm")
      // );
      setRmDataRows(rows);
      setIsLoading(false);
      setroCount(rows.length);
      setCount(data?.body?.count);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(url, "post", hSuccess, hError, payload);
  };

  const handleReject = () => {
    setMessageDialogTitle("Success");
    setMessageDialogMessage("Comment Posted");
    setMessageDialogSeverity("success");
    handleMessageDialogClickOpen();
  };

  const handleAccept = () => {
    setMessageDialogTitle("Success");
    setMessageDialogMessage("Comment Posted");
    setMessageDialogSeverity("success");
    handleMessageDialogClickOpen();
  };

  const handleOpendialog = (id) => {
    setID(id);
    fetchPOHeader(id);
    setOpendialog(true);
  };

  const handleClosedialog = () => {
    setOpendialog(false);
  };
  const handleOpendialog2 = (id) => {
    setID(id);
    fetchPOHeader(id);
    setOpendialog2(true);
  };
  const handleClosedialog2 = () => {
    setOpendialog2(false);
  };

  const handleOpendialog3 = (id) => {
    setOpendialog3(true);
    setConfirmingid(id);
    fetchPOHeader(id);
  };
  const handleClosedialog3 = () => {
    setOpendialog3(false);
  };
  const handleOpenforwarddialog = () => {
    setOpenforwarddialog(true);
  };

  const handleCloseforwarddialog = () => {
    setOpenforwarddialog(false);
  };
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleAllDataDialogOpen = () => {
    setOpenAllDataDialog(true);
  };
  const handleAllDataDialogClose = () => {
    setOpenAllDataDialog(false);
  };

  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
    setOpendialog3(false);
    setOpendialog(false);
    setOpendialog2(false);
  };

  const openAnchor = Boolean(anchorEl_Preset);

  const handleClose_Preset = () => {
    setPresetName("");
    setAnchorEl(null);
  };

  const handleRequstBenchType = (e, value) => {
    setRequestBenchType(value);
    handleTable();
  };
  const handleChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  const [presets, setPresets] = useState(null);
  const [presetName, setPresetName] = useState(null);

  const handleClear = () => {
    setSelectedCreatedBy([]);
    setSelectedMaterial([]);
    setSelectedDivision([]);
    setSelectedReqType([]);
    setSelectedOptions([]);
    let tempFilterData = {
      ...rbSearchForm,
      reqPriority: "",
    };
    dispatch(
      commonFilterUpdate({
        module: "RequestBench",
        filterData: tempFilterData,
      })
    );
    // setMaterialFilterDetails({
    //   requestId: null,
    //   requestType: null,
    //   createdBy: "",
    //   createdOn: [null, null],
    //   status:null
    // });
    dispatch(commonFilterClear({ module: "RequestBench", days: 7 }));
    setClearClicked(true);
  };
  useEffect(() => {
    setinitialLoad(false);
    // dispatch(commonFilterDateDefault({ module: "RequestBench", backDate:date[0], toDate: date[1] }));
    return () => {
      dispatch(
        initialDataUpdate({
          module: "RequestBench",
          initialData: [],
        })
      );
      dispatch(commonFilterClear({ module: "RequestBench", days: 7 }));
    };
  }, []);
  const onRowsSelectionHandler = (ids) => {
    setSelectedRows(ids);
    //Selected Columns stored here
    const selectedRowsData = ids.map((id) => rmDataRows.find((row) => row.id === id));
    var compCodes = selectedRowsData.map((row) => row.company);
    var companySet = new Set(compCodes);
    var vendors = selectedRowsData.map((row) => row.vendor);
    var vendorSet = new Set(vendors);
    var paymentTerms = selectedRowsData.map((row) => row.paymentTerm);
    var paymentTermsSet = new Set(paymentTerms);
    if (selectedRowsData.length > 0) {
      if (companySet.size === 1) {
        if (vendorSet.size === 1) {
          if (paymentTermsSet.size !== 1) {
            setDisableButton(true);
            setMessageDialogTitle("Error");
            setMessageDialogMessage("Invoice cannot be generated for vendors with different payment terms");
            setMessageDialogSeverity("danger");
            handleMessageDialogClickOpen();
          } else setDisableButton(false);
        } else {
          setDisableButton(true);
          setMessageDialogTitle("Error");
          setMessageDialogMessage("Invoice cannot be generated for multiple suppliers");
          setMessageDialogSeverity("danger");
          handleMessageDialogClickOpen();
        }
      } else {
        setDisableButton(true);
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Invoice cannot be generated for multiple companies");
        setMessageDialogSeverity("danger");
        handleMessageDialogClickOpen();
      }
    } else {
      setDisableButton(true); //Enable the Create E-Invoice button when at least one row is selected and no two companys or vendors are same
    }
    setSelectedRow(ids); //Setting the ids(PO Numbers) of selected rows
    setSelectedDetails(selectedRowsData); //Setting the entire data of a selected row
  };
  function refreshPage() {
    getFilter();
  }

  const [company, setCompany] = useState([]);
  const [Companyid, setCompanyid] = useState([]);

  // let { poId } = useParams();
  const [open, setOpen] = useState(false);
  const [matAnchorEl, setMatAnchorEl] = useState(null);
  const [materialDetails, setMaterialDetails] = useState(null);
  const [itemDataRows, setItemDataRows] = useState([]);

  // const handleMatCodeClick = (event) => {
  //   if (materialDetails !== null) setMaterialDetails(null);
  //   else fetchMaterialDetails(event.target.innerText);
  //   setMatAnchorEl(matAnchorEl ? null : event.currentTarget);
  // };

  const handlePODetailsClick = (event) => {
    setOpendialog3(true);
  };

  const matOpen = Boolean(matAnchorEl);
  const popperId = matOpen ? "simple-popper" : undefined;

  const handleClickOpen = () => {
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
  };

  const [poNum, setPONum] = useState(null);
  const fetchPOHeader = (id) => {
    var formData = new FormData();
    if (id) formData.append("extReturnId", id);
    const hSuccess = (data) => {
      if (data) {
        setPoHeader(data);
        setPONum(data[0]["poNumber"] ?? "");
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_Returns}/returnsHeader/getReturnsPreview`, "postformdata", hSuccess, hError, formData);
  };

  function generateHtmlForPdf(rowsDataForPayload, firstDataOfDisplay, secondDataOfDisplay, comments, changelogData, workFlowData) {
    return (
      <>
        <div>
          <div
            style={{
              background: "#EAE9FF",
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <div style={{ marginTop: 2, marginLeft: 2 }}>
              <h6>Request ID: {rowsDataForPayload?.requestId}</h6>
            </div>
            <div style={{ outerContainer_Information }}>
              <h2>
                <strong>SUMMARY</strong>
              </h2>
            </div>

            <div alignItems={"end"}>
              <Typography>Created By: {rowsDataForPayload?.createdBy}</Typography>
              <Typography>Created On: {rowsDataForPayload?.createdOn}</Typography>
            </div>
          </div>

          <div style={{ marginLeft: "5%", display: "flex" }}>
            <div style={{ width: "50%" }}>
              <div style={{ display: "flex" }}>
                <div style={{ width: "50%" }}>
                  <h5>
                    <u>Field</u>
                  </h5>
                </div>

                <div>
                  <h5>
                    <u>Value</u>
                  </h5>
                </div>
              </div>
              {firstDataOfDisplay.map((item) => {
                return (
                  <div>
                    <div style={{ display: "flex" }}>
                      <div style={{ width: "50%" }}>
                        {console.log(item)}
                        <h6>{item[0]}</h6>
                      </div>

                      <div>
                        <p>{item[1] != "" ? item[1] : item[1] === true ? "Yes" : item[1] === false ? "No" : "-"}</p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            <div style={{ width: "50%" }}>
              <div style={{ display: "flex" }}>
                <div style={{ width: "50%" }}>
                  <h5>
                    <u>Field</u>
                  </h5>
                </div>

                <div>
                  <h5>
                    <u>Value</u>
                  </h5>
                </div>
              </div>
              {secondDataOfDisplay.map((item) => {
                return (
                  <div>
                    <div style={{ display: "flex" }}>
                      <div style={{ width: "50%" }}>
                        {console.log(item)}
                        <h6>{item[0]}</h6>
                      </div>

                      <div>
                        <p>{item[1] != "" ? item[1] : item[1] === true ? "Yes" : item[1] === false ? "No" : "-"}</p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
          <div style={{ marginLeft: "5%", marginTop: "5%" }}>
            <h4>
              <u>Comments</u>
            </h4>
            <div>
              <h6>
                {comments.map((item) => {
                  return (
                    <ul>
                      <li> {`${item?.comment}    (${item?.user}) `}</li>
                    </ul>
                  );
                })}
              </h6>
            </div>
          </div>
          <div style={{ margin: "5%" }}>
            <h4>
              <u>Change Log</u>
            </h4>
            <div>
              <h6>
                {/* {comments.map((item) => {
                      return ( */}
                <table style={{ width: "100%", border: "1px solid black" }}>
                  <tr>
                    <th style={{ border: "1px solid black" }}>Field Name</th>
                    <th
                      style={{
                        border: "1px solid black",
                        textAlign: "left",
                        padding: "8px",
                      }}
                    >
                      Old Value
                    </th>
                    <th
                      style={{
                        border: "1px solid black",
                        textAlign: "left",
                        padding: "8px",
                      }}
                    >
                      New Value
                    </th>
                    <th
                      style={{
                        border: "1px solid black",
                        textAlign: "left",
                        padding: "8px",
                      }}
                    >
                      Updated By
                    </th>
                    <th
                      style={{
                        border: "1px solid black",
                        textAlign: "left",
                        padding: "8px",
                      }}
                    >
                      Updated On
                    </th>
                  </tr>

                  {changelogData.map((item) => {
                    return (
                      <tr>
                        <td
                          style={{
                            border: "1px solid black",
                            padding: "8px",
                          }}
                        >
                          {item?.FieldName}
                        </td>
                        <td
                          style={{
                            border: "1px solid black",
                            padding: "8px",
                          }}
                        >
                          {item?.PreviousValue}
                        </td>
                        <td
                          style={{
                            border: "1px solid black",
                            padding: "8px",
                          }}
                        >
                          {item?.CurrentValue}
                        </td>
                        <td
                          style={{
                            border: "1px solid black",
                            padding: "8px",
                          }}
                        >
                          {item?.ChangedBy}
                        </td>
                        <td
                          style={{
                            border: "1px solid black",
                            padding: "8px",
                          }}
                        >
                          {moment(item?.ChangedOn).format(appSettings?.dateFormat)}
                        </td>
                      </tr>
                    );
                  })}
                </table>
                {/* );
                    })} */}
              </h6>
            </div>
          </div>
          <div style={{ margin: "5%" }}>
            <h4>
              <u>Task Flow</u>
            </h4>

            <div>
              <div
                style={{
                  border: "1px solid",
                  borderRadius: "10px",
                  padding: "10px",
                  width: "50%",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                  }}
                >
                  <h6>Soumarya Sarkar</h6>
                  <p>24/02/2024</p>
                </div>
                <hr style={{ margin: "0px" }}></hr>
                <div style={{ marginTop: "5px" }}>
                  <h5>Process Started</h5>
                </div>
                <div style={{ marginTop: "4%" }}>
                  <i>Task Completion Time : 30 minutes </i>
                </div>
              </div>
              {workFlowData.map((item) => {
                return (
                  <>
                    <div style={{ width: "50%" }}>
                      <b
                        style={{
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          paddingBottom: "1%",
                        }}
                      >
                        {" "}
                        <p>|</p>
                      </b>
                    </div>
                    <div
                      style={{
                        border: "1px solid",
                        borderRadius: "10px",
                        padding: "10px",
                        width: "50%",
                      }}
                    >
                      <div
                        style={{
                          display: "flex",
                          justifyContent: "space-between",
                        }}
                      >
                        <h6>{item?.processor}</h6>
                        <p>{item?.createdAt}</p>
                      </div>
                      <hr style={{ margin: "0px" }}></hr>
                      <div style={{ marginTop: "5px" }}>
                        <h5>{item?.subject}</h5>
                      </div>
                      <div style={{ marginTop: "4%" }}>
                        <i>Task Completion Time : {item?.completedAt} </i>
                      </div>
                    </div>
                  </>
                );
              })}
            </div>
          </div>
        </div>
      </>
    );
  }

  const handleDownloadClick = async (rowData) => {
    const pdfBlob = await generatePDF(rowData);
    if (pdfBlob) {
      const pdfData = URL.createObjectURL(pdfBlob); // Convert the blob to a URL
      const downloadLink = document.createElement("a"); // Create a download link
      downloadLink.href = pdfData; // Set the URL as the download link's href
      downloadLink.download = "summary-report.pdf"; // Set the download filename
      document.body.appendChild(downloadLink); // Append the download link to the document body
      downloadLink.click(); // Trigger the download
      document.body.removeChild(downloadLink); // Remove the download link from the document body
    } else {
      console.log("Error: PDF blob is null or undefined");
    }
  };

  let generatePDF = async (rowData) => {
    let fetchData = (rowsDataForPayload) => {
      let payload = {};
      let url = "";
      if (rowsDataForPayload?.requestId?.includes("NCS") || rowsDataForPayload?.requestId?.includes("CCS")) {
        console.log("inside if");
        payload = {
          id: rowsDataForPayload?.id ?? "",
          costCenter: rowsDataForPayload?.costCenter ?? "",
          controllingArea: rowsDataForPayload?.controllingAreaDataCopy ?? "",
          reqStatus: rowsDataForPayload?.reqStatus ?? "Approved",
          screenName: rowsDataForPayload?.requestType ?? "Change",
        };
        url = `/${destination_CostCenter}/data/displayCostCenter`;
      } else if (rowsDataForPayload?.requestId?.includes("NPS") || rowsDataForPayload?.requestId?.includes("CPS")) {
        console.log("inside if");
        payload = {
          id: rowsDataForPayload?.id ?? "",
          profitCenter: rowsDataForPayload?.profitCenter ?? "",
          controllingArea: rowsDataForPayload?.controllingArea ?? "",
          reqStatus: rowsDataForPayload?.reqStatus ?? "Approved",
          screenName: rowsDataForPayload?.requestType ?? "Change",
        };
        url = `/${destination_ProfitCenter}/data/displayProfitCenter`;
      } else if (rowsDataForPayload?.requestId?.includes("NLS") || rowsDataForPayload?.requestId?.includes("CLS")) {
        console.log("inside if");
        payload = {
          id: rowsDataForPayload?.id ?? "",
          glAccount: rowsDataForPayload?.glAccount ?? "",
          compCode: rowsDataForPayload?.compCode ?? "",
          reqStatus: rowsDataForPayload?.reqStatus ?? "Approved",
          screenName: rowsDataForPayload?.requestType ?? "Change",
        };
        url = `/${destination_GeneralLedger}/data/displayGeneralLedger`;
      } else if (rowsDataForPayload?.requestId?.includes("NBS") || rowsDataForPayload?.requestId?.includes("CBS")) {
        payload = {
          id: rowsDataForPayload?.id ?? "",
          bankCtry: rowsDataForPayload?.bankCtryReg ?? "",
          bankKey: rowsDataForPayload?.bankKey ?? "",
          reqStatus: rowsDataForPayload?.reqStatus ?? "Approved",
          screenName: rowsDataForPayload?.requestType ?? "Change",
        };
        url = `/${destination_BankKey}/data/displayBankKey`;
      } else if (rowsDataForPayload?.requestId?.includes("NMS")) {
        payload = {
          id: rowsDataForPayload?.id ?? "",
          materialNo: rowsDataForPayload?.materialNo ?? "",
          plantOrg: rowsDataForPayload?.plantOrg ?? "",
          salesOrg: rowsDataForPayload?.salesOrg ?? "",
          distChnl: rowsDataForPayload?.distChnl ?? "",
          reqStatus: rowsDataForPayload?.reqStatus ?? "Approved",
          screenName: rowsDataForPayload?.requestType ?? "Change",
        };
        url = `/${destination_MaterialMgmt}/data/displayMaterial`;
      }

      const hError = (error) => {
        console.log(error);
      };
      return promiseAjax(url, "post", payload);
      // console.log("payload", payload);
    };

    let fetchCommentData = (rowsDataForPayload) => {
      let urForComments = "";
      if (rowsDataForPayload?.requestId?.includes("NCS") || rowsDataForPayload?.requestId?.includes("CCS")) {
        console.log("inside if");
        urForComments = `/${destination_CostCenter}/activitylog/fetchTaskDetailsForRequestId?requestId=${rowsDataForPayload?.requestId}`;
      } else if (rowsDataForPayload?.requestId?.includes("NPS") || rowsDataForPayload?.requestId?.includes("CPS")) {
        console.log("inside if");
        urForComments = `/${destination_ProfitCenter}/activitylog/fetchTaskDetailsForRequestId?requestId=${rowsDataForPayload?.requestId}`;
      } else if (rowsDataForPayload?.requestId?.includes("NLS") || rowsDataForPayload?.requestId?.includes("CLS")) {
        console.log("inside if");
        urForComments = `/${destination_GeneralLedger}/activitylog/fetchTaskDetailsForRequestId?requestId=${rowsDataForPayload?.requestId}`;
      } else if (rowsDataForPayload?.requestId?.includes("NBS") || rowsDataForPayload?.requestId?.includes("CBS")) {
        urForComments = `/${destination_BankKey}/activitylog/fetchTaskDetailsForRequestId?requestId=${rowsDataForPayload?.requestId}`;
      }
      return promiseAjax(urForComments, "get");
    };
    let fetchChangeLogData = (rowsDataForPayload) => {
      let changeLogUrl = "";
      if (rowsDataForPayload?.requestId?.includes("NCS") || rowsDataForPayload?.requestId?.includes("CCS")) {
        console.log("inside if");
        changeLogUrl = `/${destination_CostCenter}/activitylog/getChangeLogDetails?requestId=${rowsDataForPayload?.requestId?.slice(3)}&isMass=${false}&coaCostCenter=${rowsDataForPayload?.controllingArea + rowsDataForPayload?.costCenter}`;
      } else if (rowsDataForPayload?.requestId?.includes("NPS") || rowsDataForPayload?.requestId?.includes("CPS")) {
        console.log("inside if");
        changeLogUrl = `/${destination_ProfitCenter}/activitylog/getChangeLogDetails?requestId=${rowsDataForPayload?.requestId?.slice(3)}&isMass=${false}&coaProfitCenter=${rowsDataForPayload?.controllingArea + rowsDataForPayload?.profitCenter}`;
      } else if (rowsDataForPayload?.requestId?.includes("NLS") || rowsDataForPayload?.requestId?.includes("CLS")) {
        console.log("inside if");
        changeLogUrl = `/${destination_GeneralLedger}/activitylog/getChangeLogDetails?requestId=${rowsDataForPayload?.requestId?.slice(3)}&isMass=${false}&compCodeGlAcc=${rowsDataForPayload?.compCode + rowsDataForPayload?.glAccount}`;
      } else if (rowsDataForPayload?.requestId?.includes("NBS") || rowsDataForPayload?.requestId?.includes("CBS")) {
        changeLogUrl = `/${destination_BankKey}/activitylog/getChangeLogDetails?requestId=${rowsDataForPayload?.requestId?.slice(3)}&isMass=${false}&countryBankKey=${rowsDataForPayload?.bankCtryReg + rowsDataForPayload?.bankKey}`;
      }
      return promiseAjax(changeLogUrl, "get");
    };

    let fetchWorkFlowData = (rowsDataForPayload) => {
      let urlForWorkFlow = "";
      if (rowsDataForPayload?.requestId?.includes("NCS") || rowsDataForPayload?.requestId?.includes("CCS")) {
        console.log("inside if");
        urlForWorkFlow = `/${destination_CostCenter}/activitylog/fetchRequestHistory?requestId=${rowsDataForPayload?.requestId}`;
      } else if (rowsDataForPayload?.requestId?.includes("NPS") || rowsDataForPayload?.requestId?.includes("CPS")) {
        console.log("inside if");
        urlForWorkFlow = `/${destination_ProfitCenter}/activitylog/fetchRequestHistory?requestId=${rowsDataForPayload?.requestId}`;
      } else if (rowsDataForPayload?.requestId?.includes("NLS") || rowsDataForPayload?.requestId?.includes("CLS")) {
        console.log("inside if");
        urlForWorkFlow = `/${destination_GeneralLedger}/activitylog/fetchRequestHistory?requestId=${rowsDataForPayload?.requestId}`;
      } else if (rowsDataForPayload?.requestId?.includes("NBS") || rowsDataForPayload?.requestId?.includes("CBS")) {
        urlForWorkFlow = `/${destination_BankKey}/activitylog/fetchRequestHistory?requestId=${rowsDataForPayload?.requestId}`;
      }
      return promiseAjax(urlForWorkFlow, "get");
    };

    let data = await (await fetchData(rowData)).json();
    let commentData = await (await fetchCommentData(rowData)).json();
    let changeLogData = await (await fetchChangeLogData(rowData)).json();
    let workFlowData = await (await fetchWorkFlowData(rowData)).json();
    // console.log("commentData", data, commentData);
    // console.log("fetchWorkFlowData", workFlowData);
    const responseBody = data.body.viewData;
    let viewDataArray = Object.entries(responseBody);
    const toSetArray = {};
    viewDataArray.map((item) => {
      console.log("bottle", item[1]);
      let temp = Object.entries(item[1]);
      console.log("notebook", temp);
      temp.forEach((fieldGroup) => {
        fieldGroup[1].forEach((field) => {
          toSetArray[field.fieldName] = field.value;
        });
      });
      return item;
    });
    setDisplayData(toSetArray);
    let displayData = {};
    let firstDisplayData = [];
    let secondDisplayData = [];
    displayData = Object.entries(toSetArray);
    console.log(displayData.length, "displayData");
    if (displayData && displayData.length > 0) {
      let firstRange = displayData.length / 2;
      firstDisplayData = displayData.slice(0, firstRange);
      secondDisplayData = displayData.slice(-firstRange);
    }
    var commentRows = [];
    commentData.body.forEach((cmt) => {
      var tempRow = {
        id: cmt.requestId,
        comment: cmt.comment,
        user: cmt.createdByUser,
        createdAt: cmt.updatedAt,
      };
      commentRows.push(tempRow);
    });

    console.log("commentrows", commentRows);

    var changeLogRows = [];
    for (let index = 0; index < changeLogData?.body?.length; index++) {
      var tempObj = changeLogData?.body[index];
      var tempRow = {
        id: uuidv4(),
        ChangedBy: tempObj.ChangedBy,
        ChangedOn: tempObj.ChangedOn,
        FieldName: tempObj.FieldName,
        PreviousValue: tempObj.PreviousValue,
        CurrentValue: tempObj.CurrentValue,
      };
      changeLogRows.push(tempRow);
    }
    let html = generateHtmlForPdf(rowData, firstDisplayData, secondDisplayData, commentRows, changeLogRows, workFlowData?.body);
    const pdfBlob = await exportAsPDF(html);

    // setJsPdfBlob(pdfBlob);
    console.log("pdfblob", pdfBlob);
    return pdfBlob;
  };

  // const [mergedPdfBlob, setMergedPdfBlob] = useState(null);

  const generateMergedPdf = async (rowData) => {
    const firstBlob = await generatePDF(rowData);
    console.log("firstBlob", firstBlob);

    let fetchAttachmentsData = (rowsDataForPayload) => {
      let urlForAttachments = "";
      if (rowsDataForPayload?.requestId?.includes("NCS") || rowsDataForPayload?.requestId?.includes("CCS")) {
        urlForAttachments = `/${destination_DocumentManagement}/documentManagement/getDocByRequestId/${rowsDataForPayload?.requestId}`;
      } else if (rowsDataForPayload?.requestId?.includes("NPS") || rowsDataForPayload?.requestId?.includes("CPS")) {
        urlForAttachments = `/${destination_DocumentManagement}/documentManagement/getDocByRequestId/${rowsDataForPayload?.requestId}`;
      } else if (rowsDataForPayload?.requestId?.includes("NLS") || rowsDataForPayload?.requestId?.includes("CLS")) {
        urlForAttachments = `/${destination_DocumentManagement}/documentManagement/getDocByRequestId/${rowsDataForPayload?.requestId}`;
      }
      return promiseAjax(urlForAttachments, "get");
    };
    let attachmentsFetchedData = await (await fetchAttachmentsData(rowData)).json();
    console.log("attachmentsFetchedData", attachmentsFetchedData);
    const attachmentsData = attachmentsFetchedData.documentDetailDtoList;
    console.log("attachmentsData", attachmentsData);

    let attachmentRows = [];
    attachmentsFetchedData?.documentDetailDtoList?.forEach((doc) => {
      if (true) {
        attachmentRows.push({
          id: doc.documentId,
          docType: doc.fileType,
          name: doc.fileName,
          uploadedBy: doc.createdBy,
          metaData: doc.metaDataTag ?? "",
          viewUrl: doc.documentViewUrl,
        });
      }
    });
    // console.log("temprows", tempRows);
    // setattachmentData(tempRows);
    // debugger
    console.log("attachmentData", attachmentData);
    // debugger;
    // setTimeout(() => {
    handlePdfDownload(firstBlob, attachmentRows);
    // }, 3000);
    // setJsPdfBlob(firstBlob);
  };

  const handlePdfDownload = async (firstBlob, attachmentRows) => {
    const pdfDoc = await PDFDocument.create();

    if (firstBlob) {
      const arrayBuffer = await firstBlob.arrayBuffer();
      const externalPdfDoc = await PDFDocument.load(arrayBuffer);
      const copiedPages = await pdfDoc.copyPages(externalPdfDoc, externalPdfDoc.getPageIndices());
      copiedPages.forEach((page) => pdfDoc.addPage(page));
    }

    // console.log("attachmentData", attachmentData);
    for (const attachment of attachmentRows) {
      console.log("attachment", attachment);
      if (attachment.docType === "application/pdf") {
        const pdfBlob = await fetch(attachment.viewUrl).then((res) => res.blob());
        console.log("pdfBlob", pdfBlob);

        // Check if pdfBlob is not null before accessing its arrayBuffer
        if (pdfBlob) {
          const arrayBuffer = await pdfBlob.arrayBuffer();
          const externalPdfDoc = await PDFDocument.load(arrayBuffer);
          const copiedPages = await pdfDoc.copyPages(externalPdfDoc, externalPdfDoc.getPageIndices());
          copiedPages.forEach((page) => pdfDoc.addPage(page));
        }
      }
    }

    const pdfBytes = await pdfDoc.save();

    const blob = new Blob([pdfBytes], { type: "application/pdf" });
    const downloadLink = document.createElement("a");
    downloadLink.href = URL.createObjectURL(blob);
    downloadLink.download = "Merged-pdf.pdf";
    downloadLink.click();
  };

  const [id, setID] = useState("");
  const columns = [
    {
      field: "id",
      headerName: "ID",
      hide: true,
      editable: false,
      flex: 1,
    },
    {
      field: "requestId",
      headerName: "Parent Request ID",
      editable: false,
      flex: 1.4,
    },
    // {
    //   field: "requestDesc",
    //   headerName: "Request Description",
    //   editable: false,
    //   flex: 1,
    // },
    {
      field: "requestType",
      headerName: "Request Type",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        const tempName = params.row.tempName;

        return (
          <div style={{ display: "flex", alignItems: "center", gap: "4px" }}>
            <span style={{ flex: 1, wordBreak: "break-word", whiteSpace: "normal" }}>{params.row.requestType}</span>
            {tempName !== "" && (
              <Tooltip arrow placement="right" title={<div style={{ maxHeight: "200px", overflowY: "auto" }}>{tempName}</div>}>
                <InfoIcon sx={{ 
                        fontSize: "1rem",
                        color: "primary.main",
                        "&:hover": { color: "primary.dark" }
                      }}  />
              </Tooltip>
            )}
          </div>
        );
      },
    },
    {
      field: "material",
      headerName: "Material",
      sortable: false,
      flex: 1,
      renderCell: (params) => {
        const materials = params.value ? params.value.split(",").map(m => m.trim()) : [];
        const displayCount = materials.length - 1;

        if (materials.length === 0) return "-";

        return (
          <Box sx={{ 
            display: "flex", 
            alignItems: "center",
            width: "100%",
            minWidth: 0 
          }}>
            <Tooltip 
              title={materials[0]}
              placement="top"
              arrow
            >
              <Typography 
                variant="body2" 
                sx={{ 
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  flex: 1,
                  minWidth: 0,
                }}
              >
                {materials[0]}
              </Typography>
            </Tooltip>
            {displayCount > 0 && (
              <Box sx={{ 
                display: "flex",
                alignItems: "center",
                ml: 1,
                flexShrink: 0 
              }}>
                <Tooltip
                  arrow
                  placement="right"
                  title={
                    <Box sx={{ p: 1, maxHeight: 200, overflowY: "auto" }}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                        Additional Materials ({displayCount})
                      </Typography>
                      {materials.slice(1).map((material, idx) => (
                        <Typography key={idx} variant="body2" sx={{ mb: 0.5 }}>
                          {material}
                        </Typography>
                      ))}
                    </Box>
                  }
                >
                  <Box sx={{ 
                    display: "flex", 
                    alignItems: "center",
                    cursor: "pointer"
                  }}>
                    <InfoIcon 
                      sx={{ 
                        fontSize: "1rem",
                        color: "#3b30c8",
                        "&:hover": { color: "#2e25a0" }
                      }} 
                    />
                    <Typography 
                      variant="caption" 
                      sx={{ 
                        ml: 0.5,
                        color: "#3b30c8",
                        fontSize: "11px"
                      }}
                    >
                      +{displayCount}
                    </Typography>
                  </Box>
                </Tooltip>
              </Box>
            )}
          </Box>
        );
      },
    },
    {
      field: "requestPriority",
      headerName: "Request Priority",
      editable: false,
      flex: 1,
    },
    {
      field: "createdOn",
      headerName: "Created At",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        return <Typography sx={{ fontSize: "12px" }}>{moment(params.row.createdOn).format(appSettings?.dateFormat)}</Typography>;
      },
    },
    {
      field: "changedOn",
      headerName: "Updated At",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        return <Typography sx={{ fontSize: "12px" }}>{moment(params.row.changedOn).format(appSettings?.dateFormat)}</Typography>;
      },
    },

    {
      field: "createdBy",
      headerName: "Created By",
      editable: false,
      flex: 1,
    },
     {
  field: "childRequestIds",
  headerName: "Child Request ID(s)",
  editable: false,
  flex: 1.4,
  renderCell: (params) => {
    if (params.value === "Not Available") return "Not Available";
    
    const materials = params.value ? params.value.split(",").map(m => {
    const prefix = params.row.requestId.substring(0, 3);
      return prefix + m.trim();
    }) : [];
    const displayCount = materials.length - 1;

    if (materials.length === 0) return "-";

    return (
      <Box sx={{ 
        display: "flex", 
        alignItems: "center",
        width: "100%",
        minWidth: 0 
      }}>
        <Tooltip 
          title={materials[0]}
          placement="top"
          arrow
        >
          <Typography 
            variant="body2" 
            sx={{ 
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
              flex: 1,
              minWidth: 0,
            }}
          >
            {materials[0]}
          </Typography>
        </Tooltip>
        {displayCount > 0 && (
          <Box sx={{ 
            display: "flex",
            alignItems: "center",
            ml: 1,
            flexShrink: 0 
          }}>
            <Tooltip
              arrow
              placement="right"
              title={
                <Box sx={{ p: 1, maxHeight: 200, overflowY: "auto" }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                    Additional Child Requests ({displayCount})
                  </Typography>
                  {materials.slice(1).map((material, idx) => (
                    <Typography key={idx} variant="body2" sx={{ mb: 0.5 }}>
                      {material}
                    </Typography>
                  ))}
                </Box>
              }
            >
              <Box sx={{ 
                display: "flex", 
                alignItems: "center",
                cursor: "pointer"
              }}>
                <InfoIcon 
                  sx={{ 
                    fontSize: "1rem",
                    color: "#3b30c8",
                    "&:hover": { color: "#2e25a0" }
                  }} 
                />
                <Typography 
                  variant="caption" 
                  sx={{ 
                    ml: 0.5,
                    color: "#3b30c8",
                    fontSize: "11px"
                  }}
                >
                  +{displayCount}
                </Typography>
              </Box>
            </Tooltip>
          </Box>
        )}
      </Box>
    );
  },
     
    },
    {
      field: "reqStatus",
      headerName: "Status",
      sortable: false,
      width: 210,
      renderCell: (cellValues) => {
        return (
          <Chip
          sx={{
            justifyContent: "flex-start",
            borderRadius: "4px",
            color: "#000",
            width: "100%",
            minWidth: "4.6rem",
            fontSize: "12px",
            background: 
              colors.statusColorMap[
                cellValues.row.reqStatus.toLowerCase().replace(/[^a-z0-9]/gi, '')
              ] || colors.statusColorMap.default
          }}
          label={cellValues.row.reqStatus}
        />      
        );
      },
    },
    {
      field: "actions",
      align: "center",
      flex: 1, // Use flex for responsive width
      headerAlign: "center",
      headerName: "Actions",
      sortable: false,
      renderCell: (params) => {
        return (
        <div>
          {/* <Tooltip title="View Flow">
            <IconButton
              disabled={params?.row?.reqStatus === `${REQUEST_STATUS?.DRAFT}` || params?.row?.reqStatus === `${REQUEST_STATUS?.UPLOAD_SUCCESSFUL}` || params?.row?.reqStatus === `${REQUEST_STATUS?.UPLOAD_FAILED}` || params?.row?.reqStatus === `${REQUEST_STATUS?.VALIDATED_REQUESTOR}` || params?.row?.reqStatus === `${REQUEST_STATUS?.VALIDATION_FAILED_REQUESTOR}` || params?.row?.reqStatus === `${REQUEST_STATUS?.SYNDICATED_IN_SAP_DIRECT}` || params?.row?.reqStatus === `${REQUEST_STATUS?.SYNDICATION_FAILED_DIRECT}` || params?.row?.reqStatus === `${REQUEST_STATUS?.SYNDICATED_PARTIALLY_DIRECT}`}
              aria-label="View Mwtadata"
              onClick={() => {
                setLocalStorage(LOCAL_STORAGE_KEYS.REQUEST_BENCH_TASK, params?.row);
                dispatch(
                  commonSearchBarUpdate({
                    module: "RequestHistory",
                    filterData: {
                      reqId: params.row.requestId,
                    },
                  })
                );
                navigate("/requestBench/RequestHistory");
              }}
            >
              <Workflow
                color={
                  params?.row?.reqStatus === `${REQUEST_STATUS.DRAFT}` || params?.row?.reqStatus === `${REQUEST_STATUS.UPLOAD_SUCCESSFUL}` || params?.row?.reqStatus === `${REQUEST_STATUS.UPLOAD_FAILED}` || params?.row?.reqStatus === `${REQUEST_STATUS.VALIDATED_REQUESTOR}` || params?.row?.reqStatus === `${REQUEST_STATUS.VALIDATION_FAILED_REQUESTOR}` || params?.row?.reqStatus === `${REQUEST_STATUS?.SYNDICATED_IN_SAP_DIRECT}` || params?.row?.reqStatus === `${REQUEST_STATUS?.SYNDICATION_FAILED_DIRECT}` || params?.row?.reqStatus === `${REQUEST_STATUS?.SYNDICATED_PARTIALLY_DIRECT}`
                    ? "#808080"
                    : "#3B30C8"
                }
              />
            </IconButton>
          </Tooltip>

          <Tooltip title="Cancel">
            <IconButton
              disabled={params?.row?.reqStatus === `${REQUEST_STATUS.SYNDICATED_IN_SAP}` || params?.row?.reqStatus === `${REQUEST_STATUS.CANCELED}` || params?.row?.reqStatus === `${REQUEST_STATUS.REJECTED}` || params?.row?.reqStatus === `${REQUEST_STATUS.UPLOAD_FAILED}` || params?.row?.reqStatus === `${REQUEST_STATUS.SYNDICATED_IN_SAP_DIRECT}` || params?.row?.reqStatus === `${REQUEST_STATUS?.SYNDICATED_PARTIALLY_DIRECT}` || params?.row?.reqStatus === `${REQUEST_STATUS?.SYNDICATED_PARTIALLY}`}
              aria-label="View Metadata"
              onClick={() => {
                setIsDeleteDialogVisible({ ...isDeleteDialogVisible, data: params, isVisible: true });
              }}
            >
              <DeleteForeverOutlined
                sx={{
                  color: (theme) => (params?.row?.reqStatus === `${REQUEST_STATUS.SYNDICATED_IN_SAP}` || params?.row?.reqStatus === `${REQUEST_STATUS.CANCELED}` || params?.row?.reqStatus === `${REQUEST_STATUS.REJECTED}` || params?.row?.reqStatus === `${REQUEST_STATUS.UPLOAD_FAILED}` || params?.row?.reqStatus === `${REQUEST_STATUS.SYNDICATED_IN_SAP_DIRECT}` || params?.row?.reqStatus === `${REQUEST_STATUS?.SYNDICATED_PARTIALLY_DIRECT}` || params?.row?.reqStatus === `${REQUEST_STATUS?.SYNDICATED_PARTIALLY}` ? "#808080" : "#cc3300"),
                }}
              />
            </IconButton>
          </Tooltip> */}
          <Tooltip title="Error Report">
            <IconButton
              disabled={params?.row?.reqStatus === `${REQUEST_STATUS.DRAFT}` || params?.row?.reqStatus === `${REQUEST_STATUS.UPLOAD_SUCCESSFUL}`}
              onClick={() => {
                // handleErrorReport(params.row.requestId, params.row.requestType);
                navigate(`/requestBench/errorHistory?RequestId=${params?.row?.requestId}`);
              }}
            >
              <SummarizeOutlinedIcon
                sx={{
                  color: params?.row?.reqStatus === `${REQUEST_STATUS.DRAFT}` || params?.row?.reqStatus === `${REQUEST_STATUS.UPLOAD_SUCCESSFUL}` ? "#808080" : "#ffd93f",
                }}
              />
            </IconButton>
          </Tooltip>
          <Tooltip title="View Child Requests">
            <IconButton disabled={!params.row.isBifurcated} 
              onClick={() => {
                setSelectedRow(params.row);              
                fetchBifurcationDetails(params.row?.requestId);      
                setOpenPopup(true);
              }}
            >
              <PreviewIcon sx={{ fontSize: "20px", color: !params.row.isBifurcated ? "#808080" :`${colors.blue.indigo}` }}
              />
            </IconButton>
          </Tooltip>
        </div>
      )},
    },
  ];

  const titleToFieldMapping = {
    "Request ID": "requestId",
    "Request Type": "requestType",
    "Created At": "createdOn",
    "Updated At": "changedOn",
    "Created By": "createdBy",
    Status: "reqStatus",
    // Add more mappings as needed
  };

  const dynamicFilterColumns = selectedOptions
    .map((option) => {
      const field = titleToFieldMapping[option]; // Get the corresponding field from the mapping
      if (!field) {
        return null; // Handle the case when the field doesn't exist in the mapping
      }
      return {
        field: field, // Use the field name from the mapping
        headerName: option,
        editable: false,
        flex: 1,
      };
    })
    .filter((column) => column !== null);
  const allColumns = [...columns, ...dynamicFilterColumns];
  const functions_ExportAsExcel = {
    convertJsonToExcel: () => {
      let excelColumns = [];
      allColumns.forEach((item) => {
        if (item.headerName.toLowerCase() !== "action" && !item.hide) {
          excelColumns.push({ header: item.headerName, key: item.field });
        }
      });
      savePDF({
        fileName: `Request Bench Data-${moment(presentDate).format("DD-MMM-YYYY")}`,
        columns: excelColumns,
        rows: rmDataRows,
      });
    },
    button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() => functions_ExportAsExcel.convertJsonToExcel()}
        >
          Download
        </Button>
      );
    },
  };

  //   .map((option) => {
  //     const field = titleToFieldMapping[option]; // Get the corresponding field from the mapping
  //     if (!field) {
  //       return null; // Handle the case when the field doesn't exist in the mapping
  //     }
  //     return {
  //       field: field, // Use the field name from the mapping
  //       headerName: option,
  //       editable: false,
  //       flex: 1,
  //     };
  //   })
  //   .filter((column) => column !== null); // Remove any null columns

  // //   const allColumns = [
  // //     ...columns,
  // //     ...dynamicFilterColumns,
  // //     // Other fixed and dynamic columns as needed
  // //   ];
  const capitalize = (str) => {
    //  str.map((str)=>{
    const arr = str.split(" ");
    for (var i = 0; i < arr.length; i++) {
      arr[i] = arr[i].charAt(0) + arr[i].slice(1).toLowerCase();
    }

    const str2 = arr.join(" ");
    return str2;
    //  })
  };

  // const fetchData = () => {
  //   setIsLoading(true)
  //   const formData = new FormData()
  //   if (rbSearchForm?.poNum) formData.append("poNumber", rbSearchForm?.poNum);
  //   if (date[0]) formData.append("createdStartDate", moment(date[0]).format("YYYY-MM-DD 00:00:00.*********"));
  //   if (date[1]) formData.append("createdEndDate", moment(date[1]).format("YYYY-MM-DD 00:00:00.*********"));
  //   if (rbSearchForm?.company) formData.append("company", rbSearchForm?.company);
  //   if (rbSearchForm?.vendor) formData.append("supplier", rbSearchForm?.vendor);
  //   if (rbSearchForm?.poStatus) formData.append("status", rbSearchForm?.poStatus);
  //   if (rbSearchForm?.plant) formData.append("Plant", rbSearchForm?.plant);
  //   const hSuccess = (data) => {
  //     var rows = [];
  //     for (let index = 0; index < data.length; index++) {
  //       var tempObj = data[index];
  //       var tempRow = {
  //         confirmid: tempObj["extReturnId"],
  //         poNum: tempObj["poNumber"],
  //         companyCode: tempObj["companyCode"],
  //         vendorId: tempObj["vendorId"],
  //         company: tempObj["companyName"],
  //         vendor: tempObj["vendorName"],
  //         confirmdate: moment(tempObj.createdAt).format(appSettings.date),
  //         returnType: tempObj.returnType,
  //         createdBy: tempObj.createdBy,
  //         poStatus: tempObj["status"],
  //         plant: tempObj["plant"]
  //       };
  //       rows.push(tempRow);
  //     }
  //     rows.sort((a, b) => (moment(a.confirmdate, "DD MMM YYYY HH:mm") - moment(b.confirmdate, "DD MMM YYYY HH:mm")))
  //     setRmDataRows(rows.reverse());
  //     setIsLoading(false);
  //     setroCount(rows.length);
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(`/${destination_Returns}/returnsOrder/filter`, "postformdata", hSuccess, hError, formData);
  // };
  useEffect(() => {
    getFilter();
    // functions_PresetFilter.getFilterPresets();
  }, [activeTab, pageSize]);

  // let serviceRequestForm_Component = new createServiceRequestForm(Status_ServiceReqForm, setStatus_ServiceReqForm)
  // <-- Function for taking screenshot (Export button) -->
  let ref_elementForExport = useRef(null);
  let exportAsPicture = () => {
    setTimeout(() => {
      captureScreenShot("Material-Single");
    }, 100);
  };
  // <-- Functions for exporting as excel -->
  // let xlsx = require("json-as-xlsx")
  // let excelColumns = []
  // columns.forEach((item) => {
  //   if (item.headerName.toLowerCase() !== 'action' && !item.hide) {
  //     excelColumns.push({ label: item.headerName, value: item.field })
  //   }
  // })
  // const functions_ExportAsExcel = {
  //   data: [
  //     {
  //       sheet: "Return Workbench",
  //       columns: excelColumns,
  //       content: rmDataRows,
  //     }
  //   ],
  //   settings: {
  //     fileName: `Return_WorkbenchDatasheet-${moment(presentDate).format('DD-MMM-YYYY')}`, // Name of the resulting spreadsheet
  //     extraLength: 3, // A bigger number means that columns will be wider
  //     writeMode: "writeFile", // The available parameters are 'WriteFile' and 'write'. This setting is optional.
  //     writeOptions: {}, // Style options from https://docs.sheetjs.com/docs/api/write-options
  //     RTL: false, // Display the columns from right-to-left (the default value is false)
  //   },
  //   convertJsonToExcel: () => {
  //     xlsx(functions_ExportAsExcel.data, functions_ExportAsExcel.settings)
  //   },
  //   button: () => {
  //     // return (
  //     //   <Button sx={{textTransform:'capitalize', position:'absolute', right:0, top:0}} onClick={()=> functions_ExportAsExcel.convertJsonToExcel()}>Download</Button>
  //     // )
  //   }
  // }
  const getBaseUnit = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "BaseUnit", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getBaseUOM`, "get", hSuccess, hError);
  };
  const getMaterialGroupCreate = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "MaterialGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getMaterialGroup`, "get", hSuccess, hError);
  };
  const getExtMaterialGroup = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ExtMatlGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getExMaterialGroup`, "get", hSuccess, hError);
  };
  const getDivision = (inputValue) => {
    setIsDropDownLoading(true);
    let payload = {
      division: inputValue,
    };
    const hSuccess = (data) => {
      setIsDropDownLoading(false);
      dispatch(setDropDown({ keyName: "reqBenchDivision", data: data.body }));
    };
    const hError = (error) => {
      setIsDropDownLoading(false);
      customError(error);
    };
    doAjax(`/${destination_MaterialMgmt}/${END_POINTS.REQUEST_BENCH.DIVISION}`, "post", hSuccess, hError, payload);
  };

  const getCreatedBy = (inputValue) => {
    setIsDropDownLoading(true);
    let payload = {
      createdBy: inputValue,
    };
    const hSuccess = (data) => {
      setIsDropDownLoading(false);
      setCreatedByOptions(data.body);
    };
    const hError = (error) => {
      setIsDropDownLoading(false);
      customError(error);
    };
    doAjax(`/${destination_MaterialMgmt}/${END_POINTS.REQUEST_BENCH.CREATED_BY}`, "post", hSuccess, hError, payload);
  };

  const getMaterialSearch = (inputValue) => {
    setIsDropDownLoading(true);
    let payload = {
      material: inputValue,
    };
    const hSuccess = (data) => {
      setIsDropDownLoading(false);
      setMaterialOptions(data.body);
    };
    const hError = (error) => {
      setIsDropDownLoading(false);
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getSearchParamMaterial`, "post", hSuccess, hError, payload);
  };
  const handleMatInputChange = (e) => {
    const inputValue = e.target.value;
    setMatInputValue(inputValue);
    // Clear any existing timer
    if (timerId) {
      clearTimeout(timerId);
    }

    if (inputValue.length >= 4) {
      const newTimerId = setTimeout(() => {
        getMaterialSearch(inputValue);
      }, 500);

      setTimerId(newTimerId);
    }
  };

  const handleDivInputChange = (e) => {
    const inputValue = e.target.value;
    if (inputValue == "") {
      return;
    }
    if (timerId) {
      clearTimeout(timerId);
    }

    const newTimerId = setTimeout(() => {
      getDivision(inputValue);
    }, 500);

    setTimerId(newTimerId);
  };

  const handleCreatedByInputChange = (e) => {
    const inputValue = e.target.value;
    if (inputValue == "") {
      return;
    }
    if (timerId) {
      clearTimeout(timerId);
    }

    const newTimerId = setTimeout(() => {
      getCreatedBy(inputValue);
    }, 500);

    setTimerId(newTimerId);
  };

  const getLabOffice = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "LabOffice", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getLaboratoryDesignOffice`, "get", hSuccess, hError);
  };
  const getGenItemCatGroup = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "GeneralItemCategorGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getGenItemCatGroup`, "get", hSuccess, hError);
  };
  const getProductAllocation = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProductAllocation", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getProdAllocation`, "get", hSuccess, hError);
  };
  const getProductHeirarchy = () => {
    //haven't used now
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProdHier", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getProdHier`, "get", hSuccess, hError);
  };
  const getWeightUnit = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "WeightUnit", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getWeightUnit`, "get", hSuccess, hError);
  };
  const getVolumeUnit = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "VolumeUnit", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getVolumeUnit`, "get", hSuccess, hError);
  };
  const getMedium = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Medium", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getMedium`, "get", hSuccess, hError);
  };
  const getDgIndicatorProfile = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "dgIndiProfile", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getDgIndCator`, "get", hSuccess, hError);
  };
  const getEANCategory = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "CategoryOfInternationalArticleNumberEAN",
          data: data.body,
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getEanCategory`, "get", hSuccess, hError);
  };
  const getXPlantMaterialStatus = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CrossPlantMaterialStatus", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getXPlant`, "get", hSuccess, hError);
  };
  const getSegmentationStructure = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "SegmentationStructure", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getSegStructure`, "get", hSuccess, hError);
  };
  const getSegmentationStrategy = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "SegmentationStrategy", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getSegStrategy`, "get", hSuccess, hError);
  };
  const getANPCode = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ANPCode", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getANPCode`, "get", hSuccess, hError);
  };
  const getMRPType = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "MRPType", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getMRPType`, "get", hSuccess, hError);
  };
  const getMRPGroup = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "MRPGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getMRPGroup`, "get", hSuccess, hError);
  };
  const getSalesUnit = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "SalesUnit", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getSalesUnit`, "get", hSuccess, hError);
  };
  const getMRPController = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "MRPController", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getMRPController`, "get", hSuccess, hError);
  };

  const getUnitOfMeasureGroup = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "UnitsOfMeasureGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getUnitOfMeasureGroup`, "get", hSuccess, hError);
  };
  const getPurchasingGroup = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "PurchasingGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getPurchaseGroup`, "get", hSuccess, hError);
  };
  const getTaxCategory = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "TaxCategory", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getTaxCategory`, "get", hSuccess, hError);
  };
  const getPlantSplMatStatus = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "PlantSpMatStatus:", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getPlantSpMatlStatus`, "get", hSuccess, hError);
  };
  const getABCIndicator = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ABCIndicator", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getABCIndicator`, "get", hSuccess, hError);
  };
  const getStdHUType = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "StdHUType", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getStandHUType`, "get", hSuccess, hError);
  };
  const getQualityInspectionGroup = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "QualityInspectionGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getQualInspGrp`, "get", hSuccess, hError);
  };
  const getQuarantinePeriod = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "QuarantinePeriod", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getBaseUOM`, "get", hSuccess, hError);
  };
  const getDeliveryUnit = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "DeliveryUnit", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getBaseUOM`, "get", hSuccess, hError);
  };
  const getOrderUnit = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "OrderUnit", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getBaseUOM`, "get", hSuccess, hError);
  };
  const getItemCategoryGroup = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ItemCat", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getGenItemCatGroup`, "get", hSuccess, hError);
  };
  const geGentItemCategoryGroup = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ItemCat", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getGenItemCatGroup`, "get", hSuccess, hError);
  };
  const getProductShape = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProductShape", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getProductShape`, "get", hSuccess, hError);
  };
  const getHandlingIndicator = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "HandlingIndicator", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getLogHandlingInd`, "get", hSuccess, hError);
  };
  const getBasicMaterial = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "BasicMaterial", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getBasicMaterial`, "get", hSuccess, hError);
  };
  const getWarehouseMaterialGroup = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "WarehouseMaterialGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getWHMaterialGroup`, "get", hSuccess, hError);
  };
  const getWarehouseStorageConsition = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "WarehouseStorageCondition", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data//getWhseStorCondition`, "get", hSuccess, hError);
  };
  const getCwProfileForCwQuantity = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CWProfileForCWQty", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getCWProfile`, "get", hSuccess, hError);
  };
  const getCatchWeightToleranceGroup = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CatchWTToleranceGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getCatchWtTolGrp`, "get", hSuccess, hError);
  };
  const getRefProductForPkgBuilding = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "RefProductForPackagingBuilding",
          data: data.body,
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getReferenceProduct`, "get", hSuccess, hError);
  };
  const getMaterialGroupForPckMaterial = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "MaterialGroupPackagingMaterials",
          data: data.body,
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getMatGrpPack`, "get", hSuccess, hError);
  };
  const getProductOrientationProfile = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProductOrientationProfile", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getProductOrientationProfile`, "get", hSuccess, hError);
  };
  const getClassType = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ClassType", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getClassBasedOnClassType`, "get", hSuccess, hError);
  };
  const getBasicDetails = () => {
    let viewName = "Basic Data";
    const hSuccess = (data) => {
      dispatch(basicDataTabs(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getViewFieldDetails?viewName=${viewName}`, "get", hSuccess, hError);
  };
  const getSalesDetails = () => {
    let viewName = "Sales";
    const hSuccess = (data) => {
      dispatch(salesDataTabs(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getViewFieldDetails?viewName=${viewName}`, "get", hSuccess, hError);
  };
  const getPurchasingData = () => {
    let viewName = "Purchasing";
    const hSuccess = (data) => {
      dispatch(purchasingDataTabs(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getViewFieldDetails?viewName=${viewName}`, "get", hSuccess, hError);
  };
  const getMRPData = () => {
    let viewName = "MRP";
    const hSuccess = (data) => {
      dispatch(mrpDataTabs(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getViewFieldDetails?viewName=${viewName}`, "get", hSuccess, hError);
  };
  const getAccountingData = () => {
    let viewName = "Accounting";
    const hSuccess = (data) => {
      dispatch(accountingDataTabs(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_MaterialMgmt}/data/getViewFieldDetails?viewName=${viewName}`, "get", hSuccess, hError);
  };

  const handleCreateMultiple = () => {
    setEnableDocumentUpload(true);
  };
  useEffect(() => {
    let tempRequestType;

    if (selectedReqType.length === 1) {
      // If single selection, send as string
      tempRequestType = selectedReqType[0];
    } else if (selectedReqType.length > 1) {
      // If multiple selections, join with $^$
      tempRequestType = selectedReqType.map((item) => item).join("$^$");
    } else {
      // If no selection, send empty string
      tempRequestType = "";
    }

    let tempFilterData = {
      ...rbSearchForm,
      requestType: tempRequestType,
    };

    dispatch(
      commonFilterUpdate({
        module: "RequestBench",
        filterData: tempFilterData,
      })
    );
  }, [selectedReqType]);

  useEffect(() => {
    var tempMatNum = selectedMaterial.map((item) => item?.code).join("$^$");

    let tempFilterData = {
      ...rbSearchForm,
      number: tempMatNum,
    };
    dispatch(
      commonFilterUpdate({
        module: "RequestBench",
        filterData: tempFilterData,
      })
    );
  }, [selectedMaterial]);

  useEffect(() => {
    var tempDivision = selectedDivision.map((item) => item?.code).join("$^$");

    let tempFilterData = {
      ...rbSearchForm,
      division: tempDivision,
    };
    dispatch(
      commonFilterUpdate({
        module: "RequestBench",
        filterData: tempFilterData,
      })
    );
  }, [selectedDivision]);

  useEffect(() => {
    var tempCreatedBy = selectedCreatedBy.map((item) => item?.code).join("$^$");

    let tempFilterData = {
      ...rbSearchForm,
      createdBy: tempCreatedBy,
    };
    dispatch(
      commonFilterUpdate({
        module: "RequestBench",
        filterData: tempFilterData,
      })
    );
  }, [selectedCreatedBy]);

  const getProfitCenter = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProfitCenter", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_ProfitCenter}/data/getProfitCenter`, "get", hSuccess, hError);
  };
  const getControllingAreaPC = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ControllingArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_ProfitCenter}/data/getControllingArea`, "get", hSuccess, hError);
  };
  const getProfitCenterGroupForSearch = (searchControllingArea) => {
    console.log("first", value);
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProfitCtrGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_ProfitCenter}/data/getProfitCtrGroup?controllingArea=${searchControllingArea}`, "get", hSuccess, hError);
  };
  const getCompanyCodeBasedOnControllingArea = (newControllingArea) => {
    console.log("first", value);
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "CompCodeBasedOnControllingArea",
          data: data.body.map((x, idx) => {
            return {
              id: idx,
              companyCodes: x.code,
              companyName: "",
              assigned: "X",
            };
          }),
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_ProfitCenter}/data/getCompCodeBasedOnControllingArea?controllingArea=${newControllingArea.code}`, "get", hSuccess, hError);
  };
  const getCompanyCode = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CompanyCode", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_ProfitCenter}/data/getCompCode`, "get", hSuccess, hError);
  };
  const getProfitCenterGroup = (newControllingArea) => {
    console.log("first", value);
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProfitCtrGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_ProfitCenter}/data/getProfitCtrGroup?controllingArea=${newControllingArea.code}`, "get", hSuccess, hError);
  };
  const getSegment = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Segment", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_ProfitCenter}/data/getSegment`, "get", hSuccess, hError);
  };
  const getLanguageKey = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Language", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_ProfitCenter}/data/getLanguageKey`, "get", hSuccess, hError);
  };
  const getProfitCenterBasicDetails = () => {
    let viewName = "Basic Data";
    const hSuccess = (data) => {
      dispatch(setProfitCenterBasicDataTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_ProfitCenter}/data/getViewFieldDetails?viewName=${viewName}`, "get", hSuccess, hError);
  };
  const getIndicatorsProfitCenter = () => {
    let viewName = "Indicators";
    const hSuccess = (data) => {
      console.log("profit", data);
      dispatch(setProfitCenterIndicatorsTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_ProfitCenter}/data/getViewFieldDetails?viewName=${viewName}`, "get", hSuccess, hError);
  };

  const getCompCodesProfitCenter = () => {
    let viewName = "Comp Codes";
    const hSuccess = (data) => {
      dispatch(setProfitCenterCompCodesTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_ProfitCenter}/data/getViewFieldDetails?viewName=${viewName}`, "get", hSuccess, hError);
  };
  const getAddressProfitCenter = () => {
    let viewName = "Address";
    const hSuccess = (data) => {
      dispatch(setProfitCenterAddressTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_ProfitCenter}/data/getViewFieldDetails?viewName=${viewName}`, "get", hSuccess, hError);
  };
  const getCommunicationProfitCenter = () => {
    let viewName = "Communication";
    const hSuccess = (data) => {
      dispatch(setProfitCenterCommunicationTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_ProfitCenter}/data/getViewFieldDetails?viewName=${viewName}`, "get", hSuccess, hError);
  };
  const getHistoryProfitCenter = () => {
    let viewName = "History";
    const hSuccess = (data) => {
      dispatch(setProfitCenterHistoryTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_ProfitCenter}/data/getViewFieldDetails?viewName=${viewName}`, "get", hSuccess, hError);
  };
  const getUserResponsiblePC = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "UserResponsible", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_ProfitCenter}/data/getUserResponsible`, "get", hSuccess, hError);
  };
  const getFormPlanningTemp = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "FormPlanningTemp", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_ProfitCenter}/data/getFormPlanningTemp`, "get", hSuccess, hError);
  };
  const getRegionPC = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Region", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_ProfitCenter}/data/getRegion`, "get", hSuccess, hError);
  };
  const getCountryOrRegionPC = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CountryReg", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_ProfitCenter}/data/getCountryOrReg`, "get", hSuccess, hError);
  };
  const getJurisdictionPC = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "TaxJur", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_ProfitCenter}/data/getJurisdiction`, "get", hSuccess, hError);
  };

  let a = "NMR20231211035421691";
  useEffect(() => {
    // getUserResponsible();
    // getCostCenterCategory();
    // getBusinessArea();
    // getFunctionalArea();
    // getProfitCenter();
    // getCostingSheet();
    // getCountryOrRegion();
    // getJurisdiction();
    // getRegion();
    // getLanguageKey();
    // getCostCenterBasicDetails();
    // getControlCostCenter();
    // getControllingAreaPC();
    // getCompanyCode();
    // getSegment();
    // getLanguageKey();
    // getUserResponsiblePC();
    // getFormPlanningTemp();
    // getRegionPC();
    // getCountryOrRegionPC();
    // getJurisdictionPC();
    // getProfitCenterBasicDetails();
    // getIndicatorsProfitCenter();
    // getCompCodesProfitCenter();
    // getAddressProfitCenter();
    // getHistoryProfitCenter();
    // getCommunicationProfitCenter();
    // getUserResponsible();
    // getCostCenterCategory();
    // getBusinessArea();
    // getFunctionalArea();
    // getCostingSheet();
    // getCountryOrRegion();
    // getJurisdiction();
    // getRegionCC();
    // getLanguageKeyCC();
    // getCostCenterBasicDetails();
    // getControlCostCenter();
    // getTemplatesCostCenter();
    // getAddressCostCenter();
    // getCommunicationCostCenter();
    // getHistoryCostCenter();
    // getCostCenter();
    // getControllingArea();
    // getHierarchyArea();
    // getCompanyCode();
  }, []);
  const fetchBifurcationDetails = (requestId) => {
      setIsLoading(true);
   
      const hSuccess = (response) => {
        setApiData(response.body);
        setIsLoading(false);
      };
   
      const hError = (err) => {
        setIsLoading(false);
      };
    
      doAjax(
        `/${destination_MaterialMgmt}/data/${requestId.slice(3)}${END_POINTS.REQUEST_BENCH.BIFURCATION_DETAILS}`,
        "get",
        hSuccess,
        hError,
      );
    };
  const getUserResponsible = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "UserResponsible", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_CostCenter}/data/getUserResponsible`, "get", hSuccess, hError);
  };
  const getCostCenterCategory = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CostCenterCategory", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_CostCenter}/data/getCostCenterCategory`, "get", hSuccess, hError);
  };
  const getCostCenter = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CostCenter", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_CostCenter}/data/getCostCenter`, "get", hSuccess, hError);
  };
  // const getHierarchyArea = () => {
  //   var HA = "TZUS";
  //   const hSuccess = (data) => {
  //     dispatch(setDropDown({ keyName: "HierarchyArea", data: data.body }));
  //     console.log("data",data);
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_CostCenter}/data/getHierarchyArea?controllingArea=${HA}`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };
  const getControllingArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ControllingArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_CostCenter}/data/getControllingArea`, "get", hSuccess, hError);
  };

  // const getCompanyCode = () => {
  //   var HA = "TZUS";
  //   const hSuccess = (data) => {
  //     dispatch(setDropDown({ keyName: "CompCode", data: data.body }));
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_CostCenter}/data/getCompCode?contrllingArea=${HA}`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };
  const getActyIndepFormPlngTemp = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ActyIndepFormPlngTemp", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_CostCenter}/data/getCostCenterCategory`, "get", hSuccess, hError);
  };
  const getActyDepFormPlngTemp = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ActyDepFormPlngTemp", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_CostCenter}/data/getCostCenterCategory`, "get", hSuccess, hError);
  };
  const getActyIndepAllocTemp = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ActyIndepAllocTemp", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_CostCenter}/data/getCostCenterCategory`, "get", hSuccess, hError);
  };
  const getActyDepAllocTemp = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ActyDepAllocTemp", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_CostCenter}/data/getCostCenterCategory`, "get", hSuccess, hError);
  };
  const getTemplActStatKeyFigure = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "TemplActStatKeyFigure", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_CostCenter}/data/getCostCenterCategory`, "get", hSuccess, hError);
  };
  const getBusinessArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "BusinessArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_CostCenter}/data/getBusinessArea`, "get", hSuccess, hError);
  };
  const getFunctionalArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "FunctionalArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_CostCenter}/data/getFunctionalArea`, "get", hSuccess, hError);
  };

  const getCostingSheet = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CostingSheet", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_CostCenter}/data/getCostingSheet`, "get", hSuccess, hError);
  };
  const getCountryOrRegion = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CountryReg", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_CostCenter}/data/getCountry`, "get", hSuccess, hError);
  };
  const getJurisdiction = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Jurisdiction", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_CostCenter}/data/getJurisdiction`, "get", hSuccess, hError);
  };
  const getRegionCC = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Region", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_CostCenter}/data/getRegion`, "get", hSuccess, hError);
  };
  const getLanguageKeyCC = (data) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "LanguageKey", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_CostCenter}/data/getLanguageKey`, "get", hSuccess, hError);
  };
  const getCostCenterBasicDetails = () => {
    let viewName = "Basic Data";
    const hSuccess = (data) => {
      dispatch(setCostCenterBasicDataTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_CostCenter}/data/getViewFieldDetails?viewName=${viewName}`, "get", hSuccess, hError);
  };
  const getControlCostCenter = () => {
    let viewName = "Control";
    const hSuccess = (data) => {
      dispatch(setCostCenterControlTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_CostCenter}/data/getViewFieldDetails?viewName=${viewName}`, "get", hSuccess, hError);
  };
  const getTemplatesCostCenter = () => {
    let viewName = "Templates";
    const hSuccess = (data) => {
      dispatch(setCostCenterTemplatesTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_CostCenter}/data/getViewFieldDetails?viewName=${viewName}`, "get", hSuccess, hError);
  };
  const getAddressCostCenter = () => {
    let viewName = "Address";
    const hSuccess = (data) => {
      dispatch(setCostCenterAddressTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_CostCenter}/data/getViewFieldDetails?viewName=${viewName}`, "get", hSuccess, hError);
  };
  const getCommunicationCostCenter = () => {
    let viewName = "Communication";
    const hSuccess = (data) => {
      dispatch(setCostCenterCommunicationTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_CostCenter}/data/getViewFieldDetails?viewName=${viewName}`, "get", hSuccess, hError);
  };
  const getHistoryCostCenter = () => {
    let viewName = "History";
    const hSuccess = (data) => {
      dispatch(setCostCenterHistoryTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_CostCenter}/data/getViewFieldDetails?viewName=${viewName}`, "get", hSuccess, hError);
  };

  const updatedColumns = [
    {
      field: "__check__",
      width: 50,
      renderHeader: () => null,
      headerClassName: "hide-select-all",
    },
    ...columns,
  ];

  const isRowSelectable = (params) => {
    const status = params.row.reqStatus;
    return !(status === REQUEST_STATUS.REJECTED || status === REQUEST_STATUS.SYNDICATED_IN_SAP || status === REQUEST_STATUS.CANCELED || status === REQUEST_STATUS.UPLOAD_FAILED || status === REQUEST_STATUS.SYNDICATED_IN_SAP_DIRECT || status === REQUEST_STATUS.SYNDICATED_PARTIALLY_DIRECT || status === REQUEST_STATUS.SYNDICATED_PARTIALLY);
  };

  const tabContents = [
    [
      <ReusableTable
        isLoading={isLoading}
        paginationLoading={isLoading}
        module={"RequestBench"}
        width="100%"
        title={" Request Details List "}
        rows={tableData ?? []}
        columns={updatedColumns}
        onSearch={(value) => handleSearchAction(value)}
        onRefresh={refreshPage}
        page={page}
        showSearch={true}
        showRefresh={true}
        showSelectedCount={true}
        showExport={true}
        pageSize={pageSize}
        rowCount={count ?? rmDataRows?.length ?? 0}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        getRowIdValue={"id"}
        hideFooter={true}
        tempheight={"calc(100vh - 320px)"}
        checkboxSelection={true}
        disableSelectionOnClick={true}
        isRowSelectable={isRowSelectable}
        status_onRowDoubleClick={true}
        onRowsSelectionHandler={onRowsSelectionHandler}
        callback_onRowDoubleClick={(params) => {
          const isBifurcated = params.row.isBifurcated === true || params.row.isBifurcated === "true";          
          const RequestId = params.row.requestId;
          const requestType = params.row.requestType;
          if(params?.row?.reqStatus === REQUEST_STATUS?.CANCELED) {
            setOpenSnackbar(true);
            setMessageDialogMessage(ERROR_MESSAGES?.CANCELED_ERR);
            setAlertType("error");
            return;
          }
          if(isBifurcated) {
            return;
          }
          else{
          navigate(`/requestBench/createRequest?RequestId=${RequestId}&RequestType=${requestType}&reqBench=${true}`, {
            state: params.row,
          });
          }
        }}
        // setShowWork={setShowWork}
        stopPropagation_Column={"action"}
        showCustomNavigation={true}
      />,
    ],
  ];

  const handleDelete = () => {
    handleCancel(isDeleteDialogVisible?.data.row.requestId, isDeleteDialogVisible?.data.row.requestType);
  };
  const SelectionSummaryfn = () => {
    return <SelectionSummary selectedRows={selectedRows} count={count} tableData={tableData} handleMassCancel={handleMassCancel} />;
  };
  return (
    <div ref={ref_elementForExport}>
      {openAlldataDialog && (
        <ReusableDialogForAllData
          dialogState={false}
          openReusableDialog={handleAllDataDialogOpen}
          closeReusableDialog={handleAllDataDialogClose}
          // dialogTitle={messageDialogTitle}
          // dialogMessage={messageDialogMessage}
          rowsDataForPayload={rowsForDialog}
          handleDialogConfirm={handleMessageDialogClose}
          dialogOkText={"OK"}
          // handleExtraButton={handleMessageDialogNavigate}
          // dialogSeverity={messageDialogSeverity}
        />
      )}
      <ReusableSnackBar openSnackBar={openSnackbar} alertMsg={messageDialogMessage} alertType={alertType} handleSnackBarClose={handleSnackBarClose} />
      {openPopup && (
        <BifurcationPopup
          open={openPopup}
          onClose={() => setOpenPopup(false)}
          rowData={selectedRow}
          apiData={apiData}
        />
      )}
      <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
        <Stack spacing={1}>
          {/* Information */}
          <Grid container mt={0} sx={outermostContainer_Information}>
            <Grid item md={4}>
              <Typography variant="h3">
                <strong>Request Management</strong>
              </Typography>
              <Typography variant="body2" color="#777">
                This view displays the list of Active Requests
              </Typography>
            </Grid>

            
          </Grid>
          <Grid container>
            <Grid item md={12}>
            <StyledAccordion defaultExpanded={false}>
              <StyledAccordionSummary
                  expandIcon={<ExpandMoreIcon sx={{ fontSize: '1.25rem', color: colors.primary.main }} />}
                  aria-controls="panel1a-content"
                  id="panel1a-header"
                >
                  <FilterListIcon sx={{ fontSize: '1.25rem', marginRight: 1, color: colors.primary.main }} />
                  <Typography
                    sx={{
                     fontSize: '0.875rem',
                      fontWeight: 600,
                      color: colors.primary.dark,
                    }}
                  >
                    Filter Request Bench
                  </Typography>
                </StyledAccordionSummary>
                <AccordionDetails sx={{ padding: 0 }}>
                <FilterContainer container>

                      <Grid item md={2}>
                        <LabelTypography sx={font_Small}>Parent Request ID</LabelTypography>
                        <StyledTextField sx={font_Small} size="small" fullWidth onChange={handleRequestId} placeholder="ENTER PARENT REQUEST ID" value={rbSearchForm?.requestId} />
                      </Grid>
                      <Grid item md={2}>
                        <LabelTypography sx={font_Small}>Child Request ID</LabelTypography>
                        <StyledTextField sx={font_Small} size="small" fullWidth onChange={handleChildRequestId} placeholder="ENTER CHILD REQUEST ID" value={rbSearchForm?.childRequestId} />
                      </Grid>
                      <Grid item md={2}>
                        <LabelTypography sx={font_Small}>Request Type</LabelTypography>
                        <AutoCompleteSimpleDropDown
                          options={allOptions}
                          value={typeof rbSearchForm?.requestType === "string" && rbSearchForm.requestType ? rbSearchForm.requestType.split("$^$") : []}
                          onChange={(newValue) => {
                            let tempRequestType;
                            if (Array.isArray(newValue)) {
                              if (newValue.length === 1) {
                                tempRequestType = newValue[0];
                              } else if (newValue.length > 1) {
                                tempRequestType = newValue.join("$^$");
                              } else {
                                tempRequestType = "";
                              }
                            } else {
                              tempRequestType = newValue || "";
                            }

                            let tempFilterData = {
                              ...rbSearchForm,
                              requestType: tempRequestType,
                            };
                            dispatch(
                              commonFilterUpdate({
                                module: "RequestBench",
                                filterData: tempFilterData,
                              })
                            );
                          }}
                          placeholder="SELECT REQUEST TYPE"
                        />
                      </Grid>

                      <Grid item md={2}>
                        <LabelTypography sx={font_Small}>Request Priority</LabelTypography>
                        <AutoCompleteSimpleDropDown
                          options={priortyOptions}
                          value={selectedOptions}
                          onChange={(newValue) => {
                            handleChangePriority({
                              target: {
                                value: newValue,
                              },
                            });
                          }}
                          placeholder="SELECT REQUEST PRIORITY"
                        />
                      </Grid>

                      
                      {userRoles.includes(`${ROLES.SUPER_USER}`) ? (
                        <Grid item md={3}>
                          <LabelTypography sx={font_Small}>Created By</LabelTypography>
                          <FormControl fullWidth size="small">
                            <Autocomplete
                              fullWidth
                              size="small"
                              value={selectedCreatedBy}
                              multiple
                              disableCloseOnSelect
                              limitTags={1}
                              noOptionsText={
                                isDropDownLoading ? (
                                  <Box
                                    sx={{
                                      display: "flex",
                                      justifyContent: "center",
                                      mt: 1,
                                      zIndex: 9999,
                                      top: "10px",
                                    }}
                                  >
                                    <CircularProgress size={20} />
                                  </Box>
                                ) : (
                                  "No Data Available"
                                )
                              }
                              onChange={(e, value, reason) => {
                                if (reason === "clear" || value?.length === 0) {
                                  setSelectedCreatedBy([]);
                                  // TO BE USED LATER
                                  //setselectedPreset([]);
                                  return;
                                }

                                if (value.length > 0 && value[value.length - 1]?.code === "Select All") {
                                  handleSelectAllCreatedBy();
                                } else {
                                  setSelectedCreatedBy(value);
                                }
                              }}
                              options={createdByOptions?.length ? [{ code: "Select All", desc: "" }, ...createdByOptions] : createdByOptions ?? []}
                              getOptionLabel={(option) => {
                                if (option?.code) return `${option?.code} - ${option?.desc}` ?? "";
                                else return "";
                              }}
                              renderOption={(props, option, { selected }) => (
                                <li {...props}>
                                  <FormGroup>
                                    <FormControlLabel
                                      control={<Checkbox checked={isCreatedBySelected(option) || (option?.code === "Select All" && selectedCreatedBy?.length === createdByOptions?.length)} />}
                                      label={
                                        <Typography style={{ fontSize: 12 }}>
                                          {option?.desc ? (
                                            <>
                                              <strong>{option.code}</strong> - {option.desc}
                                            </>
                                          ) : (
                                            <strong>{option.code}</strong>
                                          )}
                                        </Typography>
                                      }
                                    />
                                  </FormGroup>
                                </li>
                              )}
                              renderTags={(selected, getTagProps) => {
                                const selectedOptionsText = selected.map((option) => `${option.code} `).join("<br />");
                                return selected.length > 1 ? (
                                  <>
                                    <Chip
                                      sx={{
                                        height: 25,
                                        fontSize: "0.85rem",
                                        ".MuiChip-label": { padding: "0 6px" },
                                      }}
                                      label={`${selected[0]?.code}`}
                                      {...getTagProps({ index: 0 })}
                                    />
                                    <Chip
                                      sx={{
                                        height: 25,
                                        fontSize: "0.85rem",
                                        ".MuiChip-label": { padding: "0 6px" },
                                      }}
                                      label={`+${selected.length - 1}`}
                                      onMouseEnter={(event) => handlePopoverOpen(event, selectedOptionsText)}
                                      onMouseLeave={handlePopoverClose}
                                    />
                                    <Popover
                                      id={popoverId}
                                      open={isPopoverVisible}
                                      anchorEl={popoverAnchorEl}
                                      onClose={handlePopoverClose}
                                      anchorOrigin={{
                                        vertical: "bottom",
                                        horizontal: "center",
                                      }}
                                      transformOrigin={{
                                        vertical: "top",
                                        horizontal: "center",
                                      }}
                                      onMouseEnter={handleMouseEnterPopover}
                                      onMouseLeave={handleMouseLeavePopover}
                                      ref={popoverRef}
                                      sx={{
                                        "& .MuiPopover-paper": {
                                          backgroundColor: "#f5f5f5",
                                          boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
                                          borderRadius: "8px",
                                          padding: "10px",
                                          fontSize: "0.875rem",
                                          color: "#4791db",
                                          border: "1px solid #ddd",
                                        },
                                      }}
                                    >
                                      <Box
                                        sx={{
                                          maxHeight: "270px",
                                          overflowY: "auto",
                                          padding: "5px",
                                        }}
                                        dangerouslySetInnerHTML={{ __html: popoverContent }}
                                      />
                                    </Popover>
                                  </>
                                ) : (
                                  selected.map((option, index) => (
                                    <Chip
                                      sx={{
                                        height: 25,
                                        fontSize: "0.85rem",
                                        ".MuiChip-label": { padding: "0 6px" },
                                      }}
                                      label={`${option?.code}`}
                                      {...getTagProps({ index })}
                                    />
                                  ))
                                );
                              }}
                              renderInput={(params) => (
                                <Tooltip title={params.inputProps.value.length == 0 ? "Type to search" : ""} arrow disableHoverListener={params.inputProps.value.length >= 1} placement="top">
                                  <TextField
                                    sx={{
                                      fontSize: "12px !important",
                                      "& .MuiOutlinedInput-root": {
                                        height: 35,
                                      },
                                      "& .MuiInputBase-input": {
                                        padding: "10px 14px",
                                      },
                                    }}
                                    {...params}
                                    variant="outlined"
                                    placeholder="SELECT CREATED BY"
                                    onChange={(e) => {
                                      handleCreatedByInputChange(e);
                                    }}
                                  />
                                </Tooltip>
                              )}
                            />
                          </FormControl>
                        </Grid>
                      ) : (
                        <Grid item md={4}>
                          <Typography sx={font_Small}>Created By</Typography>

                          <TextField fullWidth size="small" disabled={userData?.userRoles?.includes("Z4S:ALL:RTR:PC:PRF_CTR_MNT") ? false : true} value={userData?.userRoles?.includes("Z4S:ALL:RTR:PC:PRF_CTR_MNT") ? rbSearchForm?.createdBy : userData?.emailId} onChange={handleCreatedBy} placeholder="ENTER CREATED BY" />
                        </Grid>
                      )}
                      <Grid item md={2}>
                        <LabelTypography sx={font_Small}>Status</LabelTypography>
                        <AutoCompleteSimpleDropDown
                          options={[...names.filter((name) => name !== "Select All").sort((a, b) => a.localeCompare(b))]}
                          value={rbSearchForm?.reqStatus}
                          onChange={(newValue) => {
                            const event = {
                              target: {
                                name: "reqStatus",
                                value: newValue,
                              },
                            };
                            handleStatus(event);
                          }}
                          placeholder="SELECT REQUEST STATUS"
                        />
                      </Grid>
                      <Grid item md={2}>
                        <LabelTypography sx={font_Small}>Division</LabelTypography>
                        <FormControl fullWidth size="small">
                          <Autocomplete
                            fullWidth
                            size="small"
                            value={selectedDivision}
                            multiple
                            disableCloseOnSelect
                            limitTags={1}
                            noOptionsText={
                              isDropDownLoading ? (
                                <Box
                                  sx={{
                                    display: "flex",
                                    justifyContent: "center",
                                    mt: 1,
                                    zIndex: 9999,
                                    top: "10px",
                                  }}
                                >
                                  <CircularProgress size={20} />
                                </Box>
                              ) : (
                                "No Data Available"
                              )
                            }
                            onChange={(e, value, reason) => {
                              if (reason === "clear" || value?.length === 0) {
                                setSelectedDivision([]);
                                // TO BE USED LATER
                                //setselectedPreset([]);
                                return;
                              }

                              if (value.length > 0 && value[value.length - 1]?.code === "Select All") {
                                handleSelectAllDivision();
                              } else {
                                setSelectedDivision(value);
                              }
                            }}
                            options={dropDownData?.reqBenchDivision?.length ? [{ code: "Select All", desc: "" }, ...dropDownData?.reqBenchDivision] : dropDownData?.reqBenchDivision ?? []}
                            getOptionLabel={(option) => {
                              if (option?.code) return `${option?.code} - ${option?.desc}` ?? "";
                              else return "";
                            }}
                            renderOption={(props, option, { selected }) => (
                              <li {...props}>
                                <FormGroup>
                                  <FormControlLabel
                                    control={<Checkbox checked={isDivisionSelected(option) || (option?.code === "Select All" && selectedDivision?.length === dropDownData?.reqBenchDivision?.length)} />}
                                    label={
                                      <Typography style={{ fontSize: 12 }}>
                                        {option?.desc ? (
                                          <>
                                            <strong>{option.code}</strong> - {option.desc}
                                          </>
                                        ) : (
                                          <strong>{option.code}</strong>
                                        )}
                                      </Typography>
                                    }
                                  />
                                </FormGroup>
                              </li>
                            )}
                            renderTags={(selected, getTagProps) => {
                              const selectedOptionsText = selected.map((option) => `${option.code} `).join("<br />");
                              return selected.length > 1 ? (
                                <>
                                  <Chip
                                    sx={{
                                      height: 25,
                                      fontSize: "0.85rem",
                                      ".MuiChip-label": { padding: "0 6px" },
                                    }}
                                    label={`${selected[0]?.code}`}
                                    {...getTagProps({ index: 0 })}
                                  />
                                  <Chip
                                    sx={{
                                      height: 25,
                                      fontSize: "0.85rem",
                                      ".MuiChip-label": { padding: "0 6px" },
                                    }}
                                    label={`+${selected.length - 1}`}
                                    onMouseEnter={(event) => handlePopoverOpen(event, selectedOptionsText)}
                                    onMouseLeave={handlePopoverClose}
                                  />
                                  <Popover
                                    id={popoverId}
                                    open={isPopoverVisible}
                                    anchorEl={popoverAnchorEl}
                                    onClose={handlePopoverClose}
                                    anchorOrigin={{
                                      vertical: "bottom",
                                      horizontal: "center",
                                    }}
                                    transformOrigin={{
                                      vertical: "top",
                                      horizontal: "center",
                                    }}
                                    onMouseEnter={handleMouseEnterPopover}
                                    onMouseLeave={handleMouseLeavePopover}
                                    ref={popoverRef}
                                    sx={{
                                      "& .MuiPopover-paper": {
                                        backgroundColor: "#f5f5f5",
                                        boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
                                        borderRadius: "8px",
                                        padding: "10px",
                                        fontSize: "0.875rem",
                                        color: "#4791db",
                                        border: "1px solid #ddd",
                                      },
                                    }}
                                  >
                                    <Box
                                      sx={{
                                        maxHeight: "270px",
                                        overflowY: "auto",
                                        padding: "5px",
                                      }}
                                      dangerouslySetInnerHTML={{ __html: popoverContent }}
                                    />
                                  </Popover>
                                </>
                              ) : (
                                selected.map((option, index) => (
                                  <Chip
                                    sx={{
                                      height: 25,
                                      fontSize: "0.85rem",
                                      ".MuiChip-label": { padding: "0 6px" },
                                    }}
                                    label={`${option?.code}`}
                                    {...getTagProps({ index })}
                                  />
                                ))
                              );
                            }}
                            renderInput={(params) => (
                              <Tooltip title={params.inputProps.value.length == 0 ? "Type to search" : ""} arrow disableHoverListener={params.inputProps.value.length >= 1} placement="top">
                                <TextField
                                  sx={{
                                    fontSize: "12px !important",
                                    "& .MuiOutlinedInput-root": {
                                      height: 35,
                                    },
                                    "& .MuiInputBase-input": {
                                      padding: "10px 14px",
                                    },
                                  }}
                                  {...params}
                                  variant="outlined"
                                  placeholder="SELECT DIVISION"
                                  onChange={(e) => {
                                    handleDivInputChange(e);
                                  }}
                                />
                              </Tooltip>
                            )}
                          />
                        </FormControl>
                      </Grid>
                      <Grid item md={2}>
                        <LabelTypography sx={font_Small}>Created Date</LabelTypography>
                        <FormControl fullWidth sx={{ padding: 0, height: "37px" }}>
                          <LocalizationProvider dateAdapter={AdapterDateFns}>
                            <DateRange
                              handleDate={handleDate}
                              // onChange={(e) => handleMatTypeChange(e)
                              date={rbSearchForm?.createdOn}
                            />
                            {/* <DatePicker
                                size="small"
                                
                                inputFormat="dd/MM/yyyy" /> */}
                          </LocalizationProvider>
                        </FormControl>
                      </Grid>
                      <Grid item md={2}>
                        <LabelTypography sx={font_Small}>Material Number</LabelTypography>
                        <FormControl size="small" fullWidth>
                          <MaterialDropdown matGroup={materialOptions} selectedMaterialGroup={selectedMaterial} setSelectedMaterialGroup={setSelectedMaterial} isDropDownLoading={isDropDownLoading} placeholder="Enter Material Number" onInputChange={handleMatInputChange} minCharacters={4} />
                        </FormControl>
                      </Grid>

                      <Grid item md={2}>
                        <LabelTypography sx={font_Small}>Template Name</LabelTypography>
                        <FormControl size="small" fullWidth>
                          <Select
                            placeholder={"SELECT TEMPLATE NAME"}
                            multiple
                            size="small"
                            value={rbSearchForm?.tempName}
                            name="reqStatus"
                            onChange={(e) => handletempName(e)}
                            displayEmpty={true}
                            renderValue={(selected) => {
                              if (selected.length === 0) {
                                return (
                                  <div
                                    style={{
                                      color: `${colors.placeholder.color}`,
                                      fontSize: "12px",
                                    }}
                                  >
                                    SELECT TEMPLATE NAME
                                  </div>
                                );
                              }
                              return selected.join(", ");
                            }}
                            MenuProps={MenuProps}
                          >
                            <MenuItem sx={font_Small} disabled value={""}>
                              <div style={{ color: `${colors.placeholder.color}`, fontSize: "12px" }}>Select Template Name</div>
                            </MenuItem>
                            {["Select All", ...tempNames.filter((name) => name !== "Select All").sort((a, b) => a.localeCompare(b))].map((name) => (
                              <MenuItem sx={font_Small} key={name} value={name} style={{ fontSize: "12px !important" }}>
                                <Checkbox checked={name === "Select All" ? rbSearchForm?.tempName.length === tempNames.length : rbSearchForm?.tempName.indexOf(name) > -1} />
                                <ListItemText sx={font_Small} primary={name} style={{ fontSize: "12px !important" }} />
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Grid>
                    </FilterContainer>
                    <ButtonContainer>
                    <ActionButton
                      variant="outlined"
                      size="small"
                      startIcon={<ClearIcon sx={{ fontSize: '1rem' }} />}
                      onClick={handleClear}
                      sx={{ borderColor: colors.primary.main, color: colors.primary.main }}>
                        Clear
                      </ActionButton>

                      <ActionButton
                        variant="contained"
                        size="small"
                        startIcon={<SearchIcon sx={{ fontSize: '1rem' }} />}
                        onClick={() => getFilter()}
                        sx={{ backgroundColor: colors.primary.main }}
                      >
                        Search
                      </ActionButton>
                    </ButtonContainer>
                  </AccordionDetails>
                </StyledAccordion>
              </Grid>
          </Grid>

          <Grid container>
            <Tabs
              value={activeTab}
              onChange={handleChange}
              variant="scrollable"
              sx={{
                background: "#FFF",
                borderBottom: "1px solid #BDBDBD",
                width: "100%",
              }}
              aria-label="mui tabs example"
            >
              {tabsArray.map((factor, index) => (
                <Tab sx={{ fontSize: "12px", fontWeight: "700" }} key={index} label={factor === tabsArrayHashMap["0"] ? factor + " (" + materialCount + ")" : factor === tabsArrayHashMap["1"] ? factor + " (" + costCenterCount + ")" : factor === tabsArrayHashMap["2"] ? factor + " (" + profitCenterCount + ")" : factor === tabsArrayHashMap["3"] ? factor + " (" + bankKeyCount + ")" : factor + " (" + glCount + ")"} />
              ))}
            </Tabs>
          </Grid>

          <Grid container>
            {tabContents &&
              tabContents[activeTab]?.map((cardContent, index) => (
                <Box key={index} sx={{ width: "100%" }}>
                  {cardContent}
                </Box>
              ))}
          </Grid>
          <div ref={ref_elementForExport}>{SelectionSummaryfn()}</div>

          {/* {
            showBtmNav && */}
        </Stack>
        {isDeleteDialogVisible?.isVisible && (
          <CustomDialog isOpen={isDeleteDialogVisible?.isVisible} titleIcon={<DeleteForeverOutlined size="small" color="error" sx={{ fontSize: "20px" }} />} Title={"Cancel Request!"} handleClose={() => setIsDeleteDialogVisible({ ...isDeleteDialogVisible, isVisible: false })}>
            <DialogContent sx={{ mt: 2 }}>{DIALOUGE_BOX_MESSAGES.CANCEL_MESSAGE}</DialogContent>
            <DialogActions>
              <Button variant="outlined" size="small" sx={{ ...button_Outlined }} onClick={() => setIsDeleteDialogVisible({ ...isDeleteDialogVisible, isVisible: false })}>
                {DELETE_MODAL_BUTTONS_NAME.CANCEL}
              </Button>
              <Button variant="contained" size="small" sx={{ ...button_Primary }} onClick={handleDelete}>
                {DELETE_MODAL_BUTTONS_NAME.DELETE}
              </Button>
            </DialogActions>
          </CustomDialog>
        )}
        {blurLoading && <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />}
      </div>
    </div>
  );
};

export default RequestBench;
