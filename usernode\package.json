{"dependencies": {"@sap/approuter": "6.8.0", "@sap/xsenv": "^2.2.0", "@sap/xssec": "^2.2.5", "body-parser": "^1.19.0", "express": "^4.17.1", "passport": "^0.4.1", "path": "^0.12.7", "request": "*"}, "description": "Node Module for User details", "devDependencies": {"jest": "^23.6.0", "jest-junit": "^5.2.0"}, "files": [], "main": "server.js", "name": "mdg-usernode-dev", "scripts": {"test": "node node_modules/jest/bin/jest --config jest.json", "test-coverage": "node node_modules/jest/bin/jest --coverage --config jest.json", "start": "node server.js"}, "version": "1.0.0"}