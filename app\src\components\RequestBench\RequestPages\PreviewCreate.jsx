import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Grid,
  Tooltip,
  Typography,
  Stack,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useSelector } from "react-redux";
import { TAX_DATA } from "@constant/enum";
import { useState, useEffect } from "react";
import { capitalize } from "@helper/helper";
import DescriptionData from "../Preview/DescriptionData";
import UnitOfMeasure from "../Preview/UnitOfMeasure";
import TaxData from "../Preview/TaxData";
import MaterialRows from "../Preview/MaterialRows";
import { colors } from "@constant/colors";

const PreviewCreate = () => {
  const storedRows = useSelector((state) => state.request.materialRows);
  const singlePayloadData = useSelector((state) => state.payload);
  const [selectedMaterialId, setSelectedMaterialId] = useState(null);
  const [selectedRowData, setSelectedRowData] = useState(null);
  const selectedMaterialPayload = selectedMaterialId && singlePayloadData?.[selectedMaterialId]?.payloadData;
  const descriptionDataPayload = selectedMaterialId && singlePayloadData?.[selectedMaterialId]?.additionalData;
  const unitOfMeasureDataPayload = selectedMaterialId && singlePayloadData?.[selectedMaterialId]?.unitsOfMeasureData;


  const allTabsData = useSelector((state) => state.tabsData.allTabsData);
  
  useEffect(() => {
    if (storedRows && storedRows?.length > 0 && !selectedMaterialId) {
      setSelectedMaterialId(storedRows[0].id);
      setSelectedRowData(storedRows[0]);
    }
  }, [storedRows, selectedMaterialId]);
  
  const handleMaterialClick = (materialId) => {
    setSelectedMaterialId(materialId);
    const selectedRow = storedRows?.find(row => row?.id === materialId);
    if (selectedRow) {
      setSelectedRowData(selectedRow);
    }
  };

  const renderFieldData = () => {
    if (!selectedMaterialId || !selectedMaterialPayload || Object.keys(selectedMaterialPayload)?.length === 0) {
      return (
        <Typography variant="body1" sx={{ padding: "16px", textAlign: "center", color: colors.text.secondary }}>
          {!selectedMaterialId ? "Please select a material to view details" : "No field data available"}
        </Typography>
      );
    }

    const viewToCategoryMapping = {
      "Basic Data": ["Basic Data", "Basic"],
      "Sales": ["Sales"],
      "Purchasing": ["Purchasing"],
      "MRP": ["MRP"],
      "Accounting": ["Accounting"],
      "Costing": ["Costing"],
      "Warehouse": ["Warehouse"],
      "Sales-Plant": ["Sales-Plant"],
      "Work Scheduling": ["Work Scheduling", "WorkScheduling"]
    };

    const allowedCategories = new Set();
    if (selectedRowData && selectedRowData.views) {
      selectedRowData?.views?.forEach(view => {
        const mappedCategories = viewToCategoryMapping[view] || [view];
        mappedCategories?.forEach(category => allowedCategories.add(category));
      });
    }
    // Remove TaxData from allowed categories as we're handling it separately
    allowedCategories.delete(TAX_DATA.CATEGORY);

    const filteredCategories = Object.keys(selectedMaterialPayload).filter(
      category => {
        // Skip specific categories
        if (category === "Request Header" || category === "Header" || category === "Tostroragelocationdata" || category === TAX_DATA.CATEGORY) {
          return false;
        }
        
        // Skip categories not in the selected views
        if (!allowedCategories.has(category)) {
          return false;
        }
        
        // Skip empty objects
        const categoryData = selectedMaterialPayload[category];
        if (typeof categoryData === 'object' && Object.keys(categoryData).length === 0) {
          return false;
        }
        
        return true;
      }
    );
    return (
      <Stack spacing={2}>
        {filteredCategories?.map((category) => (
          <Accordion key={category} sx={{ boxShadow: 3 }} expanded={true}>
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              sx={{
                backgroundColor: colors.background.subtle,
                padding: "8px 16px",
                "&:hover": { backgroundColor: colors.background.subtle },
                cursor: "default"
              }}
            >
              <Typography variant="h6" sx={{ fontWeight: "bold", color: colors.text.primary }}>{capitalize(category)}</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Stack spacing={2}>
                {Object.keys(selectedMaterialPayload[category])?.map((subCategory) => (
                  <Accordion key={subCategory} sx={{ boxShadow: 2 }} expanded={true}>
                    <AccordionSummary
                      expandIcon={<ExpandMoreIcon />}
                      sx={{
                        backgroundColor: colors.background.lightCard,
                        padding: "6px 16px",
                        cursor: "default"
                      }}
                    >
                      <Typography variant="subtitle1" sx={{ fontWeight: "bold", color: colors.text.primary }}>{capitalize(subCategory)}</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Grid container spacing={2} sx={{ padding: "8px" }}>
                        {Object.entries(selectedMaterialPayload[category][subCategory])?.map(([key, value], index) => {
                          // Find the field label from allTabsData
                          let fieldName = null;
                          let fieldFound = false;
                          
                          if (allTabsData && allTabsData[category]) {
                            // Search through all subcategories in allTabsData
                            for (const tabSubCategory in allTabsData[category]) {
                              const fieldData = allTabsData[category][tabSubCategory].find(
                                field => field.jsonName === key
                              );
                              if (fieldData) {
                                fieldName = fieldData.fieldName;
                                fieldFound = true;
                                break;
                              }
                            }
                          }
                          if (!fieldFound) {
                            return null;
                          }
             
                          let displayValue = value;
                          
               
                          if (value === null || value === undefined) {
                            displayValue = "--";
                          }
                      
                          else if (value instanceof Date) {
                            displayValue = value.toLocaleDateString();
                          } 
                          
                          else if (typeof value === 'string' && value.startsWith('/Date(') && value.endsWith(')/')) {
                            const timestamp = parseInt(value.slice(6, -2));
                            displayValue = new Date(timestamp).toLocaleDateString();
                          }
                    
                          else if (typeof value === 'object' && value !== null) {
                            displayValue = JSON.stringify(value);
                          }
                          
                          return (
                            <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
                              <Box sx={{ 
                                display: 'flex', 
                                flexDirection: 'column',
                                mb: 1
                              }}>
                                <Typography 
                                  variant="caption" 
                                  sx={{ 
                                    fontWeight: 'bold',
                                    mb: 0.5,
                                    color: colors.text.primary
                                  }}
                                >
                                  {fieldName}
                                </Typography>
                                <Box
                                  sx={{
                                    border: `1px solid ${colors.border.light}`,
                                    borderRadius: '4px',
                                    padding: '8px 12px',
                                    backgroundColor: colors.background.lightCard,
                                    minHeight: '36px',
                                    display: 'flex',
                                    alignItems: 'center'
                                  }}
                                >
                                  <Tooltip title={displayValue === "--" ? "" : String(displayValue)} arrow placement="top">
                                    <Typography
                                      variant="body2"
                                      sx={{
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        whiteSpace: 'nowrap',
                                        width: '100%',
                                        color: displayValue === "--" ? colors.placeholder.dark : colors.text.primary,
                                        fontStyle: displayValue === "--" ? "italic" : "normal"
                                      }}
                                    >
                                      {String(displayValue)}
                                    </Typography>
                                  </Tooltip>
                                </Box>
                              </Box>
                            </Grid>
                          );
                        })?.filter(item => item !== null)}
                      </Grid>
                    </AccordionDetails>
                  </Accordion>
                ))}
              </Stack>
            </AccordionDetails>
          </Accordion>
        ))}
      </Stack>
    );
  };

  return (
    <Stack spacing={2} sx={{ padding: "16px" }}>
      <Accordion sx={{ boxShadow: 3 }} expanded={true}>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="material-data-content"
          id="material-data-header"
          sx={{
            backgroundColor: colors.background.subtle,
            padding: "8px 16px",
            "&:hover": { backgroundColor: colors.background.subtle },
          }}
        >
          <Typography variant="h6" sx={{ fontWeight: "bold", color: colors.text.primary }}>
            {storedRows && storedRows.length === 1
              ? "Material Data"
              : "Materials Data"}
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <MaterialRows 
            storedRows={storedRows} 
            selectedMaterialId={selectedMaterialId} 
            handleMaterialClick={handleMaterialClick} 
          />
        </AccordionDetails>
      </Accordion>
      
      
      {selectedMaterialId && (
        <Accordion sx={{ boxShadow: 3 }} expanded={true}>
          <AccordionSummary
            expandIcon={<ExpandMoreIcon />}
            aria-controls="field-data-content"
            id="field-data-header"
            sx={{
              backgroundColor: colors.background.subtle,
              padding: "8px 16px",
              "&:hover": { backgroundColor: colors.background.subtle },
            }}
          >
            <Typography variant="h6" sx={{ fontWeight: "bold", color: colors.text.primary }}>
              Material Details
            </Typography>
          </AccordionSummary>
          <AccordionDetails>{renderFieldData()}</AccordionDetails>
        </Accordion>
      )}
      <DescriptionData descriptionDataPayload={descriptionDataPayload} />
      
      <UnitOfMeasure unitOfMeasureDataPayload={unitOfMeasureDataPayload} />
      
      <TaxData taxDataPayload={selectedMaterialId && selectedMaterialPayload && selectedMaterialPayload[TAX_DATA.CATEGORY]} />
    </Stack>
  );
};

export default PreviewCreate;
