import { doAjax } from '@components/Common/fetchService';
import React, { useState } from 'react'
import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';
import { useLocation, useNavigate } from "react-router-dom";
import { pushDisplayPayload, setDataLoading, setDynamicKeyValue, setFCRows } from '../app/payloadslice';
import { destination_MaterialMgmt } from '../destinationVariables';
import { updateCurrentCount, updateExistingCreatePages, updateNextButtonStatus, updatePage, updateTotalCount } from "../app/paginationSlice"
import useChangeMaterialRows from './useChangeMaterialRows';
import useChangeMaterialRowsRequestor from './useChangeMaterialRowsRequestor';
import { PAGE_TYPE, REQUEST_TYPE } from '@constant/enum';
import { END_POINTS } from '@constant/apiEndPoints';
import useLogger from './useLogger';
import { transformApiResponseToReduxPayload } from '../functions';
import { pushMaterialRows, setMaterialRows } from '@app/requestDataSlice';
import useFinanceCostingRows from './useFinanceCostingRows';


const useDisplayCall = () => {
    const dispatch = useDispatch();
    const location = useLocation();
    const { fetchDisplayDataRows } = useChangeMaterialRows();
    const { fetchDisplayDataRequestor } = useChangeMaterialRowsRequestor();
    const { createFCRows } = useFinanceCostingRows();
    const taskData = useSelector((state) => state.userManagement?.taskData);
    const paginationData = useSelector((state) => state.paginationData);
    const prevFcRows = useSelector((state) => state.payload.fcRows);
    const changeFieldRowsDisplay = useSelector((state) => state.payload.changeFieldRowsDisplay);
    const storedRows = useSelector((state) => state.request.materialRows);
    const initialPayload = useSelector((state) => state.payload.payloadData);
    const requestorPayload = useSelector((state) => state.payload.requestorPayload);
    const queryParams = new URLSearchParams(location.search);
    const RequestId = queryParams.get('RequestId');
    const RequestType = queryParams.get('RequestType');
    const reqBench = queryParams.get('reqBench');
    const {customError} = useLogger()
    const getNextDisplayDataForChange = async (type, action) => {   
        if(type === PAGE_TYPE?.DISPLAY) {
            if(changeFieldRowsDisplay[paginationData?.page]) {
                if(paginationData?.totalElements > (paginationData?.page+1) * paginationData?.size) {
                   dispatch(updateCurrentCount((paginationData?.page+1) * paginationData?.size))
                } else {
                   dispatch(updateCurrentCount(paginationData?.totalElements))
                }
                return;
            }
            getDisplaydata();
        }
        else if(type === PAGE_TYPE?.REQUESTOR) {
            if(changeFieldRowsDisplay[paginationData?.page]) {
                if(paginationData?.totalElements > (paginationData?.page+1) * paginationData?.size) {
                    dispatch(updateCurrentCount((paginationData?.page+1) * paginationData?.size))
                } else {
                    dispatch(updateCurrentCount(paginationData?.totalElements))
                }
                return;
            }
            const result = await fetchDisplayDataRequestor(initialPayload?.TemplateName, requestorPayload);
        }
    }
    const getNextDisplayDataForCreate = async () => {
        if(paginationData?.existingCreatePages.includes(paginationData?.page)) {
            return;
        }
        getDisplaydata();
    }
    const getDisplaydata = () => {
    dispatch(setDataLoading(true));
    let payload = {};

    if (reqBench) {
        payload = {
        massSchedulingId: (RequestType === REQUEST_TYPE?.FINANCE_COSTING) ? RequestId.slice(3) : "",
        massCreationId: (RequestType === REQUEST_TYPE?.CREATE || RequestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD) ? RequestId.slice(3) : "",
        massChangeId: (RequestType === REQUEST_TYPE?.CHANGE || RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD) ? RequestId.slice(3) : "",
        massExtendId: (RequestType === REQUEST_TYPE?.EXTEND || RequestType === REQUEST_TYPE?.EXTEND_WITH_UPLOAD) ? RequestId.slice(3) : "",
        screenName: RequestType ? RequestType : "",
        dtName: "MDG_MAT_MATERIAL_FIELD_CONFIG",
        version: "v2",
        page: paginationData?.page,
        size: (RequestType === REQUEST_TYPE?.CHANGE || RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD) ? 10 : RequestType === REQUEST_TYPE?.FINANCE_COSTING ? 100 : 50,
        sort: "",
        };
    }
    else {
        payload = {
        massSchedulingId: taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.FINANCE_COSTING ? RequestId.slice(3) : "",
        massCreationId: taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.CREATE || taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.CREATE_WITH_UPLOAD ? RequestId.slice(3) : "",
        massChangeId: taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.CHANGE || taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.CHANGE_WITH_UPLOAD ? RequestId.slice(3) : "",
        massExtendId: taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.EXTEND || taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.EXTEND_WITH_UPLOAD ? RequestId.slice(3) : "",
        screenName: taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.CREATE ? REQUEST_TYPE?.CREATE : REQUEST_TYPE?.CHANGE,
        dtName: "MDG_MAT_MATERIAL_FIELD_CONFIG",
        version: "v2",
        page: paginationData?.page,
        size:  taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.CHANGE || taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.CHANGE_WITH_UPLOAD ? 10 : taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.FINANCE_COSTING ? 100 : 50,
        sort: "",
        };
    }

    const hSuccess = async(data) => {
        dispatch(setDataLoading(false));
        const apiResponse = data.body;
        dispatch(updateTotalCount(data?.totalElements))
        if(data?.totalPages === 1 || data?.currentPage+1 === data?.totalPages) {
        dispatch(updateCurrentCount(data?.totalElements))
        dispatch(updateNextButtonStatus(true))
        }
        else {
        dispatch(updateCurrentCount((data?.currentPage+1) * data?.pageSize))
        }
        if (taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.CHANGE || taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.CHANGE_WITH_UPLOAD || RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD || RequestType === REQUEST_TYPE?.CHANGE) {
        dispatch(
            setDynamicKeyValue({
                keyName: "requestHeaderData",
                data: apiResponse[0]?.Torequestheaderdata
            })
        )
        fetchDisplayDataRows(apiResponse);
        return;
        }

        if (taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.FINANCE_COSTING || RequestType === REQUEST_TYPE?.FINANCE_COSTING) {
            const fcRows = await createFCRows(apiResponse)
            dispatch(setFCRows([...prevFcRows, ...fcRows]));
            dispatch(updateExistingCreatePages(paginationData?.page));
            return;
        }

        const transformedPayload = transformApiResponseToReduxPayload(apiResponse, storedRows);
        dispatch(pushDisplayPayload({ data: transformedPayload?.payload }));
        const numericKeys = Object.keys(transformedPayload?.payload).filter((key) => !isNaN(Number(key)));
        const extractedData = {};
        numericKeys.forEach((key) => {
        extractedData[key] = transformedPayload?.payload[key];
        });
        dispatch(pushMaterialRows(Object.values(extractedData)?.map((item) => item.headerData)));
        dispatch(updateExistingCreatePages(paginationData?.page));
    };

    const hError = (error) => {
        customError(error);
    };

    doAjax(`/${destination_MaterialMgmt}/${END_POINTS?.CHG_DISPLAY_REQUESTOR?.DISPLAY_DTO}`, "post", hSuccess, hError, payload);
    };
    
    
    return { getNextDisplayDataForChange, getNextDisplayDataForCreate };
}

export default useDisplayCall