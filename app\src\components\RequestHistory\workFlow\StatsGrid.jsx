import StatCard from './StatCard';

const StatsGrid = ({ data }) => {
  let totalTasks = 0;
  let totalGroups = Object.keys(data).length;
  let slaSum = 0;
  let taskCount = 0;

  Object.values(data).forEach(group => {
    totalTasks += 2;
    slaSum += group.requestor_sla + group.mdmApprover_sla;
    taskCount += 2;

    group.workflowTaskDetailsByLevel.forEach(level => {
      Object.values(level).forEach(tasks => {
        totalTasks += tasks.length;
        taskCount += tasks.length;
        tasks.forEach(task => slaSum += task.taskSla);
      });
    });
  });

  const avgSLA = taskCount ? (slaSum / taskCount).toFixed(1) : 0;

  return (
    <div
      style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(180px, 1fr))',
        gap: 16,
        margin: '16px 0',
        padding: 8,
        borderRadius: 8,
      }}
    >
      <StatCard number={totalGroups} label="Workflow Groups" />
      <StatCard number={totalTasks} label="Total Tasks" />
      <StatCard number={avgSLA} label="Avg SLA (Days)" />
    </div>
  );
};

export default StatsGrid;
