import { useState, useEffect } from 'react';
import { DASHBOARD_MAIN_COLUMNS } from "@constant/enum";
import { Column } from "./Column";
import { closestCenter, DndContext } from "@dnd-kit/core";
import {
    Chart as ChartJS,
    BarElement,
    CategoryScale,
    LinearScale,
    ArcElement,
    Tooltip as ChartTooltip,
    Legend as ChartLegend,
} from "chart.js";
import { Box } from '@mui/material';
import {useDashboardCall}from "../../../hooks/useDashboardCall"

ChartJS.register(
    ArcElement,
    BarElement,
    CategoryScale,
    LinearScale,
    ChartTooltip,
    ChartLegend
);

const AllCharts = () => {
    const { cards: apiCards, loading } = useDashboardCall();
    const [cards, setCards] = useState([]);
    
    useEffect(() => {
        if (!loading && apiCards.length) {
            setCards(apiCards);
        }
    }, [apiCards, loading]);
    
    function handleDragEnd(event) {
        const { active, over } = event;
        if (!over) return;

        const activeId = active.id;
        const overId = over.id;

        const activeCard = cards.find((c) => c.id === activeId);
        const overCard = cards.find((c) => c.id === overId);

        const isOverColumn = DASHBOARD_MAIN_COLUMNS.some(col => col.id === overId);

        if (isOverColumn) {
            const newColumn = overId;
            setCards((prev) =>
                prev.map((card) =>
                    card.id === activeId ? { ...card, column: newColumn } : card
        )
    );
} else if (activeCard && overCard) {
    const sameColumn = activeCard.column === overCard.column;
    if (sameColumn) {
        const columnCards = cards.filter((c) => c.column === activeCard.column);
        const oldIndex = columnCards.findIndex((c) => c.id === activeId);
        const newIndex = columnCards.findIndex((c) => c.id === overId);
        const reordered = [...columnCards];
        reordered.splice(newIndex, 0, reordered.splice(oldIndex, 1)[0]);
        const otherCards = cards.filter((c) => c.column !== activeCard.column);
        setCards([...otherCards, ...reordered]);
    } else {
        setCards((prev) =>
            prev.map((card) =>
                card.id === activeId ? { ...card, column: overCard.column } : card
    )
);
}
}
}
if (loading) return <Box sx={{ p: 4 }}>Loading charts...</Box>;
    return (
        <Box sx={{
            py: 2,
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            width: '100%',
            height: "100vh",
            alignSelf: 'flex-start'
        }}>
            <DndContext onDragEnd={handleDragEnd} collisionDetection={closestCenter}>
                {DASHBOARD_MAIN_COLUMNS.map((column) => (
                    <Column
                        key={column.id}
                        column={column}
                        cards={cards.filter(card => card.column === column.id)}
                    />
                ))}
            </DndContext>
        </Box>
    );
};

export default AllCharts;
