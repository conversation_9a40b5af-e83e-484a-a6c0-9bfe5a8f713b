import { useEffect, useState } from "react";
import { Grid, Typo<PERSON>, <PERSON>, Box, Stack, Chip,
  IconButton, FormControlLabel, Checkbox,
  CircularProgress,
 }from "@mui/material";
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineDot,
  TimelineContent,
  TimelineConnector,
} from "@mui/lab";
import { Avatar, AvatarGroup, CardContent, Divider } from "@mui/material"
import { Message, MoreHoriz } from "@mui/icons-material"

import {
  Download as DownloadIcon,
  Close,
  Description as DocumentIcon,
  PictureAsPdf as PdfIcon,
  InsertDriveFile as FileIcon,
  Visibility as VisibilityIcon,
  Delete as DeleteIcon,
  InsertDriveFile,
} from "@mui/icons-material"
import TableViewIcon  from '@mui/icons-material/TableView'; 




import { CheckCircleOutlineOutlined } from "@mui/icons-material";
import AddIcon from "@mui/icons-material/Add";
import moment from "moment";
import { container_Padding } from "../../common/commonStyles";
import { useSelector } from "react-redux";
import { MatDownload, MatView, DeleteRecord } from "../../DocumentManagement/UtilDoc";
import ReusableTable from "../../Common/ReusableTable";
import ReusableSnackBar from "../../Common/ReusableSnackBar";
import ReusableAttachementAndComments from "../../Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import { destination_DocumentManagement, destination_MaterialMgmt } from "../../../destinationVariables";
import { doAjax } from "../../Common/fetchService";
import { setAttachmentType } from "../../../app/initialDataSlice";
import { useDispatch } from "react-redux";
import { colors } from "@constant/colors";
import AttachmentUploadDialog from "@components/Common/AttachmentUploadDialog";

import AttachFileIcon from "@mui/icons-material/AttachFile";
import { ENABLE_STATUSES, REQUEST_STATUS } from "../../../constant/enum";
import { END_POINTS } from "@constant/apiEndPoints";
import { useLocation } from "react-router-dom";

const getFileIcon = (fileName) => {
  const extension = fileName?.split(".")?.pop()?.toLowerCase() || "";

  const iconProps = { fontSize: "small", sx: { mr: 1 } };

  switch (extension) {
    case "xlsx":
    case "xls":
    case "csv":
      
      return <TableViewIcon sx={{ color: colors.icon.excel }} />; 
    case "pdf":
      return <PdfIcon {...iconProps} sx={{ color: colors.icon.pdf }} />; 
    case "doc":
    case "docx":
      return <DocumentIcon {...iconProps} sx={{ color: colors.icon.doc }} />; 
    case "ppt":
    case "pptx":
      return <DocumentIcon {...iconProps} sx={{ color: colors.icon.ppt }} />; 
    default:
      return <FileIcon {...iconProps} sx={{ color: colors.icon.file }} />;
  }
};

const AttachmentsCommentsTab = ({
  attachmentsData = [], 
  requestIdHeader = "",
  pcNumber = "",
  disabled = false,

}) => {
  const [DownloadLoader, setDownloadLoader] = useState({});
  const appSettings = useSelector((state) => state.appSettings);
  const dynamicData = useSelector((state) => state.payload.dynamicKeyValues)
  const [attachments, setAttachments] = useState([]);
  const [comments, setComments] = useState([]);
  const dispatch = useDispatch();

  const [showSnackbar, setShowSnackbar] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isreqBench = queryParams.get("reqBench");
  const reqBenchData = location?.state

  const handleSnackbarOpen = () => {
    setShowSnackbar(true);
  };

  const handleSnackbarClose = () => {
    setShowSnackbar(false);
  };

 
  const soothingColors = {
    primary: colors.blue.main,
    light: colors.text.offWhite, 
    accent: colors.primary.grey, 
    text: colors.text.charcoal,
    secondaryText: colors.text.greyBlue, 
    background: colors.background.paper, 
  };
  
  const attachmentColumns = [
    {
      field: "id",
      headerName: "Document ID",
      flex: 1.2,
      hideable: false,
      hidden: true,
    },
    {
      field: "attachmentType",
      headerName: "Attachment Type",
      flex: 1.5,
      renderCell: (params) => (
        <Chip
          label={params.value}
          size="small"
          sx={{
            backgroundColor: colors?.reportTile.lightBlue, 
            color: colors.primary.lightPlus,
            fontWeight: "medium",
          }}
        />
      ),
    },
    {
      field: "docName",
      headerName: "Document Name",
      flex: 2,
      renderCell: (params) => (
        <Stack direction="row" spacing={1} alignItems="center">
        {getFileIcon(params.value)} 
        <Typography variant="body2">{params.value}</Typography>
      </Stack>
      ),
    },
    {
      field: "uploadedOn",
      headerName: "Uploaded On",
      flex: 1,
      align: "center",
      headerAlign: "center",
    },
    {
      field: "uploadedBy",
      headerName: "Uploaded By",
      sortable: false,
      flex: 1,
    },
    {
      field: "view",
      headerName: "View",
      sortable: false,
      filterable: false,
      align: "center",
      headerAlign: "center",
      renderCell: (
        cellValues
      ) => (
        <IconButton
          size="small"
          sx={{
            color: colors.icon.matView,
            "&:hover": { backgroundColor: "rgba(2, 136, 209, 0.1)" },
          }}
        >
          <MatView 
              index={cellValues.row.id} 
              name={cellValues?.row?.docName || cellValues?.row?.fileName}
              documentViewUrl={cellValues.row.documentViewUrl}
            />
        </IconButton>
      ),
    },
    {
      field: "action",
      headerName: "Action",
      sortable: false,
      filterable: false,
      align: "center",
      headerAlign: "center",
      flex: 1,
      renderCell: (
        cellValues
      ) => (
        <Stack direction="row" spacing={0}>
          <IconButton
            size="small"
            sx={{
              color: colors?.icon.matDownload,
              "&:hover": { backgroundColor: "rgba(46, 125, 50, 0.1)" },
            }}
          >
              <MatDownload
              index={cellValues.row.id}
              name={cellValues?.row?.docName || cellValues?.row?.fileName}

            />
       
          </IconButton>
          <IconButton
            size="small"
            sx={{
              color: colors?.icon.delete, 
              "&:hover": { backgroundColor: "rgba(211, 47, 47, 0.1)" },
            }}
          >
            
               <DeleteRecord  
                  index={cellValues.row.id} 
                  name={cellValues.row.docName || cellValues?.row?.fileName}
                  setSnackbar={setShowSnackbar}
                  setopenSnackbar={setShowSnackbar}
                  setMessageDialogMessage={setMessageDialogMessage}
                  handleSnackbarOpen={handleSnackbarOpen}
                  setDownloadLoader={setDownloadLoader}
                  DownloadLoader={DownloadLoader}
                  />
        
          </IconButton>
        </Stack>
      ),
    },
  ]

  

  useEffect(() => {
    getAttachments();
  },[showSnackbar])

  useEffect(() => {
    if(isreqBench && reqBenchData?.reqStatus === (REQUEST_STATUS?.SYNDICATED_IN_SAP_DIRECT || REQUEST_STATUS?.SYNDICATED_PARTIALLY_DIRECT || REQUEST_STATUS?.SYNDICATION_FAILED_DIRECT)){
      let commentRows = [];
      var tempRow = {
        id: reqBenchData.requestId,
        comment: dynamicData?.otherPayloadData?.["Comments"] || "",
        user: reqBenchData.createdBy,
        createdAt: reqBenchData.changedOnAct,
        taskName: "Direct Syndication Task",
        role: "Requestor"
      };
      commentRows.push(tempRow);
      setComments(commentRows);
      return;
    }
    getComments();
  },[])

  const getAttachments = () => {
 
    let requestId = requestIdHeader
    let hSuccess = (data) => {
      var attachmentRows = [];
      data.documentDetailDtoList.forEach((doc) => {
        var tempRow = {
          id: doc.documentId,
          docType: doc.fileType,
          docName: doc.fileName,
          uploadedOn: moment(doc.docCreationDate).format(appSettings.dateFormat),
          uploadedBy: doc.createdBy,
          attachmentType: doc.attachmentType,
          documentViewUrl:doc.documentViewUrl
        };
        dispatch(setAttachmentType(doc?.attachmentType));
        attachmentRows.push(tempRow);
      });
      setAttachments(attachmentRows);
    };
    doAjax(
      `/${destination_DocumentManagement}/${END_POINTS.TASK_ACTION_DETAIL.GET_DOCS}/${requestId}`,
      "get",
      hSuccess
    );
  };

  const getComments = () => {
    let requestId = requestIdHeader
    let hSuccess = (data) => {
      var commentRows = [];
      data.body.forEach((cmt) => {
        var tempRow = {
          id: cmt.requestId,
          comment: cmt.comment,
          user: cmt.createdByUser,
          createdAt: cmt.updatedAt,
          taskName: cmt.taskName,
          role:cmt.createdByRole
        };
        commentRows.push(tempRow);
      });
      setComments(commentRows);
    };

    let hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/${END_POINTS.TASK_ACTION_DETAIL.TASKDETAILS_FOR_REQUESTID}?requestId=${requestId}`,
      "get",
      hSuccess,
      hError
    );
  };

  return (
    <div>
      <Grid container spacing={2} sx={{padding:'10px',margin:0}}>

        {/* Attachments Table Section */}
        <Grid
          item
          md={12}
          sx={{
            backgroundColor: colors.primary.white,
            maxHeight: "max-content",
            height: "max-content",
            borderRadius: "8px",
            border: "1px solid #E0E0E0",
            mt: 2,
            boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
            ...container_Padding,
          }}
        >
          <Grid container sx={{ display: "flex", justifyContent: "space-between",flexDirection:'row',alignItems:'center' }}>
            <Box sx={{ display: "flex", justifyContent: "space-between",flexDirection:'row',alignItems:'space-between',width:'100%' }}>
            <Typography variant="h6">
              <strong>Attachments</strong>
            </Typography>
            {!disabled && attachmentsData?.map((attachment, index) => (
                  <ReusableAttachementAndComments
                  title="Material"
                  useMetaData={false}
                  artifactId={`${attachment.MDG_ATTACHMENTS_NAME}_${pcNumber}`}
                  artifactName="MaterialMaster"
                  attachmentType={attachment.MDG_ATTACHMENTS_NAME}
                  requestId={requestIdHeader}
                  getAttachments={getAttachments}
                />
            ))}
        
                </Box>
          </Grid>
          {attachments.length > 0 ? (
            <ReusableTable
              width="100%"
              rows={attachments}
              columns={attachmentColumns}
              hideFooter={false}
              getRowIdValue="id"
              autoHeight={true}
              disableSelectionOnClick={true}
              stopPropagation_Column="action"
            />
          ) : (
            <Stack alignItems="center" spacing={2} sx={{ py: 4 }}>
            <AttachFileIcon sx={{ fontSize: 40, color: soothingColors.accent,transform:'rotate(90deg)' }} />
            <Typography variant="body2" color={soothingColors.secondaryText}>
            No Attachments Found
            </Typography>
          </Stack>
          )}

          <br />

          {/* Comments Section */}
          <Box sx={{ maxWidth: "100%", bgcolor: soothingColors.background, borderRadius: "12px", boxShadow: '0 4px 12px rgba(0,0,0,0.05)' }}>
  {/* Header */}
  <Stack direction="row" alignItems="center" spacing={1} mb={3}>
    <Typography variant="h6" fontWeight="bold" color={soothingColors.text}>
      Comments
    </Typography>
  </Stack>

  {/* Comments Timeline */}
  {comments.length > 0 ? (
    <Timeline position="right" 
    sx={{ 
      p: 0, 
      m: 0,
      '& .MuiTimelineItem-root:before': {
        flex: 0,
        padding: 0,
      },
    }}>
      {comments.map((comment, index) => (
        <TimelineItem key={comment.id} sx={{}}>
          <TimelineSeparator>
            <TimelineDot
              sx={{
                bgcolor: soothingColors.light,
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              }}
            >
              <Message size={18} sx={{color: 'blue' }} />

            </TimelineDot>
            
              <TimelineConnector sx={{ width: '2.2px' }} />
          
          </TimelineSeparator>
          <TimelineContent sx={{ py: '12px', px: 2 }}>
            <Card
              variant="outlined"
              sx={{
                borderRadius: "12px",
                borderColor: soothingColors.accent,
                background: 'linear-gradient(135deg, #FFFFFF 0%, #F9FAFB 100%)',
                transition: 'all 0.3s ease',
              }}
            >
              <CardContent sx={{ p: 2 }}>
                <Stack direction="row" justifyContent="space-between" alignItems="center">
                  <Stack direction="row" spacing={1.5} alignItems="center">
                    <Avatar
                      sx={{
                        bgcolor: '#e3f2fd',
                        color: soothingColors.primary,
                        boxShadow: 'none',
                        width: 32,
                        height: 32,
                        fontSize: '14px',
                        transition: 'all 0.3s ease',
                        '&:hover': { transform: 'rotate(5deg)' },
                      }}
                    >
                      {comment.user.charAt(0).toUpperCase()}
                    </Avatar>
                    <Typography fontWeight="bold" color={soothingColors.text}>
                      {comment.user}
                    </Typography>
                    <Chip
                      label={comment.taskName}
                      size="small"
                      sx={{
                       backgroundColor: colors.reportTile.lightBlue,
                        color: colors.primary.lightPlus,
                        fontSize: "12px",
                        borderRadius: '16px',
                      }}
                    />
                  </Stack>
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <Typography variant="body2" color={soothingColors.secondaryText} sx={{ fontSize: '12px' }}>
                      {moment(comment.createdAt).format("MM/DD/YYYY, h:mm A")}
                    </Typography>
                    <IconButton size="small" sx={{ color: soothingColors.secondaryText }}>
                      <MoreHoriz fontSize="small" />
                    </IconButton>
                  </Stack>
                </Stack>
                <Divider sx={{ my: 2, borderColor: soothingColors.accent }} />
                <Typography variant="body2" color={soothingColors.text} sx={{my:1 ,fontSize: '14px', lineHeight: '1.6' }}>
                  {comment.comment || "-"}
                </Typography>
              </CardContent>
            </Card>
          </TimelineContent>
        </TimelineItem>
      ))}
    </Timeline>
  ) : (
    <Stack alignItems="center" spacing={2} sx={{ py: 4 }}>
      <Message sx={{ fontSize: 40, color: soothingColors.accent }} />
      <Typography variant="body2" color={soothingColors.secondaryText}>
        No Remarks found
      </Typography>
    </Stack>
  )}
</Box>
  
          <br />
        </Grid>
      </Grid>
      {showSnackbar && (
        <ReusableSnackBar
          openSnackBar={showSnackbar}
          alertMsg={messageDialogMessage}
          handleSnackBarClose={handleSnackbarClose}
        />
      )}
    </div>
  );
};

export default AttachmentsCommentsTab;