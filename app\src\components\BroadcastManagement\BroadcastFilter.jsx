import { Accordion, AccordionDetails, AccordionSummary, Box, Button, Checkbox, FormControl, Grid, InputLabel, ListItemText, MenuItem, Select, TextField, Typography } from '@mui/material'
import React from 'react'
import { button_Outlined, button_Primary, container_filter, font_Small, iconButton_SpacingSmall, icon_MarginLeft } from '../common/commonStyles'
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useDispatch, useSelector } from 'react-redux';
// import { commonFilterClear, commonFilterUpdate } from '../../app/commonFilterSlice';
import { destination_Admin } from '../../destinationVariables';
import axios from 'axios';
// import PresetV3 from '../common/PresetV3';
// import FilterField from '../common/ReusableFilterBox/FilterField';

const BroadcastFilter = ({handleSearch,PresetMethod,PresetObj}) => {
  const presentDate = new Date();
const backDate = new Date();
backDate.setDate(backDate.getDate() - 8);
  const category=[
    "Announcements","Videos","Events"
  ];
  const status=[
"Active","Inactive","Draft","Archived"
  ];
  const supplier=[];
  // const dispatch = useDispatch();
  // const FilterSearchForm = useSelector(
  //   (state) => state.commonFilter["BroadcastHome"]
  // );
  // const appSettings=useSelector((state)=> state.appSettings["Format"])

  const moduleFilterData = [
    {
      type: "text",
      filterName: "id",
      filterTitle: "Broadcast ID",
    },
    {
      type: "multiSelect",
      filterName: "category",
      filterData: category,
      filterTitle: "Broadcast Category",
    },
    {
      type: "multiSelect",
      filterName: "supplier",
      filterData: [],
      filterTitle: "Supplier Category",
    },
    {
      type: "multiSelect",
      filterName: "status",
      filterData: status,
      filterTitle: "Broadcast Status",
    },
    {
      type: "dateRange",
      filterName: "startDate",
      filterTitle: "Start Date",
    },
    {
      type: "dateRange",
      filterName: "endDate",
      filterTitle: "End Date",
    },
  ];
 

  // const handleClear=()=>{
  //   dispatch(commonFilterClear({module:"BroadcastHome",days:appSettings.range}))
  //   // dispatch(
  //   //   commonFilterUpdate({
  //   //     module: "BroadcastHome",
  //   //     filterData: {
  //   //       ...FilterSearchForm,id:"",category:[],status:[],supplier:[],startDate:[presentDate,backDate],
  //   //       endDate:[presentDate,backDate],title:""
  //   //     },
  //   //   })
  //   // );
  // }
  return (
    <Grid container sx={container_filter}>
      <Grid item md={12}>
        <Accordion
          defaultExpanded={false}
          sx={{
            marginTop: "0px !important",
            border: "1px solid",
            borderColor: "#E0E0E0",
            "&:not(:last-child)": {
              borderBottom: 0,
            },
            "&:before": {
              display: "none",
            },
          }}
        >
          <AccordionSummary
            expandIcon={<ExpandMoreIcon sx={{ ...iconButton_SpacingSmall }} />}
            aria-controls="panel1a-content"
            id="panel1a-header"
            sx={{
              minHeight: "2rem !important",
              margin: "0px !important",
            }}
          >
            <Typography
              sx={{
                fontWeight: "700",
              }}
            >
              Filter Document
            </Typography>
          </AccordionSummary>
          <AccordionDetails sx={{ padding: "0rem 1rem 0.5rem" }}>
            <Grid container rowSpacing={1} spacing={2}>
              {moduleFilterData?.map((filter) => (
                <Grid item md={3} key={filter.filterTitle}>
                  {/* <FilterField
                    type={filter.type}
                    filterName={filter.filterName}
                    filterData={filter.filterData}
                    moduleName={"BroadcastHome"}
                    onChangeFilter={filter.onChangeFilter}
                    filterTitle={filter.filterTitle}
                  /> */}
                </Grid>
              ))}
            </Grid>
            <Grid
              container
              style={{
                display: "flex",
                justifyContent: "flex-end",
              }}
            >
              <Grid
                item
                style={{
                  display: "flex",
                  justifyContent: "space-around",
                }}
              >
                <Button
                  variant="outlined"
                  sx={button_Outlined}
                  // onClick={handleClear}
                >
                  Clear
                </Button>
               
                {/* <PresetV3
                moduleName={"BroadcastHome"}
                handleSearch={handleSearch}
                PresetObj={PresetObj}
                PresetMethod={PresetMethod}
                handleSearchBar={handleSearch}
                /> */}
                <Button
                  variant="contained"
                  sx={{ ...button_Primary, ...icon_MarginLeft }}
                  onClick={handleSearch}
                >
                  Search
                </Button>
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>
      </Grid>
    </Grid>
  )
}

export default BroadcastFilter