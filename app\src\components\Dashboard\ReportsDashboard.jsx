import AutoCompleteSimpleDropDown from "@components/Common/ui/dropdown/AutoCompleteSimpleDropDown";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  FormControl,
  Grid,
  Typography,
  <PERSON>ton,
  Stack,
  Card,
  CardContent,
  Box,
} from "@mui/material";
import React, { useState, useEffect } from "react";
import TextSnippetIcon from "@mui/icons-material/TextSnippet";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { destination_Dashboard } from "../../destinationVariables";
import {
  iconButton_SpacingSmall,
  button_Outlined,
  button_Primary,
  font_Small,
  icon_MarginLeft,
} from "@components/Common/commonStyles";
import DateRange from "../common/DateRangePicker";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { useDispatch, useSelector } from "react-redux";
import {
  commonFilterClear,
  commonFilterUpdate,
} from "../../app/commonFilterSlice";
import moment from "moment/moment";
import { doAjax } from "@components/Common/fetchService";
import LargeDropdown from "@components/Common/ui/dropdown/LargeDropdown";
import {
  reportStyle,
  dialogTitleStyle,
  downloadButtonStyle,
} from "@constant/style";
import { barColors1Store } from "./LegendStore";
import { DASHBOARD_REPORT_LABELS, LOADING_MESSAGE } from "@constant/enum";
import { END_POINTS } from "@constant/apiEndPoints";
import { APP_END_POINTS } from "@constant/appEndPoints";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { set } from "date-fns";

const ReportsDashboard = () => {
  const reportSearchForm = useSelector(
    (state) => state.commonFilter["Reports"]
  );
  const rbSearchForm = useSelector(
    (state) => state.commonFilter["RequestBench"]
  );
  const dropDownData = useSelector((state) => state.AllDropDown.dropDown);
  const [selectedDivision, setSelectedDivision] = useState([]);
  const [requestStatus, setRequestStatus] = useState([]);
  const [dateTime, setDateTime] = useState([...reportSearchForm.reportDate]);
  const [requestType, setRequestType] = useState([]);
  const [requestTypeOptions, setRequestTypeOptions] = useState([]);
  const [requestStatusOptions, setRequestStatusOptions] = useState([]);
  const [blurLoading, setBlurLoading] = useState("");
  const [loaderMessage, setLoaderMessage] = useState("");
  const dispatch = useDispatch();
  useEffect(() => {
    if (reportSearchForm?.reportDate) {
      const presentDate = new Date(reportSearchForm?.reportDate[0]);
      const backDate = new Date(reportSearchForm?.reportDate[1]);
      setDateTime([presentDate, backDate]);
    }
  }, [reportSearchForm?.reportDate]);
  useEffect(() => {
    getRequestType();
    getRequestStatus();
  }, []);

  const regionOptions = ["US", "EUR"];
  
  const downloadOptionsAPP = [
  {
    label: DASHBOARD_REPORT_LABELS.REQUEST_VS_OBJECT_TYPE,
    api: END_POINTS.DASHBOARD_APIS.KPI1,
    file: APP_END_POINTS.DASHBOARD_FILES.REQUEST_VS_OBJECT_TYPE,
    payload: {
      fromDate:
        moment(
          reportSearchForm?.reportDate?.[0] ?? rbSearchForm?.createdOn?.[0]
        ).format("YYYY-MM-DD") + " 00:00:00",
      toDate:
        moment(
          reportSearchForm?.reportDate?.[1] ?? rbSearchForm?.createdOn?.[1]
        ).format("YYYY-MM-DD") + " 00:00:00",
      objectType : "Material,Profit Center,Cost Center,General Ledger",
      "priority": "",
      "reqStatus": "",
      "userId": "",
      "reqId": ""
    }
  },
  {
    label: DASHBOARD_REPORT_LABELS.AVG_REQUEST_ID_LIFE_CYCLE,
    api: END_POINTS.DASHBOARD_APIS.KPI2,
    file: APP_END_POINTS.DASHBOARD_FILES.AVG_REQUEST_ID_LIFE_CYCLE,
    payload: {
      fromDate:
        moment(
          reportSearchForm?.reportDate?.[0] ?? rbSearchForm?.createdOn?.[0]
        ).format("YYYY-MM-DD") + " 00:00:00",
      toDate:
        moment(
          reportSearchForm?.reportDate?.[1] ?? rbSearchForm?.createdOn?.[1]
        ).format("YYYY-MM-DD") + " 00:00:00",
      "priority": "",
      "objectType": "Material,Profit Center,Cost Center,General Ledger",
      "userId": "",
      "reqId": ""
    }
  },
  {
    label: DASHBOARD_REPORT_LABELS.REJECTED_REQUESTS_PERCENTAGE,
    api: END_POINTS.DASHBOARD_APIS.KPI4,
    file: APP_END_POINTS.DASHBOARD_FILES.REJECTED_REQUESTS_PERCENTAGE,
    payload: {
      fromDate:
        moment(
          reportSearchForm?.reportDate?.[0] ?? rbSearchForm?.createdOn?.[0]
        ).format("YYYY-MM-DD") + " 00:00:00",
      toDate:
        moment(
          reportSearchForm?.reportDate?.[1] ?? rbSearchForm?.createdOn?.[1]
        ).format("YYYY-MM-DD") + " 00:00:00",
      "priority": "",
      "objectType": "Material,Profit Center,Cost Center,General Ledger",
      "userId": ""
    }
  },
  {
    label: DASHBOARD_REPORT_LABELS.NOT_SUCCESSFULLY_COMPLETED_REQUESTS,
    api: END_POINTS.DASHBOARD_APIS.KPI5,
    file: APP_END_POINTS.DASHBOARD_FILES.NOT_SUCCESSFULLY_COMPLETED_REQUESTS,
    payload: {
      fromDate:
        moment(
          reportSearchForm?.reportDate?.[0] ?? rbSearchForm?.createdOn?.[0]
        ).format("YYYY-MM-DD") + " 00:00:00",
      toDate:
        moment(
          reportSearchForm?.reportDate?.[1] ?? rbSearchForm?.createdOn?.[1]
        ).format("YYYY-MM-DD") + " 00:00:00",
      "priority": "",
      "objectType": "Material,Profit Center,Cost Center,General Ledger",
      "userId": ""
    }
  },
  {
    label: DASHBOARD_REPORT_LABELS.SLA_BREACHED_REQUESTS,
    api: END_POINTS.DASHBOARD_APIS.KPI6,
    file: APP_END_POINTS.DASHBOARD_FILES.SLA_BREACHED_REQUESTS,
    payload: {
      fromDate:
        moment(
          reportSearchForm?.reportDate?.[0] ?? rbSearchForm?.createdOn?.[0]
        ).format("YYYY-MM-DD") + " 00:00:00",
      toDate:
        moment(
          reportSearchForm?.reportDate?.[1] ?? rbSearchForm?.createdOn?.[1]
        ).format("YYYY-MM-DD") + " 00:00:00",
      "objectType": "Material,Profit Center,Cost Center,General Ledger",
    }
  },
  {
    label: DASHBOARD_REPORT_LABELS.NO_OF_REQUESTS_REJECTED,
    api: END_POINTS.DASHBOARD_APIS.KPI9,
    file: APP_END_POINTS.DASHBOARD_FILES.NO_OF_REQUESTS_REJECTED,
    payload: {
      fromDate:
        moment(
          reportSearchForm?.reportDate?.[0] ?? rbSearchForm?.createdOn?.[0]
        ).format("YYYY-MM-DD") + " 00:00:00",
      toDate:
        moment(
          reportSearchForm?.reportDate?.[1] ?? rbSearchForm?.createdOn?.[1]
        ).format("YYYY-MM-DD") + " 00:00:00",
      "priority": "",
      "objectType": "Material,Profit Center,Cost Center,General Ledger",
      "userId": ""
    }
  },
  {
    label: DASHBOARD_REPORT_LABELS.NO_OF_REQUESTS_PENDING,
    api: END_POINTS.DASHBOARD_APIS.KPI11,
    file: APP_END_POINTS.DASHBOARD_FILES.NO_OF_REQUESTS_PENDING,
    payload: {
      fromDate:
        moment(
          reportSearchForm?.reportDate?.[0] ?? rbSearchForm?.createdOn?.[0]
        ).format("YYYY-MM-DD") + " 00:00:00",
      toDate:
        moment(
          reportSearchForm?.reportDate?.[1] ?? rbSearchForm?.createdOn?.[1]
        ).format("YYYY-MM-DD") + " 00:00:00",
      "priority": "",
      "objectType": "Material,Profit Center,Cost Center,General Ledger",
      "userId": ""
    }
  },
  {
    label: DASHBOARD_REPORT_LABELS.AVG_MDM_TASK_COMPLETION_TIME,
    api: END_POINTS.DASHBOARD_APIS.KPI12,
    file: APP_END_POINTS.DASHBOARD_FILES.AVG_MDM_TASK_COMPLETION_TIME,
    payload: {
      fromDate:
        moment(
          reportSearchForm?.reportDate?.[0] ?? rbSearchForm?.createdOn?.[0]
        ).format("YYYY-MM-DD") + " 00:00:00",
      toDate:
        moment(
          reportSearchForm?.reportDate?.[1] ?? rbSearchForm?.createdOn?.[1]
        ).format("YYYY-MM-DD") + " 00:00:00",
        "priority": "",
        "objectType": "Material,Profit Center,Cost Center,General Ledger",
        "userId": ""
    }
  },
  {
    label: DASHBOARD_REPORT_LABELS.AVG_APPROVER_TASK_COMPLETION_TIME,
    api: END_POINTS.DASHBOARD_APIS.KPI13,
    file: APP_END_POINTS.DASHBOARD_FILES.AVG_APPROVER_TASK_COMPLETION_TIME,
    payload: {
      fromDate:
        moment(
          reportSearchForm?.reportDate?.[0] ?? rbSearchForm?.createdOn?.[0]
        ).format("YYYY-MM-DD") + " 00:00:00",
      toDate:
        moment(
          reportSearchForm?.reportDate?.[1] ?? rbSearchForm?.createdOn?.[1]
        ).format("YYYY-MM-DD") + " 00:00:00",
      "priority": "",
      "objectType": "Material,Profit Center,Cost Center,General Ledger",
      "userId": ""
    }
  },
  {
    label: DASHBOARD_REPORT_LABELS.NO_OF_REQUESTS_REJECTED_VS_REQUESTORS,
    api: END_POINTS.DASHBOARD_APIS.KPI14,
    file: APP_END_POINTS.DASHBOARD_FILES.NO_OF_REQUESTS_REJECTED_VS_REQUESTORS,
    payload: {
      fromDate:
        moment(
          reportSearchForm?.reportDate?.[0] ?? rbSearchForm?.createdOn?.[0]
        ).format("YYYY-MM-DD") + " 00:00:00",
      toDate:
        moment(
          reportSearchForm?.reportDate?.[1] ?? rbSearchForm?.createdOn?.[1]
        ).format("YYYY-MM-DD") + " 00:00:00",
      "priority": "",
      "objectType": "Material,Profit Center,Cost Center,General Ledger",
      "userId": ""
    }
  },
  {
    label: DASHBOARD_REPORT_LABELS.APPROVERS_WHO_HAVE_MISSED_SLA,
    api: END_POINTS.DASHBOARD_APIS.KPI15,
    file: APP_END_POINTS.DASHBOARD_FILES.APPROVERS_WHO_HAVE_MISSED_SLA,
    payload: {
      fromDate:
        moment(
          reportSearchForm?.reportDate?.[0] ?? rbSearchForm?.createdOn?.[0]
        ).format("YYYY-MM-DD") + " 00:00:00",
      toDate:
        moment(
          reportSearchForm?.reportDate?.[1] ?? rbSearchForm?.createdOn?.[1]
        ).format("YYYY-MM-DD") + " 00:00:00",
      "objectType": "Material,Profit Center,Cost Center,General Ledger",
    }
  },
  {
    label: DASHBOARD_REPORT_LABELS.APPROVERS_AVG_APPROVAL_TIME,
    api: END_POINTS.DASHBOARD_APIS.KPI16,
    file: APP_END_POINTS.DASHBOARD_FILES.APPROVERS_AVG_APPROVAL_TIME,
    payload: {
      fromDate:
        moment(
          reportSearchForm?.reportDate?.[0] ?? rbSearchForm?.createdOn?.[0]
        ).format("YYYY-MM-DD") + " 00:00:00",
      toDate:
        moment(
          reportSearchForm?.reportDate?.[1] ?? rbSearchForm?.createdOn?.[1]
        ).format("YYYY-MM-DD") + " 00:00:00",
      "objectType": "Material,Profit Center,Cost Center,General Ledger",
    }
  },
  {
    label: DASHBOARD_REPORT_LABELS.REQUESTS_COMPLETED_BY_MDM,
    api: END_POINTS.DASHBOARD_APIS.KPI17,
    file: APP_END_POINTS.DASHBOARD_FILES.REQUESTS_COMPLETED_BY_MDM,
    payload: {
      fromDate:
        moment(
          reportSearchForm?.reportDate?.[0] ?? rbSearchForm?.createdOn?.[0]
        ).format("YYYY-MM-DD") + " 00:00:00",
      toDate:
        moment(
          reportSearchForm?.reportDate?.[1] ?? rbSearchForm?.createdOn?.[1]
        ).format("YYYY-MM-DD") + " 00:00:00",
      "objectType": "Material,Profit Center,Cost Center,General Ledger",
    }
  },
  {
    label: DASHBOARD_REPORT_LABELS.OPEN_REQUESTS_VS_TYPE,
    api: END_POINTS.DASHBOARD_APIS.KPI18,
    file: APP_END_POINTS.DASHBOARD_FILES.OPEN_REQUESTS_VS_TYPE,
    payload: {
      fromDate:
        moment(
          reportSearchForm?.reportDate?.[0] ?? rbSearchForm?.createdOn?.[0]
        ).format("YYYY-MM-DD") + " 00:00:00",
      toDate:
        moment(
          reportSearchForm?.reportDate?.[1] ?? rbSearchForm?.createdOn?.[1]
        ).format("YYYY-MM-DD") + " 00:00:00",
      "priority" : "",
      "objectType": "Material,Profit Center,Cost Center,General Ledger",
      "userId": ""
    }
  },
  {
    label: DASHBOARD_REPORT_LABELS.REQUESTOR_APPROVER_COUNTS,
    api: END_POINTS.DASHBOARD_APIS.KPI19,
    file: APP_END_POINTS.DASHBOARD_FILES.REQUESTOR_APPROVER_COUNTS,
    payload: {
      fromDate:
        moment(
          reportSearchForm?.reportDate?.[0] ?? rbSearchForm?.createdOn?.[0]
        ).format("YYYY-MM-DD") + " 00:00:00",
      toDate:
        moment(
          reportSearchForm?.reportDate?.[1] ?? rbSearchForm?.createdOn?.[1]
        ).format("YYYY-MM-DD") + " 00:00:00",
      "priority": "",
      "objectType": "Material,Profit Center,Cost Center,General Ledger",
      "userId": ""
    }
  }
];

  const downloadOptionsSAP = [
    // {
    //   label: DASHBOARD_REPORT_LABELS.MISSING_HTS,
    //   api: END_POINTS.DASHBOARD_APIS.MISSING_HTS,
    //   file: APP_END_POINTS.DASHBOARD_FILES.MISSING_HTS,
    // },
  ];
  const [regionName, setRegionName] = useState([]);

  const handleSelectAllRegion = () => {
    if (regionName.length === regionOptions?.length) {
      setRegionName([]);
    } else {
      setRegionName(regionOptions);
    }
  };

  const handleSelectAllReqType = () => {
    if (requestType.length === requestTypeOptions?.length) {
      setRequestType([]);
    } else {
      setRequestType(requestTypeOptions);
    }
  };

  const handleSelectAllReqStatus= () => {
    if (requestStatus.length === requestStatusOptions?.length) {
      setRequestStatus([]);
    } else {
      setRequestStatus(requestStatusOptions);
    }
  };

  const getRequestStatus = () => {
    const hSuccess1 = (data) => {
      let RequestTypesArr = data?.body;
      setRequestStatusOptions(RequestTypesArr);
    };
    const hError1 = () => {};

    doAjax(
      `/${destination_Dashboard}/GraphConfig/getReqStatuses`,
      "get",
      hSuccess1,
      hError1
    );
  };

  const getRequestType = () => {
    const hSuccess = (data) => {
      let RequestTypesArr = data?.body;
      setRequestTypeOptions(RequestTypesArr);
    };
    const hError = () => {};

    doAjax(
      `/${destination_Dashboard}/GraphConfig/getReqTypes`,
      "get",
      hSuccess,
      hError
    );
  };

  const downloadAPPkpiData = (endpoint, filename,downloadPayload) => {
    setLoaderMessage(LOADING_MESSAGE.REPORT_LOADING);
    setBlurLoading(true);

    

    const onSuccess = (response) => {
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");
      link.href = href;
      link.setAttribute("download", filename);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      setBlurLoading(false);
      setLoaderMessage("");
    };

    const onError = () => {
      setBlurLoading(false);
      setLoaderMessage("");
    };

    doAjax(
      `/${destination_Dashboard}/excel${endpoint}`,
      "postandgetblob",
      onSuccess,
      onError,
      downloadPayload
    );
  };

  const downloadSAPkpiData = (endpoint, filename) => {
    setLoaderMessage(LOADING_MESSAGE.REPORT_LOADING);
    setBlurLoading(true);

    let downloadPayload = {
      top: "100",
      skip: "0",
    };

    const onSuccess = (response) => {
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");
      link.href = href;
      link.setAttribute("download", filename);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      setBlurLoading(false);
      setLoaderMessage("");
    };

    const onError = () => {
      setBlurLoading(false);
      setLoaderMessage("");
    };

    doAjax(
      `/${destination_Dashboard}/excel/${endpoint}`,
      "postandgetblob",
      onSuccess,
      onError,
      downloadPayload
    );
  };

  const handleDate = (e) => {
    const tempdate = e;
    dispatch(
      commonFilterUpdate({
        module: "Reports",
        filterData: {
          ...reportSearchForm,
          reportDate: tempdate,
        },
      })
    );
  };

  const handleClear = () => {
    setRegionName([]);
    setRequestType([]);
    setSelectedDivision([])
    setRequestStatus([]);
    const presentDate = new Date();
    const backDate = new Date();
    backDate.setDate(backDate.getDate() - 3650);
    setDateTime([backDate, presentDate]);
    dispatch(commonFilterClear({ module: "Reports" }));
  };

  return (
    <>
      <Grid item md={12}>
        <Accordion
          defaultExpanded={false}
          sx={{
            marginTop: "5px !important",
            marginLeft: "25px !important",
            marginRight: "20px !important",
            border: "1px solid",
            borderColor: "#E0E0E0",
            "&:not(:last-child)": {
              borderBottom: 0,
            },
            "&:before": {
              display: "none",
            },
          }}
        >
          <AccordionSummary
            expandIcon={<ExpandMoreIcon sx={{ ...iconButton_SpacingSmall }} />}
            aria-controls="panel1a-content"
            id="panel1a-header"
            sx={{
              minHeight: "2rem !important",
              margin: "0px !important",
              color: "#1D1D1D",
            }}
          >
            <Typography
              sx={{
                fontWeight: "700",
              }}
            >
              Filter Reports
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container rowSpacing={1} spacing={2}>
              <Grid item md={2}>
                <Typography sx={font_Small}>Date Range</Typography>
                <FormControl fullWidth sx={{ padding: 0, height: "37px" }}>
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DateRange
                      handleDate={handleDate}
                      cleanDate={false}
                      date={dateTime}
                    />
                  </LocalizationProvider>
                </FormControl>
              </Grid>
              <Grid item md={2}>
                <Typography sx={font_Small}>Region</Typography>
                <AutoCompleteSimpleDropDown
                  options={[
                    ...regionOptions
                      ?.filter((name) => name !== "Select All")
                      ?.sort((a, b) => (typeof a === 'string' && typeof b === 'string') ? a.localeCompare(b) : 0),
                  ]}
                  value={regionName}
                  onChange={(value) => {
                    if (
                      value.length > 0 &&
                      value[value.length - 1]?.label === "Select All"
                    ) {
                      handleSelectAllRegion();
                    } else {
                      setRegionName(value);
                    }
                  }}
                  placeholder="Select Region"
                />
              </Grid>
              <Grid item md={2}>
                <Typography sx={font_Small}>Request Type</Typography>
                <AutoCompleteSimpleDropDown
                  options={[
                    ...requestTypeOptions
                      ?.filter((name) => name !== "Select All")
                      ?.sort((a, b) => (typeof a === 'string' && typeof b === 'string') ? a.localeCompare(b) : 0),
                  ]}
                  value={requestType}
                  onChange={(value) => {
                    if (
                      value.length > 0 &&
                      value[value.length - 1]?.label === "Select All"
                    ) {
                      handleSelectAllReqType();
                    } else {
                      setRequestType(value);
                    }
                  }}
                  placeholder="Select Request Type"
                />
              </Grid>
              <Grid item md={2}>
                <Typography sx={font_Small}>Division</Typography>
                <LargeDropdown
                  matGroup={dropDownData?.Division ?? []}
                  selectedMaterialGroup={selectedDivision}
                  setSelectedMaterialGroup={(value) => {
                    if (!value || value.length === 0) {
                      setSelectedDivision([]);
                      return;
                    }
                    setSelectedDivision(value);
                  }}
                  placeholder="Select Division"
                />
              </Grid>
              <Grid item md={2}>
                <Typography sx={font_Small}>Request Status</Typography>
                <AutoCompleteSimpleDropDown
                  options={[
                    ...requestStatusOptions
                      ?.filter((name) => name !== "Select All")
                      ?.sort((a, b) => (typeof a === 'string' && typeof b === 'string') ? a.localeCompare(b) : 0),
                  ]}
                  value={requestStatus}
                  onChange={(value) => {
                    if (
                      value.length > 0 &&
                      value[value.length - 1]?.label === "Select All"
                    ) {
                      handleSelectAllReqStatus();
                    } else {
                      setRequestStatus(value);
                    }
                  }}
                  placeholder="Select Request Status"
                />
              </Grid>
            </Grid>
            <Grid
              container
              style={{ display: "flex", justifyContent: "flex-end" }}
            >
              <Grid
                item
                style={{ display: "flex", justifyContent: "space-around" }}
              >
                <Button
                  variant="outlined"
                  sx={{ ...button_Outlined }}
                  onClick={handleClear}
                >
                  Clear
                </Button>
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>
      </Grid>

      <Grid item md={12}>
        <Stack justifyContent="space-between" direction="row">
          <Grid container spacing={2}>
            <Grid item md={12}>
              <Card
                sx={{
                  borderRadius: "10px",
                  boxShadow: 4,
                  height: { 
                    xs: "calc(70vh - 100px)", 
                    md: "calc(75vh - 100px)", 
                    lg: "calc(80vh - 130px)" 
                  },
                  marginLeft: "25px",
                  marginTop: "20px",
                  marginRight: "20px",
                }}
              >
                <CardContent>
                  <Typography
                    variant="h6"
                    sx={{ fontWeight: "bold", marginBottom: "20px" }}
                  >
                    Application Report List
                  </Typography>
                  <Stack 
                    spacing={2} 
                    sx={{ 
                      height: { 
                        xs: "calc(70vh - 160px)", 
                        md: "calc(75vh - 160px)", 
                        lg: "calc(80vh - 190px)" 
                      },
                      overflowY: "auto",
                      overflowX: "hidden",
                      "&::-webkit-scrollbar": {
                        width: "4px",
                      },
                      "&::-webkit-scrollbar-thumb": {
                        backgroundColor: "rgba(0,0,0,0.2)",
                        borderRadius: "4px",
                      },
                    }}
                  >
                    <Grid container spacing={1}> 
                      {downloadOptionsAPP.map(({ label, api, file, payload }, index) => (
                        <Grid item xs={6} key={index} sx={{ paddingRight: "8px" }}> 
                          <Box sx={{
                            ...reportStyle,
                            width: "100%",
                            boxSizing: "border-box" 
                          }}>
                            <Typography variant="body1" sx={{
                              ...dialogTitleStyle,
                              whiteSpace: "nowrap",
                              overflow: "hidden",
                              textOverflow: "ellipsis"
                            }}>
                              <TextSnippetIcon
                                sx={{
                                  color: barColors1Store[index % barColors1Store.length],
                                  marginRight: "4px"
                                }}
                              />
                              {label}
                            </Typography>
                            <Button
                              variant="outlined"
                              sx={downloadButtonStyle}
                              onClick={() => downloadAPPkpiData(api, file, payload)}
                            >
                              Download
                            </Button>
                          </Box>
                        </Grid>
                      ))}
                      {/* <Grid item md={6}>
                        <Card
                          sx={{
                            borderRadius: "10px",
                            boxShadow: 4,
                            height: "400px",
                            marginRight: "20px",
                            marginTop: "20px",
                          }}
                        >
                          <CardContent>
                            <Typography
                              variant="h6"
                              sx={{ fontWeight: "bold", marginBottom: "20px" }}
                            >
                              SAP Report List
                            </Typography>
                            <Stack spacing={2}>
                              {downloadOptionsSAP.map(({ label, api, file }, index) => (
                                <Box key={index} sx={reportStyle}>
                                  <Typography variant="body1" sx={dialogTitleStyle}>
                                    <TextSnippetIcon
                                      sx={{
                                        color:
                                          barColors1Store[index % barColors1Store.length],
                                      }}
                                    />
                                    {label}
                                  </Typography>
                                  <Button
                                    variant="outlined"
                                    sx={downloadButtonStyle}
                                    onClick={() => downloadSAPkpiData(api, file)}
                                  >
                                    Download
                                  </Button>
                                </Box>
                              ))}
                            </Stack>
                          </CardContent>
                        </Card>
                      </Grid> */}
                    </Grid>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>

            
          </Grid>
        </Stack>
      </Grid>
      <ReusableBackDrop
        blurLoading={blurLoading}
        loaderMessage={loaderMessage}
      />
    </>
  );
};

export default ReportsDashboard;
