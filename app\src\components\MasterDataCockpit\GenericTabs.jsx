import { Accordion, AccordionDetails, AccordionSummary, Grid, Box, Typography, CircularProgress } from "@mui/material";
import { useEffect, useMemo, useState } from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useSelector } from "react-redux";
import FilterField from "../Common/ReusableFilterBox/FilterField";
import { container_Padding } from "../Common/commonStyles";
import { doAjax } from "../Common/fetchService";
import { destination_MaterialMgmt } from "../../destinationVariables";
import { pushMaterialDisplayData, updateMaterialData } from "../../app/payloadSlice";
import { useDispatch } from "react-redux";
import { generateUniqueCombinations, getKeysValue } from "@helper/helper";
import { colors } from "@constant/colors";
import { DT_TABLES, ERROR_MESSAGES, MATERIAL_VIEWS } from "@constant/enum";
import useCustomDtCall from "@hooks/useCustomDtCall";
import TaxData from "./TaxData";
import GenericViewGeneral from "./GenericViewGeneral";
import useLogger from "@hooks/useLogger";
import { END_POINTS } from "../../constant/apiEndPoints";
import { REQUEST_TYPE } from "../../constant/enum";

const GenericTabs = (props) => {
  const payloadState = useSelector((state) => state.payload);
  const materialRows = useSelector((state) => state?.request?.materialRows);
  const orgData = payloadState?.[props.materialID]?.headerData?.orgData;
  let taskData = useSelector((state) => state.userManagement.taskData);
  const [plantCombination, setPlantCombination] = useState({});
  const requestType = useSelector((state) => state.payload.payloadData?.RequestType);
  const [mrpLoaderArr, setMrpLoaderArr] = useState([]);
  const [expanded, setExpanded] = useState([]);
  const dispatch = useDispatch();
  const { getDtCall, dtData } = useCustomDtCall();
  const { customError } = useLogger();
  // Format a raw combination for display
  function formatCombinationDisplay(combination, requiredKeys) {
    const fieldLabels = {
      plant: "Plant",
      salesOrg: "SalesOrg",
      dc: "Distribution Channel",
      sloc: "Storage Location",
      mrpProfile: "MRP Profile",
      warehouse: "Warehouse",
    };

    const combinationParts = combination.split("-");
    return requiredKeys
      .map((key, index) => {
        const label = fieldLabels[key];
        const value = combinationParts[index] || "N/A"; // Handle missing parts
        return `${label} - ${value}`;
      })
      .join(", ");
  }
  const formatUniqueSalesOrg = (salesOrgArray) => {
    return [...new Set(salesOrgArray)].join("$^$");
  };
  const getUniqueCountries = (data) => {
    const uniqueCountries = new Map();

    data.forEach(({ CountryName, Country }) => {
      uniqueCountries.set(Country, CountryName);
    });

    return Array.from(uniqueCountries, ([Country, CountryName]) => ({ Country, CountryName }));
  };
  const formatUniqueCountries = (uniqueCountries) => {
    return uniqueCountries.map(({ Country }) => Country).join("$^$");
  };
  const getCountriesBasedOnSalesOrg = (salesOrg) => {
    const url = `/${destination_MaterialMgmt}${END_POINTS.TAX_DATA.GET_COUNTRY_SALESORG}`;
    const payload = { salesOrg: salesOrg };

    const hSuccess = (response) => {
      const data = response?.body;
      const uniqueCountries = getUniqueCountries(data);
      const formattedCountries = formatUniqueCountries(uniqueCountries);
      getTaxDataSetBasedOnCountry(formattedCountries);
    };

    const hError = (error) => {
      customError(ERROR_MESSAGES.NO_DATA_AVAILABLE);
    };

    doAjax(url, "post", hSuccess, hError, payload);
  };

  const getTaxDataSetBasedOnCountry = (country) => {
    const url = `/${destination_MaterialMgmt}${END_POINTS.TAX_DATA.GET_TAX_COUNTRY}`;
    const payload = { country };

    const hSuccess = (data) => {
      const taxDataSet = data?.body;
      const existingTaxData = payloadState[props?.materialID]?.payloadData?.TaxData?.TaxData?.TaxDataSet || [];
      const taxClassDescMap = {};
      const validTaxDataSet = taxDataSet.filter(entry => entry.TaxType);
      
      validTaxDataSet.forEach(({ TaxClass, TaxClassDesc }) => {
        taxClassDescMap[TaxClass] = TaxClassDesc;
      });

      const groupedTaxData = existingTaxData.map(existingEntry => {
        const relevantOptions = validTaxDataSet
          .filter(item => item.TaxType === existingEntry.TaxType && item.Country === existingEntry.Country)
          .map(item => ({
            code: item.TaxClass,
            desc: item.TaxClassDesc,
          }));
        let updatedSelectedTaxClass = existingEntry.SelectedTaxClass;
        if (updatedSelectedTaxClass && taxClassDescMap[updatedSelectedTaxClass.TaxClass]) {
          updatedSelectedTaxClass = {
            ...updatedSelectedTaxClass,
            TaxClassDesc: taxClassDescMap[updatedSelectedTaxClass.TaxClass]
          };
        }

        return {
          ...existingEntry,
          options: relevantOptions,
          SelectedTaxClass: updatedSelectedTaxClass
        };
      });
      validTaxDataSet.forEach(({ TaxType, SequenceNo, Country, TaxClass, TaxClassDesc }) => {
        const exists = groupedTaxData.some(item => 
          item.TaxType === TaxType && item.Country === Country
        );

        if (!exists) {
          const options = validTaxDataSet
            .filter(item => item.TaxType === TaxType && item.Country === Country)
            .map(item => ({
              code: item.TaxClass,
              desc: item.TaxClassDesc,
            }));

          groupedTaxData.push({
            TaxType,
            SequenceNo,
            Country,
            options,
            SelectedTaxClass: null
          });
        }
      });

      dispatch(
        updateMaterialData({
          materialID: props?.materialID || "",
          keyName: "TaxDataSet",
          data: groupedTaxData,
          viewID: "TaxData",
          itemID: "TaxData",
        })
      );
    };

    const hError = (error) => {
      customError(ERROR_MESSAGES.NO_DATA_AVAILABLE);
    };

    doAjax(url, "post", hSuccess, hError, payload);
  };

  const CurrentMaterialRows = materialRows?.find((material) => {
    return material?.id === props.materialID;
  });
  useEffect(() => {
    if (orgData) {
      const isRefMaterial = payloadState[props.materialID]?.headerData?.refMaterialData ? true : false;
      const combinations = generateUniqueCombinations(orgData, payloadState?.[props.materialID]?.payloadData, props?.materialID, dispatch);
      setPlantCombination(combinations);
      if (!isRefMaterial && !props.isDisplay && combinations.hasOwnProperty(MATERIAL_VIEWS.SALES) && props?.selectedViews?.includes(MATERIAL_VIEWS.SALES)) {
        combinations[MATERIAL_VIEWS.SALES].reduxCombinations.forEach((comb, index) => {
          if (requestType !== REQUEST_TYPE.EXTEND) {
            getSalesGrpPriceMapDT({ comb, dt: DT_TABLES.SALES_DIV_PRICE_MAPPING }, orgData[index]);
          }
        });
      }
      if (!isRefMaterial && props?.selectedViews?.includes(MATERIAL_VIEWS.SALES) || props?.selectedViews?.includes(MATERIAL_VIEWS.ACCOUNTING) || props?.selectedViews?.includes(MATERIAL_VIEWS.COSTING)) {
        orgData.forEach((org, index) => {
          if (requestType !== REQUEST_TYPE.EXTEND && !props.isDisplay) {
            getItemCatGrpMappingDT({ combinations, index, dt: DT_TABLES.REG_PLNT_INSPSTK_MAPPING }, org);
          }
        });
      }
      if(isRefMaterial) constructPayloadFromCopyMat(combinations,payloadState[props.materialID]?.headerData?.refMaterialData);
    } else {
      setPlantCombination({});
    }
  }, [orgData]);
  useEffect(() => {
    if (orgData) {
      const uniqueSalesOrgs = [...new Set(orgData?.map((item) => item.salesOrg?.code))];
      const FormattedSalesOrg = formatUniqueSalesOrg(uniqueSalesOrgs);
      getCountriesBasedOnSalesOrg(FormattedSalesOrg);
    }
  }, [orgData, props?.callGetCountryBasedonSalesOrg]);
  useEffect(() => {
    if (dtData) {
      if (dtData.customParam?.dt === DT_TABLES.SALES_DIV_PRICE_MAPPING && props?.selectedViews?.includes(MATERIAL_VIEWS.SALES)) {
        const fieldValue = Object.keys(dtData?.data?.result[0])?.length ? dtData?.data?.result?.[0]?.MDG_MAT_SALESDIV_PRCICEGRP_MAPPING[0]?.MDG_MAT_MATERIAL_PRICING_GROUP : "";
        if (requestType !== REQUEST_TYPE.EXTEND && requestType !== REQUEST_TYPE.CREATE_WITH_UPLOAD) {
          if(fieldValue) setFieldValues(dtData.customParam?.comb, "MatPrGrp", fieldValue, "Sales");
        }
      } else if (dtData.customParam?.dt === DT_TABLES.REG_PLNT_INSPSTK_MAPPING) {
        let combs = dtData.customParam?.combinations;
        //let ind = dtData.customParam?.index;
        let org = dtData.customParam?.org;
        if (combs?.hasOwnProperty(MATERIAL_VIEWS.SALES) && props?.selectedViews?.includes(MATERIAL_VIEWS.SALES)) {
          const fieldValue = Object.keys(dtData?.data?.result[0])?.length ? dtData?.data?.result?.[0]?.MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING[0]?.MDG_MAT_ITEM_CAT_GROUP : "";
          if (requestType !== REQUEST_TYPE.EXTEND && requestType !== REQUEST_TYPE.CREATE_WITH_UPLOAD) {
            if(fieldValue) setFieldValues(org?.salesOrg?.code + "-" + org?.dc?.value?.code, "ItemCat", fieldValue, "Sales");
          }
        }
        if (combs.hasOwnProperty(MATERIAL_VIEWS.PURCHASING) && props?.selectedViews?.includes(MATERIAL_VIEWS.PURCHASING)) {
          const fieldValue = Object.keys(dtData?.data?.result[0])?.length ? dtData?.data?.result?.[0]?.MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING[0]?.MDG_MAT_POST_TO_INSP_STOCK : "";
          if (requestType !== REQUEST_TYPE.EXTEND && requestType !== REQUEST_TYPE.CREATE_WITH_UPLOAD) {
            if(fieldValue) setFieldValues(org?.plant?.value?.code, "IndPostToInspStock", fieldValue, "Purchasing");
          }
        }
        if (combs.hasOwnProperty(MATERIAL_VIEWS.ACCOUNTING) && props?.selectedViews?.includes(MATERIAL_VIEWS.ACCOUNTING)) {
          const fieldValue = Object.keys(dtData?.data?.result[0])?.length ? dtData?.data?.result?.[0]?.MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING[0]?.MDG_MAT_PRICE_UNIT : "";
          if (requestType !== REQUEST_TYPE.EXTEND && requestType !== REQUEST_TYPE.CREATE_WITH_UPLOAD) {
            if(fieldValue) setFieldValues(org?.plant?.value?.code, "PriceUnit", fieldValue, "Accounting");
          }
        }
        if (combs.hasOwnProperty(MATERIAL_VIEWS.COSTING) && props?.selectedViews?.includes(MATERIAL_VIEWS.COSTING)) {
          const fieldValue = Object.keys(dtData?.data?.result[0])?.length ? dtData?.data?.result?.[0]?.MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING[0]?.MDG_MAT_COSTING_LOT_SIZE : "";
          if (requestType !== REQUEST_TYPE.EXTEND && requestType !== REQUEST_TYPE.CREATE_WITH_UPLOAD) {
            if(fieldValue) setFieldValues(org?.plant?.value?.code, "Lotsizekey", fieldValue, "Costing");
          }
        }
      }
    }
  }, [dtData]);
  const constructPayloadFromCopyMat = (combination,referenceData) =>{
    let payloadData = payloadState[props.materialID]?.payloadData;
    Object.keys(combination).forEach(comb =>{
      let reduxCombination = combination[comb]?.reduxCombinations;
      reduxCombination?.forEach(reduxComb =>{
        if(comb !== MATERIAL_VIEWS.BASIC_DATA && referenceData?.copyPayload?.payloadData[comb] && !payloadData[comb]?.[reduxComb]){
          Object.keys(referenceData?.copyPayload?.payloadData[comb])?.forEach(key =>{
            let keyValue = getKeysValue(key,referenceData?.copyPayload?.payloadData[comb][key],props?.allTabsData[comb]);
            if(keyValue) dispatch(updateMaterialData({materialID: props?.materialID,viewID: comb,itemID: reduxComb,keyName:key,data:keyValue}));
          })
          // dispatch(updateMaterialData({materialID : props?.materialID,viewID: comb,itemID: reduxComb,data:comb,data:referenceData?.copyPayload?.payloadData[comb]}))
          if(comb === MATERIAL_VIEWS.SALES){
            dispatch(updateMaterialData({materialID: props?.materialID, viewID: MATERIAL_VIEWS.TAX_DATA,itemID: MATERIAL_VIEWS.TAX_DATA,data:referenceData?.copyPayload?.payloadData?.TaxData?.TaxData}))
            Object.keys(referenceData?.copyPayload?.payloadData?.[MATERIAL_VIEWS.SALES_GENERAL]?.[MATERIAL_VIEWS.SALES_GENERAL])?.forEach(key =>{
              let keyValue = getKeysValue(key,referenceData?.copyPayload?.payloadData[MATERIAL_VIEWS.SALES_GENERAL]?.[MATERIAL_VIEWS.SALES_GENERAL]?.[key],props?.allTabsData[MATERIAL_VIEWS.SALES_GENERAL]);
              if(keyValue) dispatch(updateMaterialData({materialID: props?.materialID,viewID: MATERIAL_VIEWS.SALES_GENERAL,itemID: MATERIAL_VIEWS.SALES_GENERAL,keyName:key,data:keyValue}));
            })
            // dispatch(updateMaterialData({materialID: props?.materialID, viewID: MATERIAL_VIEWS.SALES_GENERAL,itemID: MATERIAL_VIEWS.SALES_GENERAL, data:referenceData?.copyPayload?.payloadData?.[MATERIAL_VIEWS.SALES_GENERAL]?.[MATERIAL_VIEWS.SALES_GENERAL]}))
          }
          if(comb === MATERIAL_VIEWS.PURCHASING && referenceData?.copyPayload?.payloadData?.[MATERIAL_VIEWS.PURCHASING_GENERAL]){
            Object.keys(referenceData?.copyPayload?.payloadData[MATERIAL_VIEWS.PURCHASING_GENERAL]?.[MATERIAL_VIEWS.PURCHASING_GENERAL])?.forEach(key =>{
              let keyValue = getKeysValue(key,referenceData?.copyPayload?.payloadData[MATERIAL_VIEWS.PURCHASING_GENERAL][key],props?.allTabsData[MATERIAL_VIEWS.PURCHASING_GENERAL]);
              if(keyValue) dispatch(updateMaterialData({materialID: props?.materialID,viewID: comb,itemID: MATERIAL_VIEWS.PURCHASING_GENERAL,keyName:key,data:keyValue}));
            })
            // dispatch(updateMaterialData({materialID: props?.materialID, viewID: MATERIAL_VIEWS.PURCHASING_GENERAL,itemID: MATERIAL_VIEWS.PURCHASING_GENERAL, data:referenceData?.copyPayload?.payloadData?.[MATERIAL_VIEWS.PURCHASING_GENERAL]?.[MATERIAL_VIEWS.PURCHASING_GENERAL]}))
          }
        }
      })
    })
  }
  const getSalesGrpPriceMapDT = (comb, org) => {
    let payload = {
      decisionTableId: null,
      decisionTableName: DT_TABLES.SALES_DIV_PRICE_MAPPING,
      version: "v1",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_SALES_ORG": org?.salesOrg?.code,
          "MDG_CONDITIONS.MDG_MAT_DIVISION": payloadState?.payloadData?.Division,
        },
      ],
    };
    comb.org = org;
    getDtCall(payload, comb);
  };
  const getItemCatGrpMappingDT = (comb, org) => {
    let payload = {
      decisionTableId: null,
      decisionTableName: DT_TABLES.REG_PLNT_INSPSTK_MAPPING,
      version: "v1",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_REGION": payloadState?.payloadData?.Region,
          "MDG_CONDITIONS.MDG_MAT_PLANT": org?.plant?.value?.code,
        },
      ],
    };
    comb.org = org;
    getDtCall(payload, comb);
  };
  useEffect(() => {
    let expandArr = orgData?.length
      ? orgData?.map(() => {
          return false;
        })
      : [];
    setExpanded(expandArr);
  }, [props.activeViewTab, orgData]);
  const handleAccordionChange = (comb, requiredKeys, index) => (event, isExpanded) => {
    setExpanded((prevExpanded) => ({
      ...prevExpanded,
      [index]: isExpanded,
    }));
    
    // if (isExpanded) {
    //   let loaderObj = [...(mrpLoaderArr || [])];
    //   loaderObj[index] = {};
    //   loaderObj[index].value = 1;
    //   if (isWithReference === "yes") {
    //     fetchViewData(comb, index), setMrpLoaderArr(loaderObj);
    //   }
    // }
  };
  const fetchViewData = (comb, index) => {
    let orgKeys = orgData[index];
    if (props.activeViewTab) {
      let payload = {};
      let url = "";
      let plantData = "";
      if (props.activeViewTab === "Purchasing" || props.activeViewTab === "Costing" || props.activeViewTab === "MRP") {
        payload = {
          materialNo: payloadState[props.materialID]?.headerData?.materialNumber,
          plant: orgKeys?.plant?.value?.code || "",
        };
        plantData = comb;
        url = `/${destination_MaterialMgmt}/data/displayLimitedPlantData`;
      } else if (props.activeViewTab === "Accounting") {
        payload = {
          materialNo: payloadState[props.materialID]?.headerData?.materialNumber,
          valArea: orgKeys?.plant?.value?.code || "",
        };
        plantData = comb;
        url = `/${destination_MaterialMgmt}/data/displayLimitedAccountingData`;
      } else if (props.activeViewTab === "Sales") {
        payload = {
          materialNo: payloadState[props.materialID]?.headerData?.materialNumber,
          salesOrg: orgKeys?.salesOrg?.code || "",
          distChnl: orgKeys?.dc?.value?.code || "",
        };
        plantData = comb;
        url = `/${destination_MaterialMgmt}/data/displayLimitedSalesData`;
      }

      const hSuccess = (data) => {
        let loaderObj = [...(mrpLoaderArr || [])];
        loaderObj[index] = {};
        loaderObj[index].value = 0;
        setMrpLoaderArr(loaderObj);
        if (props.activeViewTab === "Purchasing" || props.activeViewTab === "Costing" || props.activeViewTab === "MRP") {
          dispatch(
            pushMaterialDisplayData({
              materialID: props.materialID,
              viewID: props.activeViewTab,
              itemID: comb,
              data: data?.body?.SpecificPlantDataViewDto[0],
            })
          );
        } else if (props.activeViewTab === "Accounting") {
          dispatch(
            pushMaterialDisplayData({
              materialID: props.materialID,
              viewID: props.activeViewTab,
              itemID: comb,
              data: data?.body?.SpecificAccountingDataViewDto[0],
            })
          );
        } else if (props.activeViewTab === "Sales") {
          dispatch(
            pushMaterialDisplayData({
              materialID: props.materialID,
              viewID: props.activeViewTab,
              itemID: comb,
              data: data?.body?.SpecificSalesDataViewDto[0],
            })
          );
        }
      };

      const hError = () => {};

      const shouldCallApi = !payloadState?.[props.materialID]?.payloadData?.[props.activeViewTab]?.[plantData];

      if (shouldCallApi) {
        doAjax(url, "post", hSuccess, hError, payload);
      } else {
        let loaderObj = [...(mrpLoaderArr || [])];
        loaderObj[index] = {};
        loaderObj[index].value = 0;
        setMrpLoaderArr(loaderObj);
      }
    }
  };
  const setFieldValues = (comb, fieldName, fieldValue, tab) => {
    dispatch(
      updateMaterialData({
        materialID: props?.materialID || "",
        keyName: fieldName || "",
        data: fieldValue ?? null,
        viewID: tab,
        itemID: comb,
      })
    );
  };
  const memoizedComponent = useMemo(() => {
    const activeViewData = plantCombination[props.activeViewTab] || {};
    const { displayCombinations = [], reduxCombinations = [], requiredKeys = [] } = activeViewData;
    const filterFields = Object.entries(props?.basicDataTabDetails || {});
    const salesGeneralFields = props.allTabsData?.hasOwnProperty(MATERIAL_VIEWS.SALES_GENERAL) ? Object.entries(props.allTabsData[MATERIAL_VIEWS.SALES_GENERAL]) : [];
    const purchaseGeneralFields = props.allTabsData?.hasOwnProperty(MATERIAL_VIEWS.PURCHASING_GENERAL) ? Object.entries(props.allTabsData[MATERIAL_VIEWS.PURCHASING_GENERAL]) : [];
    if (props.activeViewTab === "Basic Data") {
      const allFieldsDisabled = filterFields.every(item => 
        item[1].every(field => field.visibility === "Hidden")
      );
      
      if (allFieldsDisabled) {
        return (
          <Grid
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              borderRadius: "8px",
              border: `1px solid ${colors.hover.hoverbg}`,
              mt: 0.25,
              boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              padding: "20px",
              textAlign: "center"
            }}
          >
            <Typography variant="body1">Fields are hidden for this view</Typography>
          </Grid>
        );
      }
      
      return filterFields.map((item) => (
        <Grid
          item
          md={12}
          sx={{
            backgroundColor: "white",
            maxHeight: "max-content",
            height: "max-content",
            borderRadius: "8px",
            border: `1px solid ${props?.missingValidationPlant?.includes(MATERIAL_VIEWS.BASIC_DATA) && !CurrentMaterialRows?.validated ? colors.error.dark : colors.hover.hoverbg} `,
            mt: 0.25,
            boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
            ...container_Padding,
          }}
          key={item[0]}
        >
          <Grid container>
            <Typography
              sx={{
                fontSize: "12px",
                fontWeight: "700",
                paddingBottom: "10px",
              }}
            >
              {item[0]}
            </Typography>
          </Grid>
          <Box>
            <Grid container spacing={1}>
              {[...item[1]]
                .filter((x) => x.visibility !== "Hidden")
                .sort((a, b) => a.sequenceNo - b.sequenceNo)
                .map((innerItem) => (
                  <FilterField key={innerItem.fieldName} disabled={props?.disabled} field={innerItem} dropDownData={props.dropDownData} materialID={props?.materialID} selectedMaterialNumber={props?.selectedMaterialNumber} viewName={props?.activeViewTab} plantData={"basic"} />
                ))}
            </Grid>
          </Box>
        </Grid>
      ));
    } else if (!displayCombinations.length) {
      return (
        <Typography variant="body2" sx={{ margin: "20px", color: "gray" }}>
          No Org Data selected.
        </Typography>
      );
    }
    
    const allFieldsDisabled = filterFields.every(item => 
      item[1].every(field => field.visibility === "Hidden")
    );
    
    if (allFieldsDisabled) {
      return (
        <Grid
          item
          md={12}
          sx={{
            backgroundColor: "white",
            maxHeight: "max-content",
            height: "max-content",
            borderRadius: "8px",
            border: `1px solid ${colors.hover.hoverbg}`,
            mt: 0.25,
            boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
            padding: "20px",
            textAlign: "center"
          }}
        >
          <Typography variant="body1">Fields are hidden for this view</Typography>
        </Grid>
      );
    }

    return (
      <>
        {props.activeViewTab === MATERIAL_VIEWS.SALES && (
          <>
            <TaxData materialID={props.materialID} selectedMaterialNumber={props?.selectedMaterialNumber} />
            {salesGeneralFields?.length > 0 && <GenericViewGeneral materialID={props.materialID} selectedMaterialNumber={props?.selectedMaterialNumber} GeneralFields={salesGeneralFields} disabled={props.disabled} dropDownData={props.dropDownData} viewName={MATERIAL_VIEWS?.SALES_GENERAL} />}
          </>
        )}
        {props.activeViewTab === MATERIAL_VIEWS.PURCHASING && <> {purchaseGeneralFields?.length > 0 && <GenericViewGeneral materialID={props.materialID} selectedMaterialNumber={props?.selectedMaterialNumber} GeneralFields={purchaseGeneralFields} disabled={props.disabled} dropDownData={props.dropDownData} viewName={MATERIAL_VIEWS?.PURCHASING_GENERAL} />}</>}
        {displayCombinations.map((combination, index) => {
          return (
            <Accordion sx={{ marginBottom: "20px", boxShadow: 3, borderRadius: "10px", borderColor: props?.missingValidationPlant?.includes(combination) && !CurrentMaterialRows?.validated ? colors?.error?.dark : colors?.primary.white }} key={index} onChange={handleAccordionChange(combination, requiredKeys, index)} expanded={expanded[index]}>
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                sx={{
                  backgroundColor: colors.primary.whiteSmoke,
                  borderRadius: "10px",
                  padding: "8px 16px",
                  "&:hover": { backgroundColor: colors.hover.hoverbg },
                }}
              >
                <Typography variant="h6" sx={{ fontWeight: "bold" }}>
                  {formatCombinationDisplay(combination, requiredKeys)}
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                {mrpLoaderArr[index]?.value === 1 ? (
                  <Box sx={{ display: "flex", alignItems: "center", justifyContent: "center", minHeight: "200px" }}>
                    {" "}
                    <CircularProgress />
                  </Box>
                ) : (
                  filterFields.map((item) => (
                    <Grid
                      item
                      md={12}
                      sx={{
                        backgroundColor: "white",
                        maxHeight: "max-content",
                        height: "max-content",
                        borderRadius: "8px",
                        border: "1px solid #E0E0E0",
                        mt: 0.25,
                        boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                        ...container_Padding,
                      }}
                      key={item[0]}
                    >
                      <Grid container>
                        <Typography
                          sx={{
                            fontSize: "12px",
                            fontWeight: "700",
                            paddingBottom: "10px",
                          }}
                        >
                          {item[0]}
                        </Typography>
                      </Grid>
                      <Box>
                        <Grid container spacing={1}>
                          {[...item[1]]
                            .filter((x) => x.visibility !== "Hidden")
                            .sort((a, b) => a.sequenceNo - b.sequenceNo)
                            .map((innerItem) => (
                              <FilterField
                                key={innerItem.fieldName}
                                disabled={props?.disabled}
                                field={innerItem}
                                dropDownData={props.dropDownData}
                                materialID={props?.materialID}
                                selectedMaterialNumber={props?.selectedMaterialNumber}
                                viewName={props?.activeViewTab}
                                plantData={reduxCombinations[index]} // Pass Redux-friendly combination
                              />
                            ))}
                        </Grid>
                      </Box>
                    </Grid>
                  ))
                )}
              </AccordionDetails>
            </Accordion>
          );
        })}
      </>
    );
  }, [plantCombination, props.activeViewTab, props.basicDataTabDetails, mrpLoaderArr, props.materialID, props.missingValidationPlant, expanded]);

  return <>{memoizedComponent}</>;
};

export default GenericTabs;
