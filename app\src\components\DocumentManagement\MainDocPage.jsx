import * as React from "react";
import Box from "@mui/material/Box";
import moment from "moment/moment";
import useLogger from "@hooks/useLogger";
import {
  container_table,
  iconButton_SpacingSmall,
  container_tableHeader,
  outermostContainer,
  outermostContainer_Information,
  primary_Color,
} from "../common/commonStyles.jsx";
import { useNavigate } from "react-router-dom";

import { IosShare, Refresh } from "@mui/icons-material";

import { DataGrid } from "@mui/x-data-grid";
import "./doc.css";
import {
  Backdrop,
  CircularProgress,
  IconButton,
  LinearProgress,
  Tooltip,
  Typography,
} from "@mui/material";
import axios from "axios";
import PictureAsPdfOutlinedIcon from '@mui/icons-material/PictureAsPdfOutlined';
import DescriptionOutlinedIcon from '@mui/icons-material/DescriptionOutlined';
import ImageOutlinedIcon from '@mui/icons-material/ImageOutlined';

import { <PERSON>rid, <PERSON>ack, Button } from "@mui/material";
import {
  destination_DocumentManagement,
  destination_ServiceRequest,
} from "../../destinationVariables.jsx";

import SearchBar from "../common/SearchBar";
import DateRange from "../common/DateRangePicker";

import {
  captureScreenShot,
  checkIwaAccess,
  controller_UrlRedirecting,
} from "../../functions";
import { useState, useEffect } from "react";

import {
  dateFormatter,
  DropdownNames,
  MatDownload,
  MatIcon_DocumentType,
  MatView,
  MultiDownloadButton,
} from "./UtilDoc.jsx";
import { useDispatch, useSelector } from "react-redux";
import ReusableDialog from "../common/ReusableDialog.jsx";
import DocumentFilter from "./DocumentFilter.jsx";
import styled from "@emotion/styled";
import ReusableSnackBar from "../common/ReusableSnackBar.jsx";
import { doAjax } from "../common/fetchService.jsx";
import { initialDataUpdate } from "../../app/initialDataSlice.jsx";
import { commonFilterClear, commonFilterUpdate } from "../../app/commonFilterSlice.jsx";
import NoDataDialog from "../common/NoDataDialog.jsx";
import ReusableDataTable from "@components/Common/ReusableTable.jsx";
import ReusableBackDrop from "@components/Common/ReusableBackDrop.jsx";
import { colors } from "@constant/colors.js";
import { ROLES, SEARCH_BAR_LABELS } from "@constant/enum.js";
import { END_POINTS } from "@constant/apiEndPoints.js";
import xlsx from "json-as-xlsx";
import InfoIcon from '@mui/icons-material/Info';
import { commonSearchBarClear } from "@app/commonSearchBarSlice.jsx";

let exportAsPicture = () => {
  setTimeout(() => {
    captureScreenShot("DocumentsManagement");
  }, 100);
};

function MainDocPage() {
  const { customError } = useLogger();
  let iwaAccessData = useSelector(
    (state) =>
      state.userManagement.entitiesAndActivities?.["Document Management"]
  );
  const FilterSearchForm = useSelector(
    (state) => state.commonFilter["DocumentManagement"]
  );
  const formcontroller_SearchBar = useSelector(
    (state) => state.commonSearchBar["DocumentManagement"]
  );
  const appSettings = useSelector((state) => state.appSettings["Format"]);
  const [page, setPage] = useState(0);

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const iconMap = {
    pdf: <PictureAsPdfOutlinedIcon sx={{ fontSize: 20, color: `${colors.icon.red}` }} />,
    image: <ImageOutlinedIcon sx={{ fontSize: 20, color: `${colors.icon.image}` }} />,
    txt: <DescriptionOutlinedIcon sx={{ fontSize: 20, color: `${colors.icon.green}` }} />,
  };
  const columns = [
    {
      field: "requestId",
      headerName: "Request ID",
      flex: 1,
      editable: false,
    },
    {
      field: "requestType",
      headerName: "Request Type",
      editable: false,
      flex: 1,
    },
    {
      field: "material",
      headerName: "Material",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        const materials = params.value ? params.value.split(",").map(m => m.trim()) : [];
        const displayCount = materials.length - 1;

        if (materials.length === 0) return "-";

        return (
          <Box sx={{ 
            display: "flex", 
            alignItems: "center",
            width: "100%",
            minWidth: 0 
          }}>
            <Tooltip 
              title={materials[0]}
              placement="top"
              arrow
            >
              <Typography 
                variant="body2" 
                sx={{ 
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  flex: 1,
                  minWidth: 0,
                }}
              >
                {materials[0]}
              </Typography>
            </Tooltip>
            {displayCount > 0 && (
              <Box sx={{ 
                display: "flex",
                alignItems: "center",
                ml: 1,
                flexShrink: 0 
              }}>
                <Tooltip
                  arrow
                  placement="right"
                  title={
                    <Box sx={{ p: 1, maxHeight: 200, overflowY: "auto" }}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                        Additional Materials ({displayCount})
                      </Typography>
                      {materials.slice(1).map((material, idx) => (
                        <Typography key={idx} variant="body2" sx={{ mb: 0.5 }}>
                          {material}
                        </Typography>
                      ))}
                    </Box>
                  }
                >
                  <Box sx={{ 
                    display: "flex", 
                    alignItems: "center",
                    cursor: "pointer"
                  }}>
                    <InfoIcon 
                      sx={{ 
                        fontSize: "1rem",
                        color: "primary.main",
                        "&:hover": { color: "primary.dark" }
                      }} 
                    />
                    <Typography 
                      variant="caption" 
                      sx={{ 
                        ml: 0.5,
                        color: "primary.main",
                        fontSize: "11px"
                      }}
                    >
                      +{displayCount}
                    </Typography>
                  </Box>
                </Tooltip>
              </Box>
            )}
          </Box>
        );
      },
    },
    {
      field: "attachmentType",
      headerName: "Attachment Type",
      editable: false,
      flex: 1,
    },
    {
      field: "documentId",
      headerName: "Document ID",
      editable: false,
      flex: 1,
    },
    {
      field: "documentType",
      headerName: "Document Type",
      editable: false,
      flex: 1,
      renderCell: (cellValues) => {
        const docType = cellValues.row.documentType?.toLowerCase();
        return (
          <>
            {iconMap[docType] || (
              <DescriptionOutlinedIcon sx={{ fontSize: 20, color: `${colors.icon.green}` }} />
            )}
            <span style={{ marginLeft: 8 }}>{cellValues.row.documentType}</span>
          </>
        );
      },
    },
    {
      field: "fileName",
      headerName: "File Name",
      editable: false,
      flex: 1,
    },
    {
      field: "uploadedBy",
      headerName: "Uploaded By",
      editable: false,
      flex: 1,
    },
    {
      field: "uploadedOn",
      headerName: "Uploaded On",
      editable: false,
      flex: 1,
      renderCell: (cellValues) => {
        const dateTime = cellValues.row.uploadedOn;
        if (!dateTime || dateTime === "-") return "-";

        try {
          const [date, time] = dateTime.split(" ");
          const formattedDate = moment(date).format("DD-MMM-YYYY");
          const formattedTime = moment(time, "HH:mm:ss").format("HH:mm");
          return (
            <Typography variant="body2">
              {formattedDate} • {formattedTime}
            </Typography>
          );
        } catch (error) {
          return "-";
        }
      },
    },
    {
      field: "actions",
      headerName: "Actions",
      editable: false,
      flex: 1,
      sortable: false,
      filterable: false,
      align: "center",
      headerAlign: "center",
      flex: 1,
      renderCell: (cellValues) => {
        return (
          <>
            <MatDownload
              index={cellValues.row.documentId}
              name={cellValues.row.fileName}
            />
            <MatView
              index={cellValues.row.documentId}
              name={cellValues.row.fileName}
            />
          </>
        );
      },
    },
  ];
  const userData = useSelector((state) => state.userManagement.userData);
  const userRoles = useSelector((state) => state.userManagement.roles);
  const names = DropdownNames;
  const presentDate = new Date();
  const backDate = new Date();
  backDate.setDate(backDate.getDate() - 3);
  const [date, setDate] = React.useState([backDate, presentDate]);
  const [rmDataRows, setRmDataRows] = useState([]);
  const [roCount, setroCount] = useState(0);
  const [tableData, setTableData] = useState([...rmDataRows]);
  const [documentCount, setDocumentCount] = useState(0);
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [snackbar, setSnackbar] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const handleSnackbarClose = () => {
    setopenSnackbar(false);
  };
  const handleSnackbarOpen = () => {
    setopenSnackbar(true);
  };
  const [downloadError, setdownloadError] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };
  const [pageSize, setPageSize] = React.useState(10);
  const [PrimaryData, setPrimaryData] = React.useState([]);

  const DocumentsRow = useSelector((state) => state.initialData?.["Document"]);
  const [showDownloadButton, setshowDownloadButton] = React.useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [DownloadArray, setDownloadArray] = React.useState([]);
  const [DownloadNumber, setDownloadNumber] = React.useState(0);

  const [value, setValue] = React.useState(null);
  const [getDocName, setgetDocName] = React.useState("");

  const StyledGridOverlay = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    height: "100%",
  }));
  function CustomNoRowsOverlay() {
    return (
      <StyledGridOverlay>
        <Box sx={{ mt: 1 }}>No Data Available</Box>
      </StyledGridOverlay>
    );
  }

  const handleSearchBar = (value) => {
    if (!value) {
      setTableData([...rmDataRows]);
      setroCount(documentCount);
      return;
    }
    const selected = rmDataRows.filter((row) => {
      let rowMatched = false;
      let keys = Object.keys(row);

      for (let k = 0; k < keys.length; k++) {
        rowMatched = !row[keys[k]]
          ? false
          : row?.[keys?.[k]] &&
            row?.[keys?.[k]]
              .toString()
              .toLowerCase()
              ?.indexOf(value?.toLowerCase()) != -1;

        if (rowMatched) break;
      }
      return rowMatched;
    });

    setTableData([...selected]);
    setroCount(selected?.length);
  };

  const handlePageSizeChange = (event) => {
    const newPageSize = event.target.value;
    setPageSize(newPageSize);
    setPage(0);
  };
  const handlePageChange = (event, newPage) => {
    setPage(isNaN(newPage) ? 0 : newPage);
  };

  const getFilter = () => {
    setIsLoading(true);
    let data = {
      documentId: "",
      fileName: "",
      fileType: "",
      artifactType: "",
      attachmentType: FilterSearchForm?.attType ?? "",
      documentUrl: "",
      createdBy: userRoles.includes(`${ROLES.SUPER_USER}`) ? FilterSearchForm?.createdBy : userData?.emailId,
      artifactId: "",
      material: FilterSearchForm?.number ?? "",
      docCreationDate: "",
      docCreatedBy: "",
      fromDate: moment(
        FilterSearchForm?.uploadedDate[0]
          ? FilterSearchForm?.uploadedDate[0]
          : ""
      ),
      toDate: moment(
        FilterSearchForm?.uploadedDate[1]
          ? FilterSearchForm?.uploadedDate[1]
          : ""
      ),
      requestId: "",
      documentType: FilterSearchForm?.docType ?? "",
      loginUser: "",
      role: "",
      requestType: FilterSearchForm?.requestType ?? "",
      top: pageSize,
      skip: 0,
    };
    let hSuccess = (data) => {
      var rows = [];
      for (
        let index = 0;
        index < data?.documentDetailDtoList?.length;
        index++
      ) {
        var tempObj = data?.documentDetailDtoList[index];
        var tempRow = {
          id: tempObj["documentId"],
          documentId: tempObj["documentId"],
          requestId:
            tempObj["requestId"] != null ? `${tempObj["requestId"]}` : "-",
          requestType:
            tempObj["requestType"] != null ? `${tempObj["requestType"]}` : "-",
          attachmentType:
            tempObj["attachmentType"] != null ? `${tempObj["attachmentType"]}` : "-",
          documentType:
            tempObj["documentType"] != null
              ? `${tempObj["documentType"]}`
              : "-",
          material: tempObj["material"].length > 0 ? `${tempObj["material"]}` : "-",
          uploadedBy:
            tempObj["createdBy"] != null ? `${tempObj["createdBy"]}` : "-",
          uploadedOn:
            tempObj["docCreationDate"] != null
              ? `${tempObj["docCreationDate"]}`
              : "-",
          fileName: tempObj["fileName"] != null ? `${tempObj["fileName"]}` : "",
        };
        rows.push(tempRow);
      }
      setDocumentCount(data?.responseMessage?.count);
      setRmDataRows(rows);
      setroCount(data?.responseMessage?.count);
      setIsLoading(false);
      dispatch(commonSearchBarClear({ module: "DocumentMgmt" }));
    };

    let hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_DocumentManagement}/${END_POINTS.DMS_API.FETCH_DOCUMENTS}`,
      "post",
      hSuccess,
      hError,
      data
    );
  };
  const getFilterBasedOnPagination = () => {
    setIsLoading(true);
    let data = {
      documentId: "",
      fileName: "",
      fileType: "",
      artifactType: "",
      attachmentType: FilterSearchForm?.attType ?? "",
      documentUrl: "",
      createdBy: userRoles.includes(`${ROLES.SUPER_USER}`) ? FilterSearchForm?.createdBy : userData?.emailId,
      artifactId: "",
      material: FilterSearchForm?.number ?? "",
      docCreationDate: "",
      docCreatedBy: "",
      fromDate: moment(
        FilterSearchForm?.uploadedDate[0]
          ? FilterSearchForm?.uploadedDate[0]
          : ""
      ),
      toDate: moment(
        FilterSearchForm?.uploadedDate[1]
          ? FilterSearchForm?.uploadedDate[1]
          : ""
      ),
      requestId: "",
      documentType: FilterSearchForm?.docType ?? "",
      loginUser: "",
      role: "",
      requestType: FilterSearchForm?.requestType ?? "",
      top: pageSize,
      skip: pageSize * (page) ?? 0,
    };
    let hSuccess = (data) => {
      var rows = [];
      for (
        let index = 0;
        index < data?.documentDetailDtoList?.length;
        index++
      ) {
        var tempObj = data?.documentDetailDtoList[index];
        var tempRow = {
          id: tempObj["documentId"],
          documentId: tempObj["documentId"],
          requestId:
            tempObj["requestId"] != null ? `${tempObj["requestId"]}` : "-",
          requestType:
            tempObj["requestType"] != null ? `${tempObj["requestType"]}` : "-",
          documentType:
            tempObj["documentType"] != null
              ? `${tempObj["documentType"]}`
              : "-",
          uploadedBy:
            tempObj["createdBy"] != null ? `${tempObj["createdBy"]}` : "-",
          attachmentType:
            tempObj["attachmentType"] != null ? `${tempObj["attachmentType"]}` : "-",
          uploadedOn:
            tempObj["docCreationDate"] != null
              ? `${tempObj["docCreationDate"]}`
              : "-",
          material: tempObj["material"].length > 0 ? `${tempObj["material"]}` : "-",
          fileName: tempObj["fileName"] != null ? `${tempObj["fileName"]}` : "",
        };
        rows.push(tempRow);
      }
      setDocumentCount(data?.responseMessage?.count);
      setRmDataRows((prevRows) => [...prevRows, ...rows]);
      setroCount(data?.responseMessage?.count);
      setIsLoading(false);
    };

    let hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_DocumentManagement}/${END_POINTS.DMS_API.FETCH_DOCUMENTS}`,
      "post",
      hSuccess,
      hError,
      data
    );
  };

  const handleSearch = () => {
    getFilter();
  };
  const clearSearchBar = () => {
    setgetDocName("");
  };
  useEffect(() => setTableData([...rmDataRows]), [rmDataRows]);

  useEffect(() => {
     
      if(page!==0){
        const requiredDataCount = pageSize * (page + 1);
        if (requiredDataCount > rmDataRows.length && rmDataRows.length % pageSize === 0) {
          getFilterBasedOnPagination();
        }
      }   
    }, [page]);

  useEffect(() => {
    getFilter();
  }, [pageSize]);

  useEffect(() => {
      return () => {
        dispatch(
          initialDataUpdate({
            module: "DocumentManagement",
            initialData: [],
          })
        );
        dispatch(commonFilterClear({ module: "DocumentManagement", days: 7 }));
      };
    }, []);

  /*********PRESET ***************************/


  const PresetObj = [
    { name: "transactionType", value: FilterSearchForm.transactionType },
    { name: "transactionId", value: FilterSearchForm.transactionId },
    {
      name: "fromDate",
      value: dateFormatter(FilterSearchForm.uploadedDate[0].toString()),
    },
    {
      name: "toDate",
      value: dateFormatter(FilterSearchForm.uploadedDate[1].toString()),
    },
    { name: "fileType", value: FilterSearchForm.docType },
    { name: "createdBy", value: FilterSearchForm.uploadedBy },
  ];
  const PresetMethod = (preset) => {
    dispatch(
      commonFilterUpdate({
        module: "DocumentManagement",
        filterData: {
          transactionId: preset.transactionId || "",
          transactionType: preset.transactionType || [],
          docType: preset.fileType || "",
          uploadedBy: preset.createdBy || "",
          uploadedDate: [
            moment(preset.startDate.split("T")[0], "YYYY-MM-DD").toDate(),
            moment(preset.endDate.split("T")[0], "YYYY-MM-DD").toDate(),
          ],
        },
      })
    );
  };

  const [Status_ServiceReqForm, setStatus_ServiceReqForm] = useState(false);


  let excelColumns = [];
  columns.forEach((item) => {
    if (item.headerName.toLowerCase() !== "action" && !item.hide) {
      excelColumns.push({ label: item.headerName, value: item.field });
    }
  });
  const functions_ExportAsExcel = {
    data: [
      {
        sheet: "Documents Management",
        columns: columns.map(({ field, headerName }) => ({
          label: headerName,
          value: field,
        })),
        content: rmDataRows,
      },
    ],
    settings: {
      fileName: `Documents Mgmt Datasheet-${moment(presentDate).format(
        "DD-MMM-YYYY"
      )}`, // Name of the resulting spreadsheet
      extraLength: 3, // A bigger number means that columns will be wider
      writeMode: "writeFile", // The available parameters are 'WriteFile' and 'write'. This setting is optional.
      writeOptions: {}, // Style options from https://docs.sheetjs.com/docs/api/write-options
      RTL: false, // Display the columns from right-to-left (the default value is false)
    },

    convertJsonToExcel: () => {
      xlsx(functions_ExportAsExcel.data, functions_ExportAsExcel.settings);
    },
    button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() => functions_ExportAsExcel.convertJsonToExcel()}
        >
          Download
        </Button>
      );
    },
  };

  return (
    <>
      <div className="printScreen" id={"container_outermost"}>
        <Box
          className="DocumentManagement "
          sx={{
            ...outermostContainer,
            margin: "0rem 0rem",
            backgroundColor: "#FAFCFF",
          }}
        >
          <NoDataDialog DataRows={DocumentsRow} />
          {downloadError && (
            <ReusableDialog
              dialogState={openMessageDialog}
              openReusableDialog={handleMessageDialogClickOpen}
              closeReusableDialog={handleMessageDialogClose}
              dialogTitle={messageDialogTitle}
              dialogMessage={messageDialogMessage}
              handleDialogConfirm={handleMessageDialogClose}
              dialogOkText={"OK"}
              dialogSeverity={messageDialogSeverity}
              handleDialogReject={handleMessageDialogClose}
            />
          )}
          {snackbar && (
            <ReusableSnackBar
              openSnackBar={openSnackbar}
              alertMsg={messageDialogMessage}
              handleSnackBarClose={handleSnackbarClose}
            />
          )}
          <Stack spacing={1}>
            {/* Information */}

              <Grid container mt={0} sx={outermostContainer_Information}>
                <Grid item md={5} xs={12}>
                  <Typography variant="h3">
                    <strong>Manage Documents</strong>
                  </Typography>
                  <Typography variant="body2" color={colors.secondary.grey}>
                    This view displays the list of Documents
                  </Typography>
                </Grid>
                <Grid
                  item
                  md={7}
                  xs={12}
                  sx={{
                    display: "flex",
                  }}
                >
                  <Grid
                    container
                    direction="row"
                    justifyContent="flex-end"
                    alignItems="center"
                    spacing={0}
                    mt={0}
                  >

                    
                    {checkIwaAccess(
                      iwaAccessData,
                      "Document Management",
                      "Create Service Request - DM"
                    ) && serviceRequestForm_Component.button()}
                  </Grid>
                </Grid>
              </Grid>

            <DocumentFilter
              names={names}
              PresetObj={PresetObj}
              PresetMethod={PresetMethod}
              handleSearch={handleSearch}
              getFilter={getFilter}
            />

            <ReusableDataTable
              title={"List of Documents (" + roCount + ")"}
              width="100%"
              isLoading={isLoading}
              paginationLoading={isLoading}
              columns={columns}
              rows={tableData ?? []}
              pageSize={pageSize}
              onPageSizeChange={handlePageSizeChange}
              rowCount={roCount ?? rmDataRows?.length ?? 0}
              onPageChange={handlePageChange}
              getRowIdValue={"id"}
              hideFooter={true}
              showSearch={true}
              showRefresh={true}
              showExport={true}
              onSearch={handleSearchBar}
              onRefresh={getFilter}
              disableSelectionOnClick={true}
              status_onRowSingleClick={true}
              showCustomNavigation={true}
              status_onRowDoubleClick={true}
              page={page}
              module={"DocumentMgmt"}
            />
          </Stack>
          <ReusableBackDrop
            blurLoading={blurLoading}
            loaderMessage={loaderMessage}
          />

          {showDownloadButton && (
            <MultiDownloadButton
              value={value}
              setValue={setValue}
              DownloadNumber={DownloadNumber}
              DownloadArray={DownloadArray}
            />
          )}
        </Box>
      </div>
    </>
  );
}

export default MainDocPage;
